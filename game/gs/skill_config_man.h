#pragma once

#include "gprotoc/player_stune_config_t.pb.h"
namespace PB { namespace player_auto_combat_set_t_NS { class player_auto_combat_set_t; } using namespace player_auto_combat_set_t_NS; }

#include <map>
#include <vector>
#include <unordered_map>
#include <unordered_set>
#include "luaconfread.h"

struct SkillConfigSet
{
	PB::player_stune_config_t_config_set value; 
	void BuildFromLuaAnyValue(const LuaAnyValue& v);
};

class SkillConfig
{
	typedef std::unordered_map<int, const PB::player_stune_config_t_config_set>  RUNE_MAP;
	RUNE_MAP _rune_template_map;		//模板数组
	std::set<int> _rune_skills;
	PB::player_stune_config_t_config_set _default;

	//技能战斗力对应
	typedef std::vector<int> LEVEL_CAPACITY;
	typedef std::unordered_map<int, LEVEL_CAPACITY> SKILL_CAPACITY;
	SKILL_CAPACITY _skill_capacity;
	std::unordered_map<int, std::vector<int>> _config;
	std::unordered_map<int, std::map<int,SkillConfigSet>> _data;
	unsigned char _version;

	//技能方案的解锁方式
	std::map<int, std::vector<int>> _unlock_map;//first=set_index
	int _max_free_index;
	std::map<int, int> _level_unlock;

	//buff替换普通攻击技能包
	std::map<int, std::map<int, int>> _common_attack_change;//<prof, <buff, skillset>>
	int common_attack_interval_tm;
	int common_attack_interval_tm_prof_8;

	//释放技能产生的位移, 离线后台和玩家镜像战斗需要用到
	std::map<int, std::vector<float>> _skill_move;

	//每个职业可以由玩家升级的技能
	std::map<int, std::set<int>> _upgrade_skill;//<prof, <skill>>

	//每个职业可以由玩家手动释放的伤害技能的id
	std::map<int, std::set<int>> _manual_damage_skill;//<prof, <skill>>

	//属性变化时自动释放瞬发技能
	std::map<int, int> _prop_skill;//<prop idx, skill>

	//普通攻击的前置技能
	//std::map<int, std::map<int, int>> _com_attack_pre_skill;//<prof, <cur_skill, pre_skill>>
	std::map<int, std::set<int>> _pre_skill_prof_5;//职业5有多个前置
	std::map<int, std::set<int>> _pre_skill_prof_8;//职业5有多个前置
	std::unordered_set<int> _pre_skill_list;//所有前置技能

	//每个职业释放技能高度差限制，米
	struct height_range_t
	{
		height_range_t(float i, float a):min(i),max(a){}
		float min = 0;
		float max = 0;
	};
	std::vector<height_range_t> _skill_height_range;//下标就是职业id,0表示怪物

	//不计入玩家统计面板的技能
	std::unordered_set<int> _no_statistic_damage;//伤害输出
	std::unordered_set<int> _no_statistic_cure;//治疗输出
	std::unordered_set<int> _no_statistic_be_hurt;//承担伤害

	//离线后台和镜像ai不能释放的技能
	std::unordered_set<int> _ai_cannot_cast_skill;

	//后段技能
	struct BuffNextSkillDef
	{
		int buff_id = 0;
		int next_skill = 0;
		void BuildFromLuaAnyValue(const LuaAnyValue& v);
	};
	struct ProfBuffNextSkillDef
	{
		std::map<int, std::vector<BuffNextSkillDef>> skillMap;//first=skillid
		void BuildFromLuaAnyValue(const LuaAnyValue& v);
	};
	std::map<int, ProfBuffNextSkillDef> _buffNextSkillMap;//first=prof

	//辅助战斗方案
	std::map<int, std::vector<int>> _autoCombatUnlockMap;//解锁 first=set_index
	std::map<int, int> _autoCombatLevelUnlock;
	int _autoCombatFreeNum = 0;//免费的方案数量
	int _autoCombatSlotNum = 0;
	struct AutoCombatSetDef
	{
		bool locked = false;
		std::vector<int> slot;
		int prof15_prior_skill_id = 0;
		void BuildFromLuaAnyValue(const LuaAnyValue& v);
	};
	struct AutoCombatProfDef
	{
		std::map<int, AutoCombatSetDef> setMap;//first=第几套方案，[1,n]
		void BuildFromLuaAnyValue(const LuaAnyValue& v);
	};
	std::map<int, AutoCombatProfDef> _autoCombatDefaultMap;//默认配置 first=prof

	std::unordered_set<int> _beyond_max_converage;//能突破群攻上限的技能

	std::map<int, std::vector<std::vector<int>>> _gfx_modify;

	//职业6辅助战斗方案里释放切换姿态技能的时机与mp4的关系
	std::map<int, std::vector<int>> _prof6_auto_combat_swap_stance_default;//<第几套方案,[mp4大于X%切换到黑猫, mp4小于X%切换到乌鸦]>

	std::unordered_set<int> _skill_do_not_add_capacity_to_player;//这些技能在玩家身上时，不会提供战斗力
	std::map<int, std::vector<float>> _prof10_replisome_player_position_bias;//职业10 分身与本体位置偏移关系
	int unlock_mp_clear = 0;
	int prof5_mech_base_hp_ratio = 60; //职业5机甲基础血量百分比
	int prof5_mech_point_hp_ratio = 10;//职业5机甲点数修正血量百分比
	int prof5_mech_reborn_cd = 30; //职业5机甲复活cd
    std::map<int, std::vector<int>> limit_cd_set;
	std::set<int> instant_skill_in_combat;//需要进入战斗状态手动释放的瞬发技能
	std::set<int> cd_add_cdid_list;//能被增加冷却时间技能效果影响的cdids
	std::set<int> need_record_buff; //需要被记录的buff (元素球)
	std::map<int, std::vector<int>> skill_buff_list_con_map; //技能释放时所需要的buff队列 (元素球)
	std::map<int, std::set<int>> buff_break_skill_map; //buff结束打断技能
	std::set<int> long_time_cd_refresh; //时间很长的cd在进场景/pk结束时刷新下
	std::vector<int> forbid_buff_pool; //所有能被禁止的buff池

	struct SkillExCdTalentAddCfg
	{
		int talent_id  		= 0; //血统id
		int count 		= 0; //增加cd充能次数
		void BuildFromLuaAnyValue(const LuaAnyValue& v);
	};
	struct SkillExCdCfg
	{
		int cd_type = 0; //0 非法; 1 充能型
		int charge_max_count = 0; //充能型用, 最大充能数量
		std::vector<SkillExCdTalentAddCfg> talent_add = {};
		void BuildFromLuaAnyValue(const LuaAnyValue& v);
	};
	std::map<int, SkillExCdCfg> _skill_ex_cd_cfg;

	struct prof12_dog
	{
		int attack_interval_millisec; //攻击间隔
		int search_target_range; 
		float attack_range; 
		int attack_skill_id; 
		int disppear_skill_id;
		float follow_master_offset;   //跟随主人的偏移（米）
		int follow_master_angle;      //根据主人方向偏移的角度 
		float born_offset;			//出生相对于主人的偏移
		float born_angle; 			//出生相对主人的角度
		float speed;               //速度 
		int survival_time;         //存活时间 ms

		void BuildFromLuaAnyValue(const LuaAnyValue& v)
		{
			attack_interval_millisec = v["attack_interval_millisec"];
			search_target_range = v["search_target_range"];
			attack_range      = (float)v["attack_range"];
			attack_skill_id      = v["attack_skill_id"];
			disppear_skill_id   = v["disppear_skill_id"];
			follow_master_offset = (float)v["follow_master_offset"];
			follow_master_angle    = v["follow_master_angle"];
			born_offset			   = v["born_offset"];
			born_angle             = v["born_angle"];
			speed                  = v["speed"];
			survival_time          = v["survival_time"];
		}
	};

	prof12_dog player_dog_config;
	struct SkillChangeRule
	{
		int change_count = 0;
		int change_to_skill = 0;
		void BuildFromLuaAnyValue(const LuaAnyValue& v)
		{
			change_count = v["change_count"];
			change_to_skill = v["change_to_skill"];
		}
	};
	std::map<int, SkillChangeRule> _skill_change_rules;
	std::set<int> _change_to_skills;
	std::set<int> _cast_skill_trigger_ids = {};

	// 职业17配置
	struct prof17_config_t
	{
		std::vector<int> need_clear_cooldown_ids;  // 需要重置CD的主动技能cooldown_id列表
		std::vector<int> need_clear_buff_ids;      // 需要清理的被动效果buff_id列表
		void BuildFromLuaAnyValue(const LuaAnyValue& v)
		{
			v["need_clear_cooldown_ids"].Fetch(need_clear_cooldown_ids);
			v["need_clear_buff_ids"].Fetch(need_clear_buff_ids);
		}
	};
	prof17_config_t prof17_config;

	// 抑制战斗数据配置
	struct SuppressCombatStatConfig
	{
		// 场景tag配置：tag -> 启用的系统列表
		std::map<int, std::vector<int>> scene_tag_config;

		// 副本配置：inst_tid -> {inst_mode -> 启用的系统列表}
		std::map<int, std::map<int, std::vector<int>>> instance_config;

		void BuildFromLuaAnyValue(const LuaAnyValue& v)
		{
			v["scene_tag_config"].Fetch(scene_tag_config);
			v["instance_config"].Fetch(instance_config);
		}
	};
	SuppressCombatStatConfig suppress_combat_stat_config;
private:
	const AutoCombatSetDef* const __GetAutoCombatSet(int prof, int idx) const;
	void __GetProf6SwapStanceMp4(int idx, int& cat_mp4, int& bird_mp4) const;
	//void __InitComAttackPreSkill(std::set<int>& no_pre_skill_set);
public:
	static SkillConfig& GetInstance()
	{
		static SkillConfig _instance;
		return _instance;
	}
	bool Load();
	bool LoadSkillExCDConfig();
	bool CanMoveSkill(int skill_id) const;
	bool LoadCapacityConf();
	int GetSkillCapacity(int skill_id, int skill_lv, bool is_player = false) const;
	const PB::player_stune_config_t_config_set& GetRuneSet(int index,unsigned char prof) const;
	void FixLockedSet(unsigned char prof,PB::player_stune_config_t& config) const;
	unsigned int GetSlotMask(unsigned char prof,int index) const;
	unsigned char GetVersion() const {return _version;}

	int GetMaxFreeIndex();
	int GetLevelUnlockIndex(int level);
	int GetUnlockNeedMoney(int index);
	const std::map<int, int>* const GetCommonAttackChangeMap(unsigned char prof);
	float GetSkillMove(int skill_id, int perform_idx) const;
	bool CanUpgradeByClient(unsigned char prof, int skill) const;
	const std::set<int>* const GetClientCanUpgrade(unsigned char prof) const;
	const std::map<int, int>& GetPropChangeSkill() const;
	bool IsPreSkill(int skill_id);
	bool CheckCommonAttackPreSkill(unsigned char prof, int skill_id, int pre_skill_id, int pre_skill_tm) const;
	int GetProf8PreSkill(int skill_id) const;
	bool IsHeightDiffInRange(float diff, unsigned char prof) const;
	bool NeedStatisticDamage(int skill_id) const;
	bool NeedStatisticCure(int skill_id) const;
	bool NeedStatisticBeHurt(int skill_id) const;
	int GetNextSkill(unsigned char prof, int skill_id, int n) const;//返回技能的第n个后段技能
	bool CanAICast(int skill_id) const;
	bool IsManualDamageSkill(unsigned char prof, int skill) const;
	bool CanBeyondMaxCoverage(int skill_id) const;

	//辅助战斗方案
	void SetFreeAutoCombatSet(int prof, PB::player_auto_combat_set_t& d) const;
	int GetExtrenAutoCombatSetNeedMoney(int index) const;
	void AddAutoCombatSetDefault(int prof, PB::player_auto_combat_set_t& d) const;
	bool IsAutoCombatSetLocked(int prof, int index) const;
	int GetAutoCombatSetNumByLevel(int level) const;
	int GetAutoCombatSlotNum() const { return _autoCombatSlotNum; }
	void FixLockedAutoCombatSet(int prof, PB::player_auto_combat_set_t& d) const;

	int GetGfxModify(uint64_t attacker_gfx_modify_mask, int gfx) const;
	int GetUnlockMpClear() const;
	void GetProf10ReplisomeBias(int idx, float& horizontal, float& vertical) const;
	float GetMechBaseHpRatio()const;
	float GetMechPointHpRatio()const;
	int GetMechRebornCD()const 
	{
	  return prof5_mech_reborn_cd;
	}
	bool GetExistLimitCDID(int CdID) const;
    int GetMinCDTimeWithID(int CdID) const;
	bool IsInstantSkillInCombat(int skill)
	{
		return instant_skill_in_combat.find(skill) != instant_skill_in_combat.end();
	}

	const prof12_dog& GetPlayerDogConfig() const
	{
		return player_dog_config;
	}
	bool IsInCdAddList(int cdid) const
	{
		cdid -= 1024;
		return cd_add_cdid_list.find(cdid) != cd_add_cdid_list.end();
	}
	SkillExCdCfg* GetExCdCfg(int cdid)
	{
		cdid -= 1024;
		auto iter = _skill_ex_cd_cfg.find(cdid);
		if (iter == _skill_ex_cd_cfg.end())
		{
			return nullptr;
		}
		return &iter->second;
	}
	bool IsExCd(int cdid)
	{
		return GetExCdCfg(cdid) != nullptr;
	}
	const SkillChangeRule* GetSkillChangeRule(int skill_id) const 
	{
		auto iter = _skill_change_rules.find(skill_id);
		if (iter == _skill_change_rules.end())
		{
			return nullptr;
		}
		return &(iter->second);
	}
	const bool IsChangeToSkill(int skill_id) const
	{
		return _change_to_skills.find(skill_id) != _change_to_skills.end();
	}
	bool IsBuffNeedRecord(int buff_id) const
	{
		return need_record_buff.find(buff_id) != need_record_buff.end();
	}
	bool GetSkillBuffListCod(int skill_id, std::vector<int>& buffs) const
	{
		auto iter = skill_buff_list_con_map.find(skill_id);
		if (iter == skill_buff_list_con_map.end())
		{
			return false;
		}
		buffs = iter->second;
		return true;
	}
	const bool IsCastSkillTrigger(int skill_id) const
	{
		return _cast_skill_trigger_ids.find(skill_id) != _cast_skill_trigger_ids.end();
	}
	bool BreakSkillWhenBuffDetach(int buff_id, int skill_id) const;
	const std::set<int>& GetLongTimeCDs() const
	{
		return long_time_cd_refresh;
	}
	const std::vector<int>& GetForbidBuffPool() const
	{
		return forbid_buff_pool;
	}

	const prof17_config_t& GetProf17Config() const { return prof17_config; }
	const SuppressCombatStatConfig& GetSuppressCombatStatConfig() const { return suppress_combat_stat_config; }
};


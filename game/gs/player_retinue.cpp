#include "player_retinue.h"
#include "player.h"
#include "gprotoc/retinue_chat.pb.h"
#include "gprotoc/role_complicated_data_retinue.pb.h"
#include "gprotoc/retinue_chat_info.pb.h"
#include "gprotoc/db_retinue_friend_info.pb.h"
#include "gprotoc/retinue_solution.pb.h"
#include "gprotoc/db_retinue_data.pb.h"
#include "gprotoc/retinue_prop.pb.h"
#include "gprotoc/data_RetinueAttachProp.pb.h"
#include "gprotoc/retinue_private_info.pb.h"
#include "gprotoc/retinue_friend_info.pb.h"
#include "gprotoc/role_complicated_data_retinues.pb.h"
#include "gprotoc/role_complicated_data.pb.h"
#include "gprotoc/gp_retinue_notify.pb.h"
#include "gprotoc/gp_social_space_op_re.pb.h"
#include "gprotoc/gp_retinue_cast_skill.pb.h"
#include "gprotoc/gp_social_space_op.pb.h"
#include "gprotoc/retinue_info.pb.h"
#include "gprotoc/retinue_solution_data.pb.h"
#include "gprotoc/gp_npc_ss_status_notify.pb.h"
#include "gprotoc/retinue_fashion.pb.h"
#include "gprotoc/retinue_ss_status_info.pb.h"
#include "gprotoc/gp_ss_status_notify.pb.h"
#include "gprotoc/retinue_task_info.pb.h"
#include "gprotoc/RETINUE_SOLUTION_STATE.pb.h"
#include "gprotoc/retinue_client_info.pb.h"
#include "gprotoc/ipt_gs_check_bad_word.pb.h"
#include "gprotoc/retinue_formation_info.pb.h"
#include "gprotoc/gp_retinue_data_notify.pb.h"
#include "gprotoc/gp_retinue_op_re.pb.h"
#include "gprotoc/player_universal_data_t.pb.h"
#include "gprotoc/gp_retinue_operate.pb.h"
#include "gprotoc/retinue_gift_info.pb.h"
#include "gprotoc/role_trade_retinue_info.pb.h"

#include "npc.h"
#include <db_if.h>
#include "level_control.h"
#include "csvread.h"
#include "filter/filter_mount.h"
#include "funcswitchmgr.h"
//#include "bilog.h"
#include "slog.h"
#include <sstream>
#include "protoc_factory.h"
#include "global_config.h"
#include "senior_tower_reward_manager.h"
#include "achievement_manager.h"
#include "player_misc.h"

#define MAX_RETINUE_SOLUTION_NAME_LEN 16
int CalcRetinueFightCapacity(gcreature_imp *imp, RETINUE_PROP& prop)
{
	int atkType = imp->GetProperty().GetGProperty()->PROP_NAME_GET(atkType);

	int atk_value = (atkType == 2) ? prop.basePhyAtk : prop.baseMagAtk;
	int64_t value = 0.025 * (int64_t)prop.baseHP + 3 * atk_value + 2 * prop.basePhyDef +
	                2 * prop.baseMagDef + prop.baseCritLevel + prop.baseCDReduLevel + prop.basePierceLevel + prop.basePsychokinesisLevel;
	value /= 5;
	if (value > INT_MAX)
	{
		return INT_MAX;
	}
	if (value < INT_MIN)
	{
		return INT_MIN;
	}
	return value;
}

player_retinue::player_retinue(gcreature_imp *imp, player_retinue_manager *_p, const retinue_template *_t):
	_parent(_p), _template(_t),
	_self_fight_capacity(0), _attach_fight_capacity(0), _attached(false), _fashion_color_count(0), _slot_index(INVALID_COMBAT_INDEX)
{
	_data.set_retinue_id(_template->retinue_id);
	memset(&_prop, 0, sizeof(RETINUE_PROP));
	memset(&_attached_prop, 0, sizeof(RETINUE_PROP));
	memset(&_upgrade_prop, 0, sizeof(RETINUE_PROP));
}

void player_retinue::OnPlayerLevelUp(gcreature_imp *imp, int level)
{
	//_level = level;
	//_dirty = true;
	//GLog::formatlog("upgrade_retinue_level:roleid=%ld:retinue_id=%d:level=%d", imp->Parent()->ID.id, GetTID(), level);
}

int player_retinue::UpgradeQuality(gplayer_imp *imp)
{
	int cur_quality = GetQuality();
	int quality_limit = std::min(_template->max_quality, retinue_template_manager::GetInstance().GetRetinueUpgradeLimit(imp->GetLevel()));
	if (cur_quality >= quality_limit)
	{
		__PRINTF("RetinueUpgradeQuality, retinue reach max quality! roleid=%ld, retinue_id=%d\n", imp->Parent()->ID.id, GetTID());
		return S2C::ERR_RETINUE_MAX_QUALITY;
	}
	int need_item_id = _template->quality_config[cur_quality - 1].upgrade_cost_item_id;
	int need_item_count = _template->quality_config[cur_quality - 1].upgrade_cost_item_count;
	ITEM_CHECK_INFO info;
	if (need_item_id > 0 && need_item_count > 0 && !imp->CheckItemExistAtBackpack2(info, need_item_id, need_item_count))
	{
		__PRINTF("RetinueUpgradeQuality, item not enough! roleid=%ld, retinue_id=%d, itemid=%d, count=%d\n"
		         , imp->Parent()->ID.id, GetTID(), need_item_id, need_item_count);
		return S2C::ERR_RETINUE_UPGRADE_ITEM;
	}

	if (need_item_id > 0 && need_item_count > 0)
	{
		imp->DecItem2(info, {kFuncCodeRetinueUpgrade, cur_quality});
	}

	DeactiveSkill(imp);
	Dettach(imp);
	SetQuality(cur_quality + 1);
	RebuildProp(imp);
	Attach(imp);
	ActiveSkill(imp);

	_parent->UpdateMaxQuality(this);
	imp->GetAchievement().OnRetinueQualityChange(imp);
	//_dirty = true;
	// for BI
	BI_LOG_GLOBAL(imp->GetParent()->account.ToStr());
	SLOG(FORMAT, "upgrade_retinue_quality")
	.BI_HEADER2_GS(imp)
	.P("retinue_id", GetTID())
	.P("new_quality", cur_quality + 1)
	.P("cost_tid", need_item_id)
	.P("cost_num", need_item_count);
	return 0;
}

int player_retinue::ActivePrivateItem(gplayer_imp *imp, int item_id)
{
	for (int i = 0; i < _data.privates_size(); ++i)
	{
		if (_data.privates(i) == item_id)
		{
			//不能重复激活
			return S2C::ERR_RETINUE_ACTIVE_PRIVATE_ALREADY;
		}
	}

	bool found = false;
	for (int i = 0; i < RETINUE_PRIVATE_ITEM_COUNT; ++i)
	{
		if (_template->private_item[i] <= 0)
		{
			continue;
		}
		if (item_id == _template->private_item[i])
		{
			found = true;
			break;
		}
	}

	if (!found)
	{
		return S2C::ERR_RETINUE_ACTIVE_PRIVATE_NOT_MATCH;
	}

	Dettach(imp);
	_data.add_privates(item_id);
	RebuildProp(imp);
	Attach(imp);
	imp->GetAchievement().OnRetinuePrivateItem(imp);
	// for BI
	BI_LOG_GLOBAL(imp->GetParent()->account.ToStr());
	SLOG(FORMAT, "active_retinue_private")
	.BI_HEADER2_GS(imp)
	.P("retinue_id", GetTID())
	.P("item_id", item_id);
	return 0;
}

bool player_retinue::HasPrivateItem(int private_item_id) const
{
	for (int i = 0; i < _data.privates_size(); ++i)
	{
		if (_data.privates(i) == private_item_id)
		{
			return true;
		}
	}
	return false;
}

int player_retinue::ActiveFashionColor(gplayer_imp *imp, int fashion_index, int color_index)
{
	if (imp->IsRoamIn() && !GLOBAL_CONFIG.IsNormalCenterZone(GNET::g_zoneid))
	{
		__PRINTF("GS:roleid="   FMT_I64":跨服禁止时装解锁\n", imp->Parent()->ID.id);
		return S2C::ERR_SERVICE_UNAVILABLE;
	}

	if (fashion_index < 1 || fashion_index > RETINUE_FASHION_COUNT_MAX || color_index < 1 || color_index > RETINUE_FASHION_COLOR_COUNT_MAX)
	{
		return -1;
	}

	PB::retinue_fashion *pFashion = NULL;
	for (int i = 0; i < _data.fashions_size(); i++)
	{
		if (_data.fashions(i).fashion_index() == fashion_index)
		{
			pFashion = _data.mutable_fashions(i);
			break;
		}
	}
	auto& fashion_templ = _template->fashions[fashion_index - 1];
	int fashion_id = fashion_templ.fashion_id;
	if (!pFashion)
	{
		//如果没有找到此时装，检查玩家身上是否激活此时装，如果激活了添加下
		auto pPlayerFashion = imp->GetFashion().GetFashionDetailByIndex(fashion_id);
		if (!pPlayerFashion)
		{
			return S2C::ERR_RETINUE_PLAYER_FASHION_NOT_ACTIVE;
		}

		//初始化一下时装默认数据
		pFashion = _data.add_fashions();
		pFashion->set_fashion_index(fashion_index);
		pFashion->set_active_color_plan(0);
	}

	//检查颜色是否已经激活
	int active_color = pFashion->active_color_plan();
	if (active_color & (1 << (color_index - 1)))
	{
		return S2C::ERR_RETINUE_FASHION_ALREADY_ACTIVE;
	}

	//检查是否能激活这个颜色
	if (fashion_templ.color_count < color_index)
	{
		return -4;
	}

	auto& color_templ = fashion_templ.colors[color_index - 1];

	//检查需要的好友度
	if (color_templ.need_amity > 0 && _parent->GetRetinueAmity(GetTID()) < color_templ.need_amity)
	{
		return S2C::ERR_RETINUE_AMITY_LESS;
	}

	//money cost
	if (color_templ.cost > 0)
	{
		if (!imp->CheckMoneyAndCash(color_templ.cost_type, color_templ.cost))
		{
			return S2C::ERR_NOT_ENOUGH_MONEY;
		}

		//
		FuncInfo fi{kFuncCodeRetinueFashion, fashion_index, color_index};
		imp->CostMoneyAndCash(color_templ.cost_type, fi, color_templ.cost);
	}

	Dettach(imp);
	pFashion->set_active_color_plan(active_color | (1 << (color_index - 1)));
	RebuildProp(imp);
	Attach(imp);

	UpdateColorCount();
	imp->GetAchievement().OnRetinueFashionColor(imp);

	__PRINTF("player_retinue::ActiveFashionColor roleid=%ld:tid=%d:fashion_index=%d:color_index=%d\n", imp->Parent()->ID.id, GetTID(), fashion_index, color_index);
	GLog::formatlog("active_retinue_fashion", "roleid=%ld:retinue_id=%d:fashion_index=%d:color_index=%d", imp->Parent()->ID.id, GetTID(), fashion_index, color_index);
	return 0;
}

void player_retinue::UpdateColorCount()
{
	_fashion_color_count = 0;
	for (int i = 0; i < _data.fashions_size(); i++)
	{
		auto& fashion = _data.fashions(i);
		int active_color = fashion.active_color_plan();
		auto& fashion_templ = _template->fashions[fashion.fashion_index() - 1];
		for (int color_index = 1; color_index <= fashion_templ.color_count; color_index++)
		{
			if (active_color & (1 << (color_index - 1)))
			{
				_fashion_color_count++;
			}
		}
	}
}

int player_retinue::SelectFashion(gplayer_imp *imp, int fashion_index, int color_index)
{
	if (fashion_index < 1 || fashion_index > RETINUE_FASHION_COUNT_MAX || color_index < 1 || color_index > RETINUE_FASHION_COLOR_COUNT_MAX)
	{
		return -1;
	}

	PB::retinue_fashion *pFashion = NULL;
	for (int i = 0; i < _data.fashions_size(); i++)
	{
		if (_data.fashions(i).fashion_index() == fashion_index)
		{
			pFashion = _data.mutable_fashions(i);
			break;
		}
	}
	if (!pFashion)
	{
		return S2C::ERR_RETINUE_FAHSION_COLOR_NOT_ACTIVE;
	}

	//检查颜色是否已经激活
	int active_color = pFashion->active_color_plan();
	if (!(active_color & (1 << (color_index - 1))))
	{
		return S2C::ERR_RETINUE_FAHSION_COLOR_NOT_ACTIVE;
	}

	_data.set_select_fashion(fashion_index);
	_data.set_select_color(color_index);

	LOG_TRACE("player_retinue::SelectFashion roleid=%ld:retinue_id=%d:fashion_index=%d:color_index=%d", imp->Parent()->ID.id, GetTID(), fashion_index, color_index);
	return 0;
}

int player_retinue::Awake(gplayer_imp *pImp)
{
	CHECK_FUNC_SWITCH_AND_NOTIFY_RETURN_INT(kFuncCodeRetinueAwake, pImp, S2C::ERR_SERVICE_UNAVILABLE);
	int skillid = RETINUE_TEMPL.GetAwakeSkill(GetTID());
	if (skillid <= 0)
	{
		return -1;
	}
	if (GetQuality() < RETINUE_TEMPL.awake_retinue_quality_limit || GetLevel() < RETINUE_TEMPL.awake_retinue_level_limit)
	{
		return -2;
	}
	int old_level = _data.awake_level();
	int new_level = 0;
	bool upgrade = false;
	if (IsAwake())
	{
		new_level = _data.awake_level() + 1;
		upgrade = true;
	}
	auto pConfig = RETINUE_TEMPL.GetAwakeEquipConfig(new_level);
	if (!pConfig)
	{
		return -3;
	}
	std::vector<ITEM_CHECK_INFO> check_infos;
	for (auto& item : pConfig->cost_item)
	{
		ITEM_CHECK_INFO info;
		if (!pImp->CheckItemExistAt2(info, GNET::IL_BACKPACK, item.tid, item.count))
		{
			return S2C::ERR_NOT_ENOUGH_MATERIAL;
		}
		check_infos.push_back(info);
	}
	FuncInfo func_info{kFuncCodeRetinueAwake};
	for (auto& info : check_infos)
	{
		pImp->DecItem2(info, func_info);
	}

	DeactiveSkill(pImp);
	Dettach(pImp);
	if (!upgrade)
	{
		_data.set_awake_active(true);
	}
	_data.set_awake_level(new_level);
	RebuildProp(pImp);
	Attach(pImp);
	ActiveSkill(pImp);

	BI_LOG_GLOBAL(pImp->GetParent()->account.ToStr());
	SLOG(FORMAT, "retinue_awake")
	.BI_HEADER2_GS(pImp)
	.P("retinue_id", GetTID())
	.P("awake_type", upgrade ? 2 : 1)
	.P("old_equip_level", old_level)
	.P("equip_level", new_level)
	.P("cost_tid", pConfig->cost_item.size() > 0 ? pConfig->cost_item[0].tid : 0)
	.P("cost_num", pConfig->cost_item.size() > 0 ? pConfig->cost_item[0].count : 0)
	.P("cost_tid2", pConfig->cost_item.size() > 1 ? pConfig->cost_item[1].tid : 0)
	.P("cost_num2", pConfig->cost_item.size() > 1 ? pConfig->cost_item[1].count : 0);
	return 0;
}

int player_retinue_friend::Chat(gplayer_imp *pImp, int chat_id, int msg_id)
{
	if (chat_id <= 0 || msg_id <= 0)
	{
		return -1;
	}

	auto *pChatConfig = retinue_template_manager::GetInstance().GetRetinueChatConfig(chat_id);
	if (!pChatConfig || pChatConfig->retinue_id != GetTID())
	{
		return -2;
	}

	auto& chat = _data.cur_chat();
	int msg_state = 0;

	int add_amity = 0;
	if (chat.chat_id() == 0)
	{
		//新激活的对话, 检查下是否在激活列表里
		if (!_manager->IsChatActive(chat_id))
		{
			return -1;
		}

		if (msg_id != pChatConfig->start_msg_id)
		{
			return -2;
		}

		auto *pMap = retinue_template_manager::GetInstance().GetRetinueChatMsgConfig(pChatConfig->GetMsgId(pImp->GetGender()));
		if (!pMap)
		{
			return -3;
		}
		auto& msg_map = *pMap;
		auto it = msg_map.find(msg_id);
		if (it == msg_map.end())
		{
			return -4;
		}

		if (it->second.amity_add > 0)
		{
			AddAmity(it->second.amity_add);
			add_amity = it->second.amity_add;
		}
		if (it->second.repu_id > 0)
		{
			pImp->ModifyReputation({kFuncCodeRetinueChat, 0}, it->second.repu_id, it->second.repu_value);
		}

		//保存下进度
		_data.mutable_cur_chat()->set_chat_id(chat_id);
		_data.mutable_cur_chat()->add_msg_id(msg_id);
		_data.mutable_cur_chat()->set_gender(pImp->GetGender());
		_data.mutable_cur_chat()->set_timestamp(gmatrix::GetInstance().GetSysTime());

		msg_state = it->second.next_msg_state;
		SLOG(FORMAT, "retinue_chat_begin").P("role", pImp->Parent()->ID.id).P("retinue", GetTID()).P("chatid", chat_id).P("msgid", msg_id);
	}
	else
	{
		if (chat.chat_id() != chat_id)
		{
			return -10;
		}

		int cur_msg = 0;
		if (chat.msg_id_size() > 0)
		{
			cur_msg = chat.msg_id(chat.msg_id_size() - 1);
		}
		if (cur_msg <= 0)
		{
			return -12;
		}

		//判断下是不是下一句
		auto *pMap = retinue_template_manager::GetInstance().GetRetinueChatMsgConfig(pChatConfig->GetMsgId(chat.gender()));
		if (!pMap)
		{
			return -16;
		}
		auto& msg_map = *pMap;
		auto it = msg_map.find(cur_msg);
		if (it == msg_map.end())
		{
			return -13;
		}
		if (it->second.next_msg.find(msg_id) == it->second.next_msg.end())
		{
			return S2C::ERR_RETINUE_CHAT_MSG_ERROR;
		}

		auto next = msg_map.find(msg_id);
		if (next == msg_map.end())
		{
			return -15;
		}

		if (next->second.amity_add > 0)
		{
			AddAmity(next->second.amity_add);
			add_amity = it->second.amity_add;
		}
		if (next->second.repu_id > 0)
		{
			pImp->ModifyReputation({kFuncCodeRetinueChat, 0}, next->second.repu_id, next->second.repu_value);
		}

		_data.mutable_cur_chat()->add_msg_id(msg_id);
		msg_state = next->second.next_msg_state;
		SLOG(FORMAT, "retinue_chat_continue").P("role", pImp->Parent()->ID.id).P("retinue", GetTID()).P("chatid", chat_id).P("msgid", msg_id);
	}

	_manager->NotifyRetinueInfo(NULL, PB::gp_retinue_data_notify::N_TYPE_CHAT, GetTID(), chat_id, msg_id);

	if (msg_state == 3)
	{
		//对话结束 保存对话状态 客户端记录用
		for (int i = 0; i < _data.finish_chats_size(); ++i)
		{
			//先删除重复的对话 只保留最后一次
			if (_data.finish_chats(i).chat_id() == chat_id)
			{
				_data.mutable_finish_chats()->DeleteSubrange(i, 1);
				break;
			}
		}
		_data.add_finish_chats()->CopyFrom(_data.cur_chat());

		_manager->DelActiveChat(chat_id);

		//清除当前对话
		_data.clear_cur_chat();
		_manager->NotifyRetinueInfo(NULL, PB::gp_retinue_data_notify::N_TYPE_CHAT_FINISH, GetTID(), chat_id, msg_id);

		if (pChatConfig->finish_task > 0)
		{
			(*pImp->GetTaskGuard())->CheckDeliverTask(pChatConfig->finish_task);
		}

		SLOG(FORMAT, "retinue_chat_finish").P("role", pImp->Parent()->ID.id).P("retinue", GetTID()).P("chatid", chat_id).P("msgid", msg_id);
	}

	_manager->InteractLog(GetTID(), IL_TYPE_CHAT, add_amity);
	__PRINTF("player_retinue::Chat retinue=%d:chatid=%d:msg=%d\n", GetTID(), chat_id, msg_id);
	return 0;
}

void player_retinue_friend::GetChatRecord(int chat_id, PB::gp_retinue_data_notify& pb)
{
	if (chat_id == 0)
	{
		//取所有的对话
		if (_data.finish_chats_size() > 0)
		{
			auto *record = pb.add_chat_records();
			record->set_retinue_id(GetTID());
			for (int i = 0; i < _data.finish_chats_size(); ++ i)
			{
				record->add_chat()->CopyFrom(_data.finish_chats(i));
			}
		}
	}
	else
	{
		for (int i = 0; i < _data.finish_chats_size(); ++ i)
		{
			auto& chat = _data.finish_chats(i);
			if (chat.chat_id() == chat_id)
			{
				auto *record = pb.add_chat_records();
				record->set_retinue_id(GetTID());
				record->add_chat()->CopyFrom(_data.finish_chats(i));
				break;
			}
		}
	}
}

void player_retinue::GetAvailablePrivateItem(int grade, std::vector<int>& result)
{
	int start = 0;
	int count = 0;
	switch (grade)
	{
	case 0:
		start = RETINUE_PRIVATE_NORMAL_COUNT + RETINUE_PRIVATE_RARE_COUNT;
		count = RETINUE_PRIVATE_SPECIAL_COUNT;
		break;
	case 1:
		start = RETINUE_PRIVATE_NORMAL_COUNT;
		count = RETINUE_PRIVATE_RARE_COUNT;
		break;
	case 2:
		start = 0;
		count = RETINUE_PRIVATE_NORMAL_COUNT;
		break;
	default:
		return;
	}

	for (int i = start; i < start + count; ++i)
	{
		bool found = false;
		int item = GetTemplate()->private_item[i];
		if (item <= 0)
		{
			continue;
		}
		for (int index = 0; index < _data.privates_size(); ++index)
		{
			if ( _data.privates(index) == item)
			{
				found = true;
				break;
			}
		}
		//找出该伙伴还没有激活过的私有物
		if (!found)
		{
			result.push_back(item);
		}
	}
}

void player_retinue::OnGenerate(gplayer_imp *imp, int count)
{
	if (!_template)
	{
		return;
	}

	SetLevel(_template->init_level);
	SetQuality(_template->init_quality);
	SetCount(count);

	//检查能激活的羁绊技
	for (int i = 0; i < RETINUE_RELATION_MAX; ++i)
	{
		auto& relation = _template->relations[i];
		if (relation.target_retinue > 0 && relation.skill_id > 0)
		{
			if (imp->HasRetinue(relation.target_retinue))
			{
				AddRelationSkill(relation.skill_id);
			}
		}
	}

	//初始化默认时装
	auto fashion = _data.add_fashions();
	fashion->set_fashion_index(1);
	fashion->set_active_color_plan(1);
	_data.set_select_fashion(1);
	_data.set_select_color(1);
}

void player_retinue::RebuildProp(gcreature_imp *imp)
{
	//属性计算方式 :等级基础属性 * (模板单项属性修正 * 品质成长系数 * ( 1 + 羁绊修正倍率)) + 时装私有物加成
	const baseprop_t *base_prop = prof_template_manager::GetInstance().Get(RETINUE_DEFAULT_PROF, GetLevel());
	ASSERT(base_prop);

	float prop_add_rate[RETINUE_PROP_COUNT];
	float upgrade_prop_add_rate[RETINUE_PROP_COUNT];
	int prop_add_value[RETINUE_PROP_COUNT] = {0};
	float quatity_rate = (float)_template->quality_config[GetQuality() - 1].quality_growth / 100;
	float upgrade_quatity_rate = GetQuality() >= _template->max_quality ? quatity_rate : (float)_template->quality_config[GetQuality()].quality_growth / 100;
	for (int i = 0; i < RETINUE_PROP_COUNT; ++i)
	{
		prop_add_rate[i] = _template->base_prop_rate[i] * quatity_rate;
		upgrade_prop_add_rate[i] = _template->base_prop_rate[i] * upgrade_quatity_rate;
	}

	//羁绊技修正倍率
	float relation_skill_prop[RETINUE_PROP_COUNT];
	memset(relation_skill_prop, 0, sizeof(relation_skill_prop));
	for (int i = 0; i < _data.relation_skill_size(); ++i)
	{
		const retinue_relation_skill_template *pTemplate = retinue_template_manager::GetInstance().GetRetinueRelationSkill(_data.relation_skill(i));
		if (pTemplate)
		{
			for (int index = 0; index < RETINUE_PROP_COUNT; ++index)
			{
				if (pTemplate->prop_rate[index] > 1e-6)
				{
					relation_skill_prop[index] += pTemplate->prop_rate[index];
				}
			}
		}
	}
	//辅战位的羁绊伙伴对主战位羁绊技有加强
	if (CombatSlot() == MAIN_COMBAT_RETINUE_SLOT)
	{
		for (int i = 0; i < RETINUE_RELATION_MAX; ++i)
		{
			int target_id = _template->relations[i].target_retinue;
			if (target_id <= 0)
			{
				continue;
			}
			auto target_retinue = _parent->GetRetinue(target_id);
			if (!target_retinue || target_retinue->CombatSlot() <= 0)
			{
				continue;
			}
			float slot_ratio = retinue_template_manager::GetInstance().GetAssistCombatEnhanceRatio(target_retinue->CombatSlot());
			if (slot_ratio > 0.f)
			{
				const auto *pTemplate = retinue_template_manager::GetInstance().GetRetinueRelationSkill(_template->relations[i].skill_id);
				if (pTemplate)
				{
					for (int index = 0; index < RETINUE_PROP_COUNT; ++index)
					{
						if (pTemplate->prop_rate[index] > 1e-6)
						{
							relation_skill_prop[index] += pTemplate->prop_rate[index] * slot_ratio;
						}
					}
				}
			}
		}
	}

	for (int index = 0; index < RETINUE_PROP_COUNT; ++index)
	{
		if (relation_skill_prop[index] > 1e-6)
		{
			prop_add_rate[index] *= 1 + relation_skill_prop[index] / 100;
			upgrade_prop_add_rate[index] *= 1 + relation_skill_prop[index] / 100;
		}
	}

	//增加激活私有物的附加属性
	for (int i = 0; i < _data.privates_size(); ++i)
	{
		const retinue_private_item_template *pTemplate = retinue_template_manager::GetInstance().GetRetinuePrivateItem(_data.privates(i));
		if (pTemplate)
		{
			for (int index = 0; index < RETINUE_PROP_COUNT; ++index)
			{
				prop_add_value[index] += pTemplate->prop[index];
			}
		}
	}

	//增加时装颜色的附加属性
	for (int i = 0; i < _data.fashions_size(); ++i)
	{
		int fashion_index = _data.fashions(i).fashion_index();
		if (fashion_index < 1 || fashion_index > RETINUE_FASHION_COUNT_MAX)
		{
			continue;
		}
		int colors = _data.fashions(i).active_color_plan();
		int index = 0;
		while (colors > 0 && index < RETINUE_FASHION_COLOR_COUNT_MAX)
		{
			if (colors & 0x01)
			{
				auto& templ = _template->fashions[fashion_index - 1].colors[index];
				for (int j = 0; j < RETINUE_PROP_COUNT; ++j)
				{
					if (templ.prop[j] > 0)
					{
						prop_add_value[j] += templ.prop[j];
					}
				}
			}
			++index;
			colors = colors >> 1;
		}
	}

	//觉醒装置
	if (IsAwake())
	{
		auto *pConfig = RETINUE_TEMPL.GetAwakeEquipConfig(_data.awake_level());
		if (pConfig)
		{
			for (int ap = 0; ap < RETINUE_PROP_COUNT; ++ap)
			{
				if (pConfig->prop[ap] > 0)
				{
					prop_add_value[ap] += pConfig->prop[ap];
				}
			}
		}
	}

#define SET_RETINUE_PROP(type)  _prop.type = base_prop->type * prop_add_rate[prop_index] + prop_add_value[prop_index]; \
				_upgrade_prop.type = base_prop->type * upgrade_prop_add_rate[prop_index] + prop_add_value[prop_index]; \
                                prop_index ++;
	int prop_index = 0;
	SET_RETINUE_PROP(baseHP);
	SET_RETINUE_PROP(basePhyAtk);
	SET_RETINUE_PROP(baseMagAtk);
	SET_RETINUE_PROP(basePhyDef);
	SET_RETINUE_PROP(baseMagDef);
	SET_RETINUE_PROP(baseCritLevel);
	SET_RETINUE_PROP(basePierceLevel);
	SET_RETINUE_PROP(baseCDReduLevel);
	SET_RETINUE_PROP(basePsychokinesisLevel);
#undef SET_RETINUE_PROP

	_self_fight_capacity = CalcRetinueFightCapacity(imp, _prop);
	_attached_prop = _prop;

	float transfer_rate = CombatSlot() >= 0 ? retinue_template_manager::GetInstance().combat_retinue_property_ratio : _template->trans_rate;
	creature_prop::IncAllPropertyByRate(_attached_prop, transfer_rate);
	//_attach_fight_capacity = _CalcFightCapacity(imp, _attached_prop);
	//int attach_fight_capacity = CalcRetinueFightCapacity(imp, _attached_prop);
	int attach_fight_capacity = _self_fight_capacity * transfer_rate;
	_parent->UpdateRetinueAttachFightCapacity(GetTID(), attach_fight_capacity);

	if (CombatSlot() >= 0)
	{
		auto *slot = _parent->GetCombatSlot(CombatSlot());
		if (slot)
		{
			auto& prop =  slot->_prop;
			prop.Import(&_prop);
			prop.SET_PROP_BY_NAME(baseCritRatio, base_prop->baseCritRatio, creature_prop::CPM_BASE);
			prop.Update();
		}
	}

	__PRINTF("retinue refresh prop, retinueid=%d,level=%d,quality=%d,power=%d\n", GetTID(), GetLevel(), GetQuality(), _self_fight_capacity);
}

void player_retinue::Attach(gcreature_imp *imp)
{
	if (_attached)
	{
		return;
	}
	property_template::data_RetinueAttachProp& point_prop = *(property_template::data_RetinueAttachProp *)&_attached_prop;
	ConditionSuppressCombatStat(imp, SuppressCombatSystem::RETINUE)
		.Then(nullptr, "player_retinue::Attach: suppress attach")
		.Else([&](gcreature_imp*) {
			imp->GetProperty().IncByStruct(point_prop);
		}, "player_retinue::Attach: normal attach");
	_attached = true;
}

void player_retinue::Dettach(gcreature_imp *imp)
{
	if (!_attached)
	{
		return;
	}
	property_template::data_RetinueAttachProp& point_prop = *(property_template::data_RetinueAttachProp *)&_attached_prop;
	ConditionSuppressCombatStat(imp, SuppressCombatSystem::RETINUE)
		.Then(nullptr, "player_retinue::Dettach: suppress dettach")
		.Else([&](gcreature_imp*) {
			imp->GetProperty().DecByStruct(point_prop);
		}, "player_retinue::Dettach: normal dettach");
	_attached = false;
}

void player_retinue::DeactiveSkill(gcreature_imp *imp)
{
	auto& support_skills = _template->quality_config[GetQuality() - 1].support_skills;
	auto& combat_skill = _template->quality_config[GetQuality() - 1].active_skill;
	auto& support_extra_skills = _template->quality_config[GetQuality() - 1].support_skills_extra;

	object_interface oif(imp);
	for (int i = 0; i < RETINUE_SUPPORT_SKILL_COUNT; ++ i)
	{
		if (support_skills[i].skill_id > 0)
		{
			if (CombatSlot() == MAIN_COMBAT_RETINUE_SLOT || (CombatSlot() > 0 && support_skills[i].assist_can_use))
			{
				ConditionSuppressCombatStat(imp, SuppressCombatSystem::RETINUE)
					.Then(nullptr, "player_retinue::DeactiveSkill: suppress support skill remove")
					.Else([&](gcreature_imp*) {
						imp->GetSkill().RemoveSkill(support_skills[i].skill_id, support_skills[i].skill_level, oif);
					}, "player_retinue::DeactiveSkill: normal support skill remove");
				_parent->EraseBuffSkillRelation(support_skills[i].skill_id);
				if (support_extra_skills[i] > 0)
				{
					_parent->EraseBuffSkillExtraRelation(support_extra_skills[i]);
				}
			}
		}
	}
	if (combat_skill.skill_id > 0)
	{
		if (CombatSlot() == MAIN_COMBAT_RETINUE_SLOT || (CombatSlot() > 0 && combat_skill.assist_can_use))
		{
			ConditionSuppressCombatStat(imp, SuppressCombatSystem::RETINUE)
				.Then(nullptr, "player_retinue::DeactiveSkill: suppress combat skill remove")
				.Else([&](gcreature_imp*) {
					imp->GetSkill().RemoveSkill(combat_skill.skill_id, combat_skill.skill_level, oif);
				}, "player_retinue::DeactiveSkill: normal combat skill remove");
			_parent->EraseBuffSkillRelation(combat_skill.skill_id);
		}
	}
	if (IsAwake())
	{
		int awake_skill = RETINUE_TEMPL.GetAwakeSkill(GetTID());
		if (awake_skill > 0)
		{
			ConditionSuppressCombatStat(imp, SuppressCombatSystem::RETINUE)
				.Then(nullptr, "player_retinue::DeactiveSkill: suppress awake skill remove")
				.Else([&](gcreature_imp*) {
					imp->GetSkill().RemoveSkill(awake_skill, 0, oif);
				}, "player_retinue::DeactiveSkill: normal awake skill remove");
		}
		int awake_skill_extra = RETINUE_TEMPL.GetAwakeSkillExtra(GetTID());
		if (awake_skill_extra)
		{
			_parent->EraseAwakeSkillExtraRelation(awake_skill_extra);
		}
	}
}

void player_retinue::ActiveSkill(gcreature_imp *imp)
{
	auto& support_skills = _template->quality_config[GetQuality() - 1].support_skills;
	auto& support_extra_skills = _template->quality_config[GetQuality() - 1].support_skills_extra;
	auto& combat_skill = _template->quality_config[GetQuality() - 1].active_skill;

	object_interface oif(imp);
	if (combat_skill.skill_id > 0)
	{
		if (CombatSlot() == MAIN_COMBAT_RETINUE_SLOT || (CombatSlot() > 0 && combat_skill.assist_can_use))
		{
			_parent->AddBuffSkillRelation(combat_skill.skill_id, GetTID(), CombatSlot());
			ConditionSuppressCombatStat(imp, SuppressCombatSystem::RETINUE)
				.Then(nullptr, "player_retinue::ActiveSkill: suppress combat skill insert")
				.Else([&](gcreature_imp*) {
					imp->GetSkill().InsertSkill(combat_skill.skill_id, combat_skill.skill_level, oif);
				}, "player_retinue::ActiveSkill: normal combat skill insert");
		}
	}
	for (int i = 0; i < RETINUE_SUPPORT_SKILL_COUNT; ++ i)
	{
		if (support_skills[i].skill_id > 0)
		{
			if (CombatSlot() == MAIN_COMBAT_RETINUE_SLOT || (CombatSlot() > 0 && support_skills[i].assist_can_use))
			{
				_parent->AddBuffSkillRelation(support_skills[i].skill_id, GetTID(), CombatSlot());
				if (support_extra_skills[i] > 0)
				{
					_parent->AddBuffSkillExtraRelation(support_extra_skills[i], GetTID(), CombatSlot());
				}
				ConditionSuppressCombatStat(imp, SuppressCombatSystem::RETINUE)
					.Then(nullptr, "player_retinue::ActiveSkill: suppress support skill insert")
					.Else([&](gcreature_imp*) {
						imp->GetSkill().InsertSkill(support_skills[i].skill_id, support_skills[i].skill_level, oif);
					}, "player_retinue::ActiveSkill: normal support skill insert");
			}
		}
	}
	if (IsAwake())
	{
		int awake_skill = RETINUE_TEMPL.GetAwakeSkill(GetTID());
		if (awake_skill > 0)
		{
			auto *pConfig = RETINUE_TEMPL.GetAwakeEquipConfig(_data.awake_level());
			if (pConfig)
			{
				ConditionSuppressCombatStat(imp, SuppressCombatSystem::RETINUE)
					.Then(nullptr, "player_retinue::ActiveSkill: suppress awake skill insert")
					.Else([&](gcreature_imp*) {
						imp->GetSkill().InsertSkill(awake_skill, pConfig->skill_level, oif);
					}, "player_retinue::ActiveSkill: normal awake skill insert");
			}
		}
		int awake_skill_extra = RETINUE_TEMPL.GetAwakeSkillExtra(GetTID());
		if (awake_skill_extra > 0)
		{
			_parent->AddAwakeSkillExtraRelation(awake_skill_extra, GetTID(), CombatSlot());
		}
	}
}

void player_retinue::OnAssistRetinueChange(gcreature_imp *imp, int index, int old_retinue, int new_retinue)
{
	if (CombatSlot() != MAIN_COMBAT_RETINUE_SLOT)
	{
		return;
	}
	bool old_active = false;
	bool new_active = false;
	for (int i = 0; i < RETINUE_RELATION_MAX; ++i)
	{
		auto& relation = _template->relations[i];
		if (relation.target_retinue > 0 && relation.skill_id > 0)
		{
			if (old_retinue == relation.target_retinue)
			{
				old_active = true;
			}
			else if (new_retinue == relation.target_retinue)
			{
				new_active = true;
			}
		}
	}

	if (old_active || new_active)
	{
		Dettach(imp);
		RebuildProp(imp);
		Attach(imp);
		_parent->SendRetinue(this);
	}
}

player_retinue_friend::player_retinue_friend(player_retinue_manager *_p, int _tid): _manager(_p)
{
	_data.set_retinue_id(_tid);
}

void player_retinue_friend::AddAmity(int offset)
{
	int res = _data.amity() + offset;
	if (res < 0)
	{
		res = 0;
	}
	_data.set_amity(res);
	if (_manager)
	{
		_manager->SetAmityChanged();
	}
}

void player_retinue_friend::RefreshMood()
{
	int mood = 0;
	int last = 0;
	retinue_template_manager::GetInstance().RandRetinueMood(GetTID(), mood, last);
	_data.set_mood(mood);
	_data.set_mood_timestamp(gmatrix::GetInstance().GetSysTime() + last);
}

void player_retinue::MakeClientData(PB::retinue_client_info& info)
{
	info.mutable_base_info()->CopyFrom(_data);

	_prop.ExportTo(info.mutable_prop());
	_upgrade_prop.ExportTo(info.mutable_upgrade_prop());
	info.set_fight_capacity(_self_fight_capacity);
}

void player_retinue::MakeChatData(gplayer_imp *pImp, PB::retinue_chat_info& info)
{
	info.set_retinue_id(_data.retinue_id());
	info.set_level(_data.level());
	info.set_quality(_data.quality());

	info.clear_relation_retinue();
	for (int i = 0; i < RETINUE_RELATION_MAX; ++i)
	{
		auto& relation = _template->relations[i];
		if (relation.target_retinue > 0 && relation.skill_id > 0)
		{
			if (pImp->HasRetinue(relation.target_retinue))
			{
				info.add_relation_retinue(relation.target_retinue);
			}
		}
	}

	info.mutable_privates()->CopyFrom(_data.privates());
	info.set_awake(_data.awake_active());
	info.set_awake_level(_data.awake_level());
	_prop.ExportTo(info.mutable_prop());
}

void player_retinue::MakeComplicatedData(PB::role_complicated_data_retinue& complicated_data, int slot_index)
{
	complicated_data.set_retinue_id(GetTID());
	complicated_data.set_quality(GetQuality());
	complicated_data.set_level(GetLevel());
	complicated_data.set_fight_capacity(_self_fight_capacity);
	complicated_data.set_fashion_index(_data.select_fashion());
	complicated_data.set_color_index(_data.select_color());
	complicated_data.set_slot_index(slot_index);
	complicated_data.set_awake_level(_data.awake_level());
	complicated_data.set_awake_active(_data.awake_active());
	_prop.ExportTo(complicated_data.mutable_prop());
}

void player_retinue_manager::MakeHighestComplicatedData(PB::role_complicated_data_retinue& complicated_data)
{
	player_retinue *highestRetinue = NULL;
	int max_fight_capacity = 0;
	for (auto it = _retinues.begin(); it != _retinues.end(); ++ it)
	{
		auto *retinue = it->second;
		int fight_capacity = retinue->GetFightCapacity();
		if (max_fight_capacity < fight_capacity)
		{
			max_fight_capacity = fight_capacity;
			highestRetinue = retinue;
		}
	}
	if (highestRetinue)
	{
		highestRetinue->MakeComplicatedData(complicated_data);
	}
}
void player_retinue_manager::MakeComplicatedData(PB::role_complicated_data_retinues& complicated_data)
{
	complicated_data.set_role_fight_capacity(_total_attach_fightcapacity);
	for (auto& kv : _combat_retinues)
	{
		kv.second._retinue->MakeComplicatedData(*complicated_data.add_retinue(), kv.first);
	}
}

int player_retinue_manager::CheckAddRetinue(int tid, int count)
{
	gplayer_imp *pImp = GetPlayerImp();
	if (pImp)
	{
		CHECK_FUNC_SWITCH_AND_NOTIFY_RETURN_INT(kFuncCodeRetinue, pImp, S2C::ERR_SERVICE_UNAVILABLE);
		CHECK_FUNC_SWITCH_AND_NOTIFY_RETURN_INT(kFuncCodeRetinueAdd, pImp, S2C::ERR_SERVICE_UNAVILABLE);
	}
	if (count < 1)
	{
		return S2C::ERR_SERVICE_UNAVILABLE;
	}

	const retinue_template *pTemplate = retinue_template_manager::GetInstance().GetRetinueTemplate(tid);
	if (!pTemplate )
	{
		return -2;
	}

	return 0;
}

int player_retinue_manager::CheckAddRetinueGift(int tid, int count)
{
	if (!GET_FUNC_SWITCH(kFuncCodeRetinueGift))
	{
		return S2C::ERR_SERVICE_UNAVILABLE;
	}
	if (count < 1)
	{
		return S2C::ERR_SERVICE_UNAVILABLE;
	}

	if (retinue_template_manager::GetInstance().GetRetinueGiftConfig(tid) < 0)
	{
		return -2;
	}

	return 0;
}

int player_retinue_manager::AddRetinue(int tid, int count, int inc_type, bool& is_auto_decompose, int extra_data)
{
	gplayer_imp *pImp = GetPlayerImp();
	if (!pImp)
	{
		return S2C::ERR_SERVICE_UNAVILABLE;
	}

	int ret = CheckAddRetinue(tid, count);
	if (ret != 0)
	{
		return ret;
	}

	//这里再检查一遍是为了统计服务器事件
	CHECK_FUNC_SWITCH_AND_RETURN_INT(kFuncCodeRetinueAdd, pImp, S2C::ERR_SERVICE_UNAVILABLE);

	const retinue_template *pTemplate = retinue_template_manager::GetInstance().GetRetinueTemplate(tid);
	if (!pTemplate )
	{
		return S2C::ERR_SERVICE_UNAVILABLE;
	}

	if (pTemplate->is_special)
	{
		//获得伙伴经验卡
		_special_retinues[tid] += count;
		int total_count = _special_retinues[tid];

		NotifyRetinueInfo(NULL, PB::gp_retinue_data_notify::N_TYPE_ADD_SPECIAL, tid, total_count);

		BI_LOG_GLOBAL(pImp->GetParent()->account.ToStr());
		SLOG(FORMAT, "add_retinue")
		.BI_HEADER2_GS(pImp)
		.P("retinue_id", tid)
		.P("count", total_count);

		return 0;
	}

	player_retinue *nretinue = NULL;
	bool is_new = false;
	auto it = _retinues.find(tid);
	if (it != _retinues.end())
	{
		nretinue = it->second;
		nretinue->SetCount(nretinue->GetCount() + count);
	}
	else
	{
		nretinue = new player_retinue(_imp, this, pTemplate);
		nretinue->OnGenerate(pImp, count);
		nretinue->RebuildProp(_imp);
		nretinue->Attach(_imp);

		//放入队列
		_retinues[tid] = nretinue;

		//刷新相关伙伴的羁绊属性
		auto *relation_retinues = retinue_template_manager::GetInstance().GetRelationRetinues(tid);
		if (relation_retinues)
		{
			for ( auto relation_id : *relation_retinues)
			{
				auto relation_retinue = GetRetinue(relation_id);
				if (relation_retinue)
				{
					//找到对应伙伴的羁绊技
					for (int i = 0; i < RETINUE_RELATION_MAX; ++i)
					{
						auto& tmp = relation_retinue->GetTemplate()->relations[i];
						if (tid == tmp.target_retinue)
						{
							relation_retinue->Dettach(_imp);
							relation_retinue->AddRelationSkill(tmp.skill_id);
							relation_retinue->RebuildProp(_imp);
							relation_retinue->Attach(_imp);
							SendRetinue(relation_retinue);
							break;
						}
					}
				}
			}
		}

		is_new = true;

		pImp->GetAchievement().OnGetRetinueEvent(pImp, tid);
	}

	UpdateStarCounter();
	UpdateMaxQuality(nretinue);
	pImp->GetAchievement().OnGetRetinue(pImp);
	pImp->GetAchievement().OnCommonFightingCapacityUpdate(pImp, 2, _total_attach_fightcapacity);
	//通知客户端
	NotifyRetinueInfo(nretinue, PB::gp_retinue_data_notify::N_TYPE_ADD_RETINUE, nretinue->GetCount(), inc_type);

	//低星自动拆分
	if (nretinue->GetTemplate()->star <= 4 && nretinue->GetCount() > 1)
	{
		if (DecomposeRetinue(nretinue->GetTID(), nretinue->GetCount() - 1) == 0)
		{
			is_auto_decompose = true;
		}
	}
	else
	{
		is_auto_decompose = false;
	}

	if (is_new) //客户端要求最后再通知
	{
		//获得新伙伴自动加好友
		//AddRetinueFriend(tid);
		//获得新伙伴解锁聊天装扮
		chatbox_template_manager::Instance().OnAddRetinue(pImp, tid);

		//首次获得伙伴发任务
		int task_id = retinue_template_manager::GetInstance().GetNewRetinueTaskId(tid);
		if (task_id > 0)
		{
			(*pImp->GetTaskGuard())->CheckDeliverTask(task_id);
		}
	}

	if (inc_type == INC_RETINUE_TYPE_RECRUIT)
	{
		int speak_id = retinue_template_manager::GetInstance().GetRecruitSpeakId(tid, extra_data);
		if (speak_id > 0)
		{
			gplayer_imp::SPEAK_PARAM_MAP map;

			//raw_wrapper& h1 = map[SYS_SPEAK::PLAYER_NAME];
			//SYS_SPEAK::MakeRoleName(h1, SYS_SPEAK::PLAYER_NAME, pImp->Parent()->ID.id, pImp->GetPlayerNameSize(), pImp->GetPlayerNameBuf());

			//取伙伴的基础属性值
			int prop[RETINUE_PROP_COUNT] = {0};
			const baseprop_t *base_prop = prof_template_manager::GetInstance().Get(RETINUE_DEFAULT_PROF, nretinue->GetLevel());
			float prop_add_rate[RETINUE_PROP_COUNT] = {0};
			float quatity_rate = (float)nretinue->GetTemplate()->quality_config[nretinue->GetQuality() - 1].quality_growth / 100;
			for (int i = 0; i < RETINUE_PROP_COUNT; ++i)
			{
				prop_add_rate[i] = nretinue->GetTemplate()->base_prop_rate[i] * quatity_rate;
			}
			if (base_prop)
			{
				prop[0] = base_prop->baseHP * prop_add_rate[0];
				prop[1] = base_prop->basePhyAtk * prop_add_rate[1];
				prop[2] = base_prop->baseMagAtk * prop_add_rate[2];
				prop[3] = base_prop->basePhyDef * prop_add_rate[3];
				prop[4] = base_prop->baseMagDef * prop_add_rate[4];
				prop[5] = base_prop->baseCritLevel * prop_add_rate[5];
				prop[6] = base_prop->basePierceLevel * prop_add_rate[6];
				prop[7] = base_prop->baseCDReduLevel * prop_add_rate[7];
				prop[8] = base_prop->basePsychokinesisLevel * prop_add_rate[8];
			}

			raw_wrapper& h2 = map[SYS_SPEAK::RETINUE];
			SYS_SPEAK::MakeRetinueData(h2, SYS_SPEAK::RETINUE, tid, prop, RETINUE_PROP_COUNT);

			pImp->SystemSpeak2(speak_id, &map);
		}
	}

	if (is_new || !is_auto_decompose)
	{
		// for BI
		BI_LOG_GLOBAL(pImp->GetParent()->account.ToStr());
		SLOG(FORMAT, "add_retinue")
		.BI_HEADER2_GS(pImp)
		.P("retinue_id", tid)
		.P("count", count)
		.P("inc_type", inc_type);

		if (nretinue->GetTemplate()->star == 5)
		{
			pImp->UploadQQInfo(GNET::BTYPE_5STAR_RETINUE, tid);
		}
		pImp->UploadQQInfo(GNET::BTYPE_RETINUE_COUNT, GetSize());
	}

	__PRINTF("player:%ld obtain retinue:%d, total=%d\n", _imp->Parent()->ID.id, tid, nretinue->GetCount());

	return 0;
}

int player_retinue_manager::AddRetinueFriend(int tid)
{
	if (GetRetinueFriend(tid))
	{
		//已经是好友了
		return -1;
	}
	const retinue_template *pTemplate = retinue_template_manager::GetInstance().GetRetinueTemplate(tid);
	if (!pTemplate)
	{
		return -2;
	}

	if (!pTemplate->can_be_friend)
	{
		return -3;
	}

	player_retinue_friend *new_friend = new player_retinue_friend(this, tid);
	_retinue_friends[tid] = new_friend;

	//初始化心情
	new_friend->RefreshMood();

	//通知客户端
	NotifyRetinueInfo(NULL, PB::gp_retinue_data_notify::N_TYPE_FRIEND, tid, new_friend->GetMood());

	GLog::formatlog("add_retinue_friend", "roleid=%ld:retinue_id=%d", _imp->Parent()->ID.id, tid);
	__PRINTF("player:%ld get retinue friend:%d\n", _imp->Parent()->ID.id, tid);

	return 0;
}

int player_retinue_manager::AddRetinueGift(int tid, int count)
{
	gplayer_imp *pImp = GetPlayerImp();
	if (!pImp)
	{
		return S2C::ERR_SERVICE_UNAVILABLE;
	}

	if (CheckAddRetinueGift(tid, count) != 0)
	{
		return S2C::ERR_SERVICE_UNAVILABLE;
	}

	CHECK_FUNC_SWITCH_AND_RETURN_INT(kFuncCodeRetinueGift, pImp, S2C::ERR_SERVICE_UNAVILABLE);

	_gift_map[tid] += count;

	int total_count = _gift_map[tid];
	NotifyRetinueInfo(NULL, PB::gp_retinue_data_notify::N_TYPE_GIFT_ADD, tid, total_count);
	__PRINTF("player_retinue_manager::AddRetinueGift rolelid=%ld:tid=%d:add:%d:total=%d\n", _imp->GetParent()->ID.id, tid, count, total_count);
	GLog::formatlog("add_retinue_gift", "rolelid=%ld:tid=%d:add:%d:total=%d", _imp->GetParent()->ID.id, tid, count, total_count);
	return 0;
}

bool player_retinue_manager::UpdateRetinueSkill(size_t index)
{
	return false;
}

player_retinue_manager::~player_retinue_manager()
{
	for (auto it = _retinues.begin(); it != _retinues.end(); ++ it)
	{
		delete  it->second;
	}
	_retinues.clear();
	for (auto it = _retinue_friends.begin(); it != _retinue_friends.end(); ++ it)
	{
		delete  it->second;
	}
	_retinue_friends.clear();
	_combat_retinues.clear();
}

/*void player_retinue_manager::DelFormationPropToCreatue()
{
	if (_is_actived)
	{
		std::stringstream ss;
		ss << "formationid=" << _formation_id;
		ss << ":final_prop=" << _r_prop.pointBaseProperty1 << ":" << _r_prop.pointBaseProperty2
		   << ":" << _r_prop.pointBaseProperty3 << ":" << _r_prop.pointBaseProperty4 << ":" << _r_prop.pointBaseProperty5;
		__PRINTF("DelFormationPropToCreatue, roleid=%ld, %s\n", _imp->GetParent()->ID.id, ss.str().c_str());
		_imp->GetProperty().DecByStruct(_r_prop, creature_prop::CPM_BASE);
		memset(&_r_prop, 0, sizeof(_r_prop));
		_is_actived = false;
	}
}*/

/*void player_retinue_manager::AddFormationPropToCreatue()
{
	auto pformation = retinue_template_manager::GetInstance().GetFormationTemplate(_formation_id);
	if (!pformation)
	{
		return;
	}

	memset(&_r_prop, 0, sizeof(_r_prop));
	std::stringstream ss;
	float whole_fitness_factor = 1.2f;
	player_retinue *retinues[pformation->retinue_count];
	float fitness[pformation->retinue_count];
	for (int i = 0; i < pformation->retinue_count; i ++)
	{
		auto pretinue = GetRetinue(_formation[i]);
		ASSERT(pretinue);
		retinues[i] = pretinue;
		fitness[i] = fitness_factor(pretinue->_template->feature, pformation->postition_config[i].feature);
		if (fitness[i] != 1.2f)
		{
			whole_fitness_factor = 1.0f;
		}
	}

	float final_prop[5];
	memset(final_prop, 0, sizeof(final_prop));
	ss << "formationid=" << _formation_id << ":whole_fit_factor=" << whole_fitness_factor;
	for (int i = 0; i < pformation->retinue_count; i ++)
	{
		auto pretinue = retinues[i];
		ss << "\n\tretinue_id=" << pretinue->GetTID() <<  ":feature=" << pretinue->_template->feature << ":index_feature=" << pformation->postition_config[i].feature;
		ss << ":fit_factor=" << fitness[i];
	#define UPDATE_FORMATION_PROP(A, B, index) \
		{	\
		float tmp = pretinue->base_prop.B##prop * (pformation->base_prop_factor * whole_fitness_factor \
				+ pformation->postition_config[i].B##factor * fitness[i] ); \
		final_prop[index] += tmp; \
		ss << "\n\t\tprop" << index << "=" << pretinue->base_prop.B##prop << ":add="<< tmp <<":total=" << final_prop[index] << ":postition_config=" << pformation->postition_config[i].B##factor\
			<< ":base_prop_factor=" << pformation->base_prop_factor; \
		}

		UPDATE_FORMATION_PROP(pointBaseProperty1, att_, 0);
		UPDATE_FORMATION_PROP(pointBaseProperty2, str_, 1);
		UPDATE_FORMATION_PROP(pointBaseProperty3, int_, 2);
		UPDATE_FORMATION_PROP(pointBaseProperty4, spt_, 3);
		UPDATE_FORMATION_PROP(pointBaseProperty5, wll_, 4);

	#undef UPDATE_FORMATION_PROP
	}
	_r_prop.pointBaseProperty1 = final_prop[0];
	_r_prop.pointBaseProperty2 = final_prop[1];
	_r_prop.pointBaseProperty3 = final_prop[2];
	_r_prop.pointBaseProperty4 = final_prop[3];
	_r_prop.pointBaseProperty5 = final_prop[4];
	ss << "\nfinal_prop=" << final_prop[0] << ":" << final_prop[1]  << ":" << final_prop[2] << ":" << final_prop[3] << ":" << final_prop[4];
	ss << ":prop=" << _r_prop.pointBaseProperty1 << ":" << _r_prop.pointBaseProperty2
	   << ":" << _r_prop.pointBaseProperty3 << ":" << _r_prop.pointBaseProperty4 << ":" << _r_prop.pointBaseProperty5;

	_is_actived = true;
	_imp->GetProperty().IncByStruct(_r_prop, creature_prop::CPM_BASE);
	__PRINTF("AddFormationPropToCreatue, roleid=%ld, %s\n", _imp->GetParent()->ID.id, ss.str().c_str());
}*/
bool player_retinue_manager::Load(gcreature_imp *pImp, raw_wrapper& rw)
{
	//_imp = pImp;

	PB::db_retinue_data retinue_data;
	if (rw.size() > 0)
	{
		if (!retinue_data.ParseFromArray(rw.data(), rw.size()))
		{
			GLog::log(GLOG_ERR, "玩家: " FMT_I64" 解析随从数据失败", pImp->GetParent()->ID.id);
			return true;
		}
	}
	else
	{
		return true;
	}
	for (int i = 0; i < retinue_data.retinues_size(); ++i)
	{
		int retinue_id = retinue_data.retinues(i).retinue_id();
		const retinue_template *pTemplate = retinue_template_manager::GetInstance().GetRetinueTemplate(retinue_id);
		if (!pTemplate)
		{
			GLog::log(LOG_ERR, "玩家: "   FMT_I64" 加载第%d随从的模板时出错,tid:%d\n",
			          pImp->Parent()->ID.id, (int)i, retinue_id);
			return false;
		}

		player_retinue *p_retinue = new player_retinue(pImp, this, pTemplate);
		p_retinue->GetData().CopyFrom(retinue_data.retinues(i));
		p_retinue->UpdateColorCount();

		_retinues[retinue_id] = p_retinue;
		if (p_retinue->IsAwake())
		{
			_awake_retinues.insert(retinue_id);
		}
	}
	for (int i = 0; i < retinue_data.friends_size(); ++i)
	{
		int retinue_id = retinue_data.friends(i).retinue_id();
		player_retinue_friend *p_retinue = new player_retinue_friend(this, retinue_id);
		p_retinue->GetData().CopyFrom(retinue_data.friends(i));

		//新老收礼数据转换
		if (p_retinue->GetData().take_gift_level() > 0 && p_retinue->GetData().take_gift_mask() == 0)
		{
			int gift_mask = p_retinue->GetData().take_gift_mask();
			int gift_level = p_retinue->GetData().take_gift_level();
			gift_mask |= ~((~0) << gift_level);
			p_retinue->GetData().set_take_gift_mask(gift_mask);
			p_retinue->GetData().set_take_gift_level(0);
			LOG_TRACE("player_retinue_manager::Load Transfer take_gift_level player=%ld:retinue=%d:level=%d:mask=%d",
			          pImp->Parent()->ID.id, retinue_id, gift_level, gift_mask);
		}

		_retinue_friends[retinue_id] = p_retinue;
	}

	if (retinue_data.select_id() > 0)
	{
		auto retinue = GetRetinue(retinue_data.select_id());
		if (retinue)
		{
			_combat_retinues.insert(std::make_pair(MAIN_COMBAT_RETINUE_SLOT, RETINUE_SLOT_INFO(pImp, retinue)));
			retinue->SetCombatSlot(MAIN_COMBAT_RETINUE_SLOT);
		}
	}
	_assist_combat_unlock_mask = retinue_data.assist_combat_unlock_mask();
	for (int i = 0; i < retinue_data.assist_combat_retinues_size(); ++i)
	{
		int assist_id = retinue_data.assist_combat_retinues(i);
		if (assist_id <= 0)
		{
			continue;
		}
		auto retinue = GetRetinue(assist_id);
		if (retinue)
		{
			_combat_retinues.insert(std::make_pair(i + 1, RETINUE_SLOT_INFO(pImp, retinue)));
			retinue->SetCombatSlot(i + 1);
		}
	}

	for (int i = 0; i < retinue_data.privates_size(); ++i)
	{
		_private_item_map[retinue_data.privates(i).id()] = retinue_data.privates(i).count();
	}

	for (int i = 0; i < retinue_data.gifts_size(); ++i)
	{
		_gift_map[retinue_data.gifts(i).id()] = retinue_data.gifts(i).count();
	}

	/*_formation_id = retinue_data.formation().formation_id();
	if (_formation_id)
	{
		auto pformation = retinue_template_manager::GetInstance().GetFormationTemplate(_formation_id);
		if (pformation)
		{
			auto count = 0;
			for (auto i = 0; i < retinue_data.formation().retinue_id_size() && count < pformation->retinue_count; i ++)
			{
				_formation[i] = retinue_data.formation().retinue_id(i);
				if (_formation[i])
				{
					count++;
				}
			}
			if (count != pformation->retinue_count)
			{
				GLog::log(LOG_ERR, "玩家: "   FMT_I64" 加载随从阵法时出错,formationid:%d\n",
				          pImp->Parent()->ID.id, _formation_id);
				_formation_id = 0;
				memset(_formation, 0, sizeof(_formation));
			}
		}

	}
	for (int i = 0; i < retinue_data.formation_owned_size(); i ++)
	{
		_owned_formations.insert(retinue_data.formation_owned(i));
	}*/
	for (int i = 0; i < retinue_data.tasks_size(); i ++)
	{
		auto retinue_task = retinue_data.tasks(i);
		_retinue_tasks[retinue_task.taskid()].CopyFrom(retinue_task);
		//check retinue 数据上是否有此task
		for (auto j = 0; j < retinue_task.retinue_id_size(); ++j)
		{
			auto retinue = GetRetinue(retinue_task.retinue_id(j));
			if (retinue)
			{
				if (retinue->GetTaskID() != retinue_task.taskid())
				{
					GLog::log(LOG_ERR, "retinue_load check failed!, roleid=%ld, retinue=%d, task=%d, retinue_task=%d",
					          _imp->GetParent()->ID.id, retinue_task.retinue_id(j), retinue_task.taskid(), retinue->GetTaskID());
				}
			}
		}
	}

	if (retinue_data.has_ex_task())
	{
		_ex_task.CopyFrom(retinue_data.ex_task());
	}

	for (int i = 0; i < retinue_data.active_chats_size(); i ++)
	{
		_active_chats.insert(retinue_data.active_chats(i));
	}

	for (int i = 0; i < retinue_data.trigger_events_size(); i ++)
	{
		_trigger_events.insert(retinue_data.trigger_events(i));
	}

	_next_ex_task_time = retinue_data.next_ex_task_time();
	_ex_task_id = retinue_data.ex_task_id();
	_recruit.pay_total = retinue_data.recruit_pay_total();
	_recruit.pay_last = retinue_data.recruit_pay_last();
	_ex_task_deadline = retinue_data.ex_task_deadline();
	_recruit.pay_today = retinue_data.recruit_pay_today();
	_recruit.pay_timestamp = retinue_data.recruit_pay_tm();
	_recruit.free_total = retinue_data.recruit_free_total();
	_recruit.pay_6_total = retinue_data.recruit_pay_6_total();
	_recruit_10_mask_old = retinue_data.recruit_10_mask();
	_recruit_10_mask_new = retinue_data.recruit_10_mask_new();
	_recruit_10_mask_2 = RECRUIT_MASK_BITSET(retinue_data.recruit_10_mask_2());

#ifdef USE_TENCENT
	FixOldRecruit(); //处理老数据
	_recruit_10_mask_new |= (_recruit_10_mask_old & 0x1c); //兼容第3 4 5位的老数据
#endif

	for (int status_id : retinue_data.active_ss_status())
	{
		_active_ss_status.insert(status_id);
	}

	for (int status_id : retinue_data.active_npc_ss_status())
	{
		_active_npc_ss_status.insert(status_id);
	}

	for (auto& status : retinue_data.finish_ss_status())
	{
		int status_id = status.status_id();
		_finish_status_map[status.moment_id()] = status;
		// 老数据兼容
		if (!status.has_gender())
		{
			_finish_status_map[status_id].set_gender(_imp->GetGender());
		}
		_finish_ss_status.push_back(&(_finish_status_map[status_id]));
		_finish_ss_status_ids.insert(status_id);
	}
	for (auto& status : retinue_data.cur_status())
	{
		_retinue_status[status.status_id()] = status;
		// 老数据兼容
		if (!status.has_gender())
		{
			_retinue_status[status.status_id()].set_gender(_imp->GetGender());
		}
		CalcStatusNotifyTime(_retinue_status[status.status_id()], _status_notify_time[status.status_id()]);
	}

	if (retinue_data.has_last_confirm_ss_notify_time())
	{
		last_ss_notify_confirm_time = retinue_data.last_confirm_ss_notify_time();
	}

	if (retinue_data.has_last_ss_notify_time())
	{
		last_ss_notify_time = retinue_data.last_ss_notify_time();
	}
	_special_pay_recruit_count = RETINUE_SPECIAL_PAY_RECRUIT_MIN + ZONE_INDEX(_imp->GetParent()->ID.id) % (RETINUE_SPECIAL_PAY_RECRUIT_MAX - RETINUE_SPECIAL_PAY_RECRUIT_MIN + 1);
	_special_free_recruit_count = RETINUE_SPECIAL_FREE_RECRUIT_MIN + ZONE_INDEX(_imp->GetParent()->ID.id) % (RETINUE_SPECIAL_FREE_RECRUIT_MAX - RETINUE_SPECIAL_FREE_RECRUIT_MIN + 1);
	_dirty = true;
	_special_pay2_recruit_count = RETINUE_SPECIAL_PAY2_RECRUIT_MIN + (_imp->GetParent()->ID.id >> 8) % (RETINUE_SPECIAL_PAY2_RECRUIT_MAX - RETINUE_SPECIAL_PAY2_RECRUIT_MIN + 1);

	for (auto& sr : retinue_data.special_retinues())
	{
		_special_retinues[sr.tid()] = sr.count();
	}

	UpdateStarCounter();
	UpdateMaxQuality(NULL);
	CalcNotifyTime();

	return true;
}

void player_retinue_manager::LoadSolution(PB::player_universal_data_t& pud)
{
	if (pud.has_retinue_solutions())
	{
		_retinue_solutions.CopyFrom(pud.retinue_solutions());
	}
	else
	{
		InitSolution();
	}
}

void player_retinue_manager::InitSolution()
{
	PB::retinue_solution_data retinue_solutions;
	retinue_solutions.set_cur_index(0);
	for (int solution_index = 0; solution_index < GLOBAL_CONFIG.partner_solution_max_num; ++solution_index)
	{
		auto solution_ptr = retinue_solutions.add_solutions();
		solution_ptr->set_index(solution_index);
		solution_ptr->set_state(PB::RETINUE_SOLUTION_STATE::RSS_LOCK);
		if (solution_index < GLOBAL_CONFIG.partner_solution_free_num)
		{
			solution_ptr->set_state(PB::RETINUE_SOLUTION_STATE::RSS_UNLOCK);
		}

		// 0号做主战
		for (int retinue_index = 0; retinue_index <= ASSIST_COMBAT_RETINUE_NUM; ++retinue_index)
		{
			solution_ptr->add_retinue_id(0);
			if (solution_index == retinue_solutions.cur_index())
			{
				auto iter_retinue = _combat_retinues.find(retinue_index);
				if (iter_retinue != _combat_retinues.end())
				{
					int retinue_id = iter_retinue->second._retinue->GetTID();
					solution_ptr->set_retinue_id(retinue_index, retinue_id);
				}
			}
		}
	}

	_retinue_solutions.CopyFrom(retinue_solutions);
}

void player_retinue_manager::Save(raw_wrapper& rw)
{
	//Heartbeat();

	PB::db_retinue_data retinue_data;
	for (auto it = _retinues.begin(); it != _retinues.end(); ++it)
	{
		PB::retinue_info *retinue = retinue_data.add_retinues();
		retinue->CopyFrom(it->second->GetData());
	}
	/*if (_combat_retinue)
	{
		retinue_data.set_select_id(_combat_retinue->GetTID());
	}*/

	for (int i = 0; i < ASSIST_COMBAT_RETINUE_NUM; ++i)
	{
		retinue_data.add_assist_combat_retinues(0);
	}

	for (auto& it : _combat_retinues)
	{
		if (it.second._retinue)
		{
			if (it.first == MAIN_COMBAT_RETINUE_SLOT)
			{
				retinue_data.set_select_id(it.second._retinue->GetTID());
			}
			else if (it.first <= ASSIST_COMBAT_RETINUE_NUM)
			{
				retinue_data.set_assist_combat_retinues(it.first - 1, it.second._retinue->GetTID());
			}
		}
	}

	for (auto it = _private_item_map.begin(); it != _private_item_map.end(); ++it)
	{
		if (it->first > 0 && it->second > 0)
		{
			auto item = retinue_data.add_privates();
			item->set_id(it->first);
			item->set_count(it->second);
		}
	}

	for (auto it = _gift_map.begin(); it != _gift_map.end(); ++it)
	{
		if (it->first > 0 && it->second > 0)
		{
			auto item = retinue_data.add_gifts();
			item->set_id(it->first);
			item->set_count(it->second);
		}
	}

	/*if (_formation_id)
	{
		retinue_data.mutable_formation()->set_formation_id(_formation_id);
		for (int i = 0; i < EXP_RETINUE_FORMATION_POS_COUNT_MAX; ++i)
		{
			retinue_data.mutable_formation()->add_retinue_id(_formation[i]);
		}
	}
	for (auto it = _owned_formations.begin(); it != _owned_formations.end(); ++it)
	{
		retinue_data.add_formation_owned(*it);
	}*/
	for (auto it = _retinue_tasks.begin(); it != _retinue_tasks.end(); ++it)
	{
		retinue_data.add_tasks()->CopyFrom(it->second);
	}

	for (auto it = _active_chats.begin(); it != _active_chats.end(); ++it)
	{
		retinue_data.add_active_chats(*it);
	}

	for (auto it = _trigger_events.begin(); it != _trigger_events.end(); ++it)
	{
		retinue_data.add_trigger_events(*it);
	}
	for (auto it = _retinue_friends.begin(); it != _retinue_friends.end(); ++it)
	{
		PB::db_retinue_friend_info *retinue = retinue_data.add_friends();
		retinue->CopyFrom(it->second->GetData());
	}

	retinue_data.mutable_ex_task()->CopyFrom(_ex_task);
	retinue_data.set_next_ex_task_time(_next_ex_task_time);
	retinue_data.set_ex_task_id(_ex_task_id);
	retinue_data.set_recruit_pay_total(_recruit.pay_total);
	retinue_data.set_recruit_pay_last(_recruit.pay_last);
	retinue_data.set_recruit_pay_today(_recruit.pay_today);
	retinue_data.set_recruit_pay_tm(_recruit.pay_timestamp);
	retinue_data.set_ex_task_deadline(_ex_task_deadline);
	retinue_data.set_recruit_free_total(_recruit.free_total);
	retinue_data.set_recruit_pay_6_total(_recruit.pay_6_total);
	retinue_data.set_recruit_10_mask(_recruit_10_mask_old);
	retinue_data.set_recruit_10_mask_new(_recruit_10_mask_new);
	retinue_data.set_recruit_10_mask_2(_recruit_10_mask_2.to_string());

	for (int status_id : _active_ss_status)
	{
		retinue_data.add_active_ss_status(status_id);
	}

	for (int status_id : _active_npc_ss_status)
	{
		retinue_data.add_active_npc_ss_status(status_id);
	}

	for (auto *pStatus : _finish_ss_status)
	{
		retinue_data.add_finish_ss_status()->CopyFrom(*pStatus);
	}

	for (auto& status_kv : _retinue_status)
	{
		retinue_data.add_cur_status()->CopyFrom(status_kv.second);
	}
	retinue_data.set_last_confirm_ss_notify_time(last_ss_notify_confirm_time);
	retinue_data.set_last_ss_notify_time(last_ss_notify_time);
	retinue_data.set_assist_combat_unlock_mask(_assist_combat_unlock_mask);

	for (auto& sr : _special_retinues)
	{
		if (sr.second <= 0)
		{
			continue;
		}
		auto *pSr = retinue_data.add_special_retinues();
		pSr->set_tid(sr.first);
		pSr->set_count(sr.second);
	}

	abase::octets os;
	os.__resize(retinue_data.ByteSize());
	retinue_data.SerializeWithCachedSizesToArray((unsigned char *)os.begin());
	rw.swap(os);
}

void player_retinue_manager::SaveSolution(PB::player_universal_data_t& pud) const
{
	pud.mutable_retinue_solutions()->CopyFrom(_retinue_solutions);
}

void player_retinue_manager::Heartbeat()
{
	ExTaskHeartBeat();

	int now = gmatrix::GetInstance().GetSysTime();
	if (_heartbeat_count++ % 60 == 0)
	{
		//刷新下伙伴的心情
		for (auto& it : _retinue_friends)
		{
			auto& data = it.second->GetData();
			if (data.mood_timestamp() <= now)
			{
				it.second->RefreshMood();
				//通知客户端
				NotifyRetinueInfo(NULL, PB::gp_retinue_data_notify::N_TYPE_MOOD_CHANGE, it.first, it.second->GetMood());
			}
		}
	}

	if (_amity_changed)
	{
		gplayer_imp *pImp = GetPlayerImp();
		if (pImp)
		{
			pImp->GetAchievement().OnRetinueAmityChange(pImp);
		}
		_amity_changed = false;
	}

	// 通知客户端红点
	if (last_ss_notify_time > last_ss_notify_confirm_time)
	{
		// 上次通知的，客户端还没有确认
		if (!notified_after_login)
		{
			//玩家下线又上线了，补发一次
			SendPlayerSsNotify();
		}
	}
	else
	{
		if (last_ss_need_notify_time > 0 && last_ss_need_notify_time <= now && last_ss_notify_time < last_ss_need_notify_time)
		{
			SendPlayerSsNotify();
		}
	}

	//__PRINTF("player_retinue_manager::Heartbeat %d, %d\n", _ready_casting, _skill_delay);
	if (!_ready_casting)
	{
		return;
	}

	if (--_skill_delay > 0)
	{
		return;
	}
	DoCastActiveSkill();

	_ready_casting = false;
	_skill_target.Clear();

	//计算下下次哪个伙伴 什么时候释放技能
	CalcSkillTime();
}

void player_retinue_manager::SendRetinue(player_retinue *pretinue)
{
	PB::gp_retinue_data_notify pb;
	pb.set_notify_type(PB::gp_retinue_data_notify::N_TYPE_SYNC_RETINUE_ONE);
	PB::retinue_client_info& retinue = *pb.add_retinues();
	pretinue->MakeClientData(retinue);
	_imp->Runner()->CommonSend<S2C::CMD::PBS2C>(pb);
}
void player_retinue_manager::SendRetinueList()
{
	PB::gp_retinue_data_notify pb;
	pb.set_notify_type(PB::gp_retinue_data_notify::N_TYPE_SYNC_RETINUE);
	for (auto it = _retinues.begin(); it != _retinues.end(); ++ it)
	{
		PB::retinue_client_info& retinue = *pb.add_retinues();
		it->second->MakeClientData(retinue);
	}
	for (auto& sr : _special_retinues)
	{
		PB::retinue_client_info *pRetinue = pb.add_retinues();
		pRetinue->mutable_base_info()->set_retinue_id(sr.first);
		pRetinue->mutable_base_info()->set_count(sr.second);
	}
	int day_begin = GetLocalDayBegin();
	for (auto it = _retinue_friends.begin(); it != _retinue_friends.end(); ++ it)
	{
		PB::retinue_friend_info *pfriend = pb.add_friends();
		//pfriend->CopyFrom(it->second->GetData());
		auto& data = it->second->GetData();
		pfriend->set_retinue_id(data.retinue_id());
		pfriend->set_amity(data.amity());
		pfriend->mutable_cur_chat()->CopyFrom(data.cur_chat());
		pfriend->set_mood(data.mood());
		pfriend->set_take_gift_level(data.take_gift_level());
		pfriend->mutable_unlock_diary()->CopyFrom(data.unlock_diary());
		if (data.active_gift_timestamp() < day_begin)
		{
			pfriend->set_active_gift_count_today(0);
		}
		else
		{
			pfriend->set_active_gift_count_today(data.active_gift_count_today());
		}
		pfriend->set_take_gift_mask(data.take_gift_mask());
	}
	for (int i = 0; i < ASSIST_COMBAT_RETINUE_NUM; ++i)
	{
		pb.add_assist_combat_retinue(0);
	}

	for (auto& it : _combat_retinues)
	{
		if (it.second._retinue)
		{
			if (it.first == MAIN_COMBAT_RETINUE_SLOT)
			{
				pb.set_param1(it.second._retinue->GetTID());
			}
			else if (it.first <= ASSIST_COMBAT_RETINUE_NUM)
			{
				pb.set_assist_combat_retinue(it.first - 1, it.second._retinue->GetTID());
			}
		}
	}

	/*if (_formation_id)
	{
		pb.mutable_formation()->set_formation_id(_formation_id);
		for (int i = 0; i < EXP_RETINUE_FORMATION_POS_COUNT_MAX; ++i)
		{
			pb.mutable_formation()->add_retinue_id(_formation[i]);
		}
	}
	for (auto it = _owned_formations.begin(); it != _owned_formations.end(); ++it)
	{
		pb.add_formation_owned(*it);
	}*/
	for (auto it = _retinue_tasks.begin(); it != _retinue_tasks.end(); ++it)
	{
		PB::retinue_task_info& info = *pb.add_task();
		info.CopyFrom(it->second);
	}

	for (auto it = _private_item_map.begin(); it != _private_item_map.end(); ++it)
	{
		PB::retinue_private_info& info = *pb.add_privates();
		info.set_id(it->first);
		info.set_count(it->second);
	}

	for (auto it = _gift_map.begin(); it != _gift_map.end(); ++it)
	{
		PB::retinue_gift_info& info = *pb.add_gifts();
		info.set_id(it->first);
		info.set_count(it->second);
	}

	int chat_count_max = GLOBAL_CONFIG.retinue_chat_active_count_max;
	//在此处检查下当前激活的对话数量 超过最大值时去掉优先级低与id大的
	if (_active_chats.size() > chat_count_max)
	{
		std::set<int64_t> tmp;
		//先按优先级排序
		for (int chat_id : _active_chats)
		{
			auto *pChatConfig = retinue_template_manager::GetInstance().GetRetinueChatConfig(chat_id);
			if (!pChatConfig)
			{
				continue;
			}
			tmp.insert(((int64_t)65535 - pChatConfig->priority) << 32 | chat_id);
		}

		//取靠前的chatid
		_active_chats.clear();
		int count = 0;
		for (auto it = tmp.begin(); it != tmp.end(); ++it)
		{
			if (++count > chat_count_max)
			{
				break;
			}
			_active_chats.insert((*it) & 0xFFFFFFFF);
		}
	}


	for (auto it = _active_chats.begin(); it != _active_chats.end(); ++it)
	{
		pb.add_active_chats(*it);
	}

	pb.mutable_ex_task()->CopyFrom(_ex_task);
	pb.set_param2(_ex_task_id);
	pb.set_timestamp(_ex_task_deadline);
	pb.set_recruit_10_count(_recruit_10_mask_new);
	pb.set_recruit_10_count_2(_recruit_10_mask_2.to_string());
	pb.set_assist_combat_unlock_mask(_assist_combat_unlock_mask);
	_imp->Runner()->CommonSend<S2C::CMD::PBS2C>(pb);
}

void player_retinue_manager::NotifyRetinueInfo(player_retinue *pretinue, PB::gp_retinue_data_notify_N_TYPE cmd, int param1, int param2, int param3)
{
	PB::gp_retinue_data_notify pb;
	pb.set_notify_type(cmd);
	switch (cmd)
	{
	/*case PB::gp_retinue_data_notify::N_TYPE_USE_FORMATION:
		pb.mutable_formation()->set_formation_id(_formation_id);
		for (int i = 0; i < EXP_RETINUE_FORMATION_POS_COUNT_MAX; ++i)
		{
			if (_formation[i])
			{
				pb.mutable_formation()->add_retinue_id(_formation[i]);
			}
		}
		pb.set_hidden_prop_actived(0);
		break;*/

	case PB::gp_retinue_data_notify::N_TYPE_ADD_RETINUE:
	case PB::gp_retinue_data_notify::N_TYPE_RETINUE_UP_QUALITY:
	case PB::gp_retinue_data_notify::N_TYPE_AWAKE:
	{
		if (pretinue)
		{
			PB::retinue_client_info *retinue_data = pb.add_retinues();
			pretinue->MakeClientData(*retinue_data);
			pb.set_param1(param1);
			pb.set_param2(param2);
		}
	}
	break;
	/*case PB::gp_retinue_data_notify::N_TYPE_ADD_FORMATION:
		pb.set_param1(param1);
		break;*/
	case PB::gp_retinue_data_notify::N_TYPE_CHAT_ACTIVE:
	case PB::gp_retinue_data_notify::N_TYPE_SS_ACTIVE:
	case PB::gp_retinue_data_notify::N_TYPE_UNLOCK_ASSIST_COMBAT:
	{
		pb.set_param1(param1);
	}
	break;
	case PB::gp_retinue_data_notify::N_TYPE_EX_TASK_REFRESH:
	{
		pb.set_param1(param1);
		pb.set_timestamp(param2);
	}
	break;
	case PB::gp_retinue_data_notify::N_TYPE_ADD_PRIVATE:
	case PB::gp_retinue_data_notify::N_TYPE_GIFT_ADD:
	case PB::gp_retinue_data_notify::N_TYPE_TASK_SPEAK:
	{
		pb.set_param1(param1);
		pb.set_param2(param2);
	}
	break;
	case PB::gp_retinue_data_notify::N_TYPE_ACTIVE_PRIVATE:
	{
		if (pretinue)
		{
			PB::retinue_client_info *retinue_data = pb.add_retinues();
			pretinue->MakeClientData(*retinue_data);
			pb.set_param1(param1);
			pb.set_param2(param2);
		}
	}
	break;
	case PB::gp_retinue_data_notify::N_TYPE_GAIN_EXP:
	case PB::gp_retinue_data_notify::N_TYPE_DECOMPOSE:
	{
		if (!pretinue)
		{
			break;
		}
		pb.set_retinue_id(pretinue->GetTID());
		pb.set_param1(param1);
	}
	break;

	case PB::gp_retinue_data_notify::N_TYPE_SELECT_FASHION:
	{
		if (!pretinue)
		{
			break;
		}
		pb.set_retinue_id(pretinue->GetTID());
		pb.set_param1(param1);
		pb.set_param2(param2);
	}
	break;
	case PB::gp_retinue_data_notify::N_TYPE_MOOD_CHANGE:
	case PB::gp_retinue_data_notify::N_TYPE_AMITY_CHANGE:
	case PB::gp_retinue_data_notify::N_TYPE_TAKE_GIFT:
	case PB::gp_retinue_data_notify::N_TYPE_FRIEND:
	case PB::gp_retinue_data_notify::N_TYPE_SELECT_COMBAT:
	{
		pb.set_retinue_id(param1);
		pb.set_param1(param2);
	}
	break;
	case PB::gp_retinue_data_notify::N_TYPE_RECRUIT:
	case PB::gp_retinue_data_notify::N_TYPE_CHAT:
	case PB::gp_retinue_data_notify::N_TYPE_CHAT_FINISH:
	case PB::gp_retinue_data_notify::N_TYPE_GIVE_GIFT:
	case PB::gp_retinue_data_notify::N_TYPE_UNLOCK_DIARY:
	{
		pb.set_retinue_id(param1);
		pb.set_param1(param2);
		pb.set_param2(param3);
	}
	break;
	case PB::gp_retinue_data_notify::N_TYPE_ADD_SPECIAL:
	case PB::gp_retinue_data_notify::N_TYPE_SPECIAL_DECOMPOSE:
	{
		PB::retinue_client_info *retinue_data = pb.add_retinues();
		retinue_data->mutable_base_info()->set_retinue_id(param1);
		retinue_data->mutable_base_info()->set_count(param2);
		pb.set_retinue_id(param1);
		pb.set_param1(param2);
	}
	break;

	default:
		break;
	};
	_imp->Runner()->CommonSend<S2C::CMD::PBS2C>(pb);
}

void player_retinue_manager::SendRetinueSsStatus()
{
	SendRetinueSsStatusList();
	for (int status_id : _active_npc_ss_status)
	{
		NotifyNewNpcSsStatus(status_id);
	}
}

void player_retinue_manager::SendRetinueSsStatusList()
{
	gplayer_imp *pImp = GetPlayerImp();
	if (pImp)
	{
		PB::gp_ss_status_notify pb;
		for (int status_id : _active_ss_status)
		{
			pb.add_status_id(status_id);
		}
		pb.set_is_confirmed(pImp->GetSwitch(SERVER_SWITCH_TYPE, SERVER_SWITCH_INDEX_RETINUE_ACTIVE_SSS_CONFIRMED));
		_imp->Runner()->CommonSend<S2C::CMD::PBS2C>(pb);
	}
}

void player_retinue_manager::AddRetinueSsStatus(int status_id)
{
	auto pCfg = retinue_template_manager::GetInstance().GetRetinueSssConfig(status_id);
	if (pCfg)
	{
		if (pCfg->partner_id == 0)
		{
			gplayer_imp *pImp = GetPlayerImp();
			if (pImp)
			{
				auto result = _active_ss_status.insert(status_id);
				if (result.second)
				{
					pImp->SetServerSwitch(SERVER_SWITCH_INDEX_RETINUE_ACTIVE_SSS_CONFIRMED, false);
					SendRetinueSsStatusList();
				}
			}
		}
		else
		{
			auto result = _active_npc_ss_status.insert(status_id);
			if (result.second)
			{
				NotifyNewNpcSsStatus(status_id);
			}
		}
	}
}

void player_retinue_manager::ConsumeSsStatus(int status_id)
{
	auto pCfg = retinue_template_manager::GetInstance().GetRetinueSssConfig(status_id);
	if (pCfg)
	{
		if (pCfg->partner_id == 0)
		{
			if (_active_ss_status.erase(status_id))
			{
				SendRetinueSsStatusList();
			}
		}
		else
		{
			_active_npc_ss_status.erase(status_id);
		}
	}
}

void player_retinue_manager::NotifyNewNpcSsStatus(int status_id)
{
	PB::gp_npc_ss_status_notify pb;
	pb.set_status_id(status_id);
	_imp->Runner()->CommonSend<S2C::CMD::PBS2C>(pb);
}

bool player_retinue_manager::StatusActived(int status_id)
{
	auto CheckSet = [status_id](const std::unordered_set<int>& status_set) -> bool
	{
		return status_set.find(status_id) != status_set.end();
	};
	return CheckSet(_active_ss_status) || CheckSet(_active_npc_ss_status);
}

bool player_retinue_manager::StatusInUse(int status_id)
{
	return _retinue_status.find(status_id) != _retinue_status.end();
}

void player_retinue_manager::OnPlayerReconnect()
{
	// 断线重连要重新推送一次朋友圈红点
	notified_after_login = false;
}

void player_retinue_manager::OnPlayerLogin()
{
	// 修复辅战位意外解锁的问题
	for (int slot_idx = 5; slot_idx <= 6; ++slot_idx)
	{
		if ((_assist_combat_unlock_mask & (1 << (slot_idx - 1))) == 0)
		{
			continue;
		}
		if (SeniorTowerRewardManager::GetInstance().CanUnlockRetinueSecondaryAssistCombatSlot(_imp, slot_idx))
		{
			continue;
		}

		SLOG(WARNING, "GS::player_retinue_manager::OnPlayerLogin: slot unlock wrong")
		.P("roleid", _imp->Parent()->ID.id)
		.P("slot_idx", slot_idx)
		.PX("unlock_mask", _assist_combat_unlock_mask);

		if (GetCombatRetinue(slot_idx))
		{
			RemoveCombatRetinue(slot_idx);
		}
		_assist_combat_unlock_mask &= ~(1 << (slot_idx - 1));
	}

	//激活所有属性
	for (auto it = _retinues.begin(); it != _retinues.end(); ++ it)
	{
		auto retinue = it->second;
		retinue->RebuildProp(_imp);
		retinue->Attach(_imp);
	}

	for (auto& it : _combat_retinues)
	{
		if (it.second._retinue)
		{
			it.second._retinue->ActiveSkill(_imp);
		}
	}

	/*if (_formation_id)
	{
		AddFormationPropToCreatue();
	}*/
	//SyncSolution2Client();
}
/*int player_retinue_manager::PlayerUnUseFormation()
{
	if (_formation_id == 0)
	{
		return -1;
	}
	NotifyRetinueInfo(NULL, PB::gp_retinue_data_notify::N_TYPE_UNUSE_FORMATION);
	_dirty = true;
	_formation_id = 0;
	memset(_formation, 0, sizeof(_formation));
	__PRINTF("player unuse retinue formation, roleid=%ld\n", _imp->GetParent()->ID.id);
	return 0;
}

int player_retinue_manager::PlayerAddOwnedFormation(const retinue_formation_template *pformation)
{
	if (pformation->level_need > _imp->GetLevel())
	{
		__PRINTF("AddOwnedFormation, level error! roleid=%ld, formationid=%d, level=%d, required_level=%d\n"
		         , _imp->Parent()->ID.id, pformation->formation_id, _imp->GetLevel(), pformation->level_need);
		return S2C::ERR_RETINUE_LEVEL_LIMIT;
	}
	int power = 0;
	for (auto it = _retinues.begin(); it != _retinues.end(); ++it)
	{
		power += it->second->_power;
	}

	if (power < pformation->retinue_power_need)
	{
		__PRINTF("AddOwnedFormation, power not enough! roleid=%ld, formationid=%d, power=%d, required_power=%d\n"
		         , _imp->Parent()->ID.id, pformation->formation_id, power, pformation->retinue_power_need);
		return S2C::ERR_RETINUE_POWER_LIMIT;
	}
	__AddFormation(pformation->formation_id);

	return 0;
}

bool player_retinue_manager::CheckFormation(const retinue_formation_template *pformation, const PB::retinue_formation_info& formation)
{
	if (formation.retinue_id_size() != pformation->retinue_count)
	{
		__PRINTF("CheckFormation, retinue count not match! roleid=%ld, formationid=%d, retinue_count=%d, required_retinue_count=%d\n"
		         , _imp->Parent()->ID.id, pformation->formation_id, formation.retinue_id_size(), pformation->retinue_count);
		return false;
	}

	abase::static_set<int> retinues;
	retinues.reserve(pformation->retinue_count);
	for (auto i = 0; i < pformation->retinue_count; i ++)
	{
		int retinue_id = formation.retinue_id(i);
		player_retinue *retinue = GetRetinue(retinue_id);
		//if (!retinue || retinue->GetTaskID()) 不检查出阵随从是否在任务状态
		if (!retinue)
		{
			__PRINTF("CheckFormation, retinue not exist or has other job! roleid=%ld, formationid=%d, retinue_id=%d\n"
			         , _imp->Parent()->ID.id, pformation->formation_id, retinue_id);
			return false;
		}
		retinues.insert(formation.retinue_id(i));
	}
	if ((int)retinues.size() != pformation->retinue_count)
	{
		__PRINTF("CheckFormation, retinue count error! roleid=%ld, formationid=%d, retinue_count=%d, required_retinue_count=%d\n"
		         , _imp->Parent()->ID.id, pformation->formation_id, formation.retinue_id_size(), pformation->retinue_count);
		return false;
	}
	return true;
}
void player_retinue_manager::__AddFormation(int formation_id)
{
	_owned_formations.insert(formation_id);
	GLog::formatlog("add_retinue_formation", "roleid=%ld:formationid=%d", _imp->Parent()->ID.id, formation_id);
}
int player_retinue_manager::TaskAddFormation(int formation_id)
{
	//功能先关闭，需要的话再打开
	return -1;

	if (IsOwnedFormation(formation_id))
	{
		return -1;
	}

	auto pformation = retinue_template_manager::GetInstance().GetFormationTemplate(formation_id);
	if (!pformation)
	{
		return -2;
	}

	__AddFormation(formation_id);

	NotifyRetinueInfo(NULL, PB::gp_retinue_data_notify::N_TYPE_ADD_FORMATION, formation_id);

	return 0;
}
int player_retinue_manager::PlayerUseFormation(const PB::retinue_formation_info& formation)
{
	int formation_id = formation.formation_id();
	auto pformation = retinue_template_manager::GetInstance().GetFormationTemplate(formation_id);
	if (!pformation)
	{
		return -1;
	}
	if (!IsOwnedFormation(formation_id))
	{
		int retcode = PlayerAddOwnedFormation(pformation);
		if (retcode)
		{
			return retcode;
		}
	}
	if (!CheckFormation(pformation, formation))
	{
		return -2;
	}

	if (_formation_id)
	{
		PlayerUnUseFormation();
	}
	_formation_id = formation_id;
	memset(_formation, 0, sizeof(_formation));
	for (auto i = 0; i < pformation->retinue_count; i ++)
	{
		_formation[i] = formation.retinue_id(i);
	}
	NotifyRetinueInfo(NULL, PB::gp_retinue_data_notify::N_TYPE_USE_FORMATION);
	_dirty = true;

	_imp->GetAchievement().OnActiveFormation(_imp, formation_id);
	__PRINTF("player use retinue formation, roleid=%ld, formationid=%d(%d:%d:%d:%d:%d)\n"
	         , _imp->GetParent()->ID.id, _formation_id, _formation[0], _formation[1], _formation[2], _formation[3], _formation[4]);
	return 0;
}*/
void player_retinue_manager::OnPlayerLevelUp(int level)
{
	//功能先关闭，需要的话再打开
	return;

	for (auto it = _retinues.begin(); it != _retinues.end(); ++it)
	{
		player_retinue *pretinue =  it->second;

		pretinue->OnPlayerLevelUp(_imp, level);
	}
	_dirty = true;
}

void player_retinue_manager::HandleOperation(const PB::gp_retinue_operate& op)
{
	gplayer_imp *pImp = GetPlayerImp();
	if (!pImp)
	{
		return;
	}

	switch (op.oper())
	{
	case PB::gp_retinue_operate::OT_UPGRADE_QUALITY:
	{
		CHECK_FUNC_SWITCH_AND_NOTIFY(kFuncCodeRetinue, pImp);
		CHECK_FUNC_SWITCH_AND_NOTIFY(kFuncCodeRetinueUpgrade, pImp);

		if (_imp->IsRoamIn() && !GLOBAL_CONFIG.IsNormalCenterZone(GNET::g_zoneid))
		{
			__PRINTF("GS:roleid="   FMT_I64":跨服禁止随从升级\n", _imp->Parent()->ID.id);
			return;
		}
		int retinue_id = op.param1();
		player_retinue *pretinue = GetRetinue(retinue_id);
		if (!pretinue)
		{
			return;
		}

		if (pretinue->CombatSlot() >= 0)
		{
			pretinue->DeactiveSkill(_imp);
		}

		if (pretinue->GetQuality() != op.param2()) //防止客户端多次点击 重复发送
		{
			return;
		}

		int ret = pretinue->UpgradeQuality(pImp);
		if (!ret)
		{
			pImp->GetAchievement().OnCommonFightingCapacityUpdate(pImp, 2, _total_attach_fightcapacity);
			NotifyRetinueInfo(pretinue, PB::gp_retinue_data_notify::N_TYPE_RETINUE_UP_QUALITY);
		}
		else
		{
			_imp->error_message(1, 1, ret);
		}
		if (pretinue->CombatSlot() >= 0)
		{
			pretinue->ActiveSkill(_imp);
		}
	}
	break;

	/*case PB::gp_retinue_operate::OT_USE_FORMATION:
	{
		const PB::retinue_formation_info& formation = op.formation();
		int ret = PlayerUseFormation(formation);
		if (ret > 0)
		{
			_imp->error_message(1, 1, ret);
		}
	}
	break;*/


	/*case PB::gp_retinue_operate::OT_UNUSE_FORMATION:
	{
		int ret = PlayerUnUseFormation();
		if (ret > 0)
		{
			_imp->error_message(1, 1, ret);
		}
	}
	break;*/

	case PB::gp_retinue_operate::OT_ADD_TASK:
	{
		CHECK_FUNC_SWITCH_AND_NOTIFY(kFuncCodeRetinue, pImp);
		CHECK_FUNC_SWITCH_AND_NOTIFY(kFuncCodeRetinueTaskAdd, pImp);
		if (_imp->IsRoamIn() && !GLOBAL_CONFIG.IsNormalCenterZone(GNET::g_zoneid))
		{
			__PRINTF("GS:roleid="   FMT_I64":跨服禁止伙伴接取任务\n", _imp->Parent()->ID.id);
			return;
		}
		const PB::retinue_task_info& task = op.task();
		int ret = AddTask(task);
		if (ret > 0)
		{
			_imp->error_message(1, 1, ret);
		}
	}
	break;

	case PB::gp_retinue_operate::OT_RECEIVE_TASK_AWARD:
	{
		CHECK_FUNC_SWITCH_AND_NOTIFY(kFuncCodeRetinue, pImp);
		CHECK_FUNC_SWITCH_AND_NOTIFY(kFuncCodeRetinueTaskAward, pImp);
		if (_imp->IsRoamIn() && !GLOBAL_CONFIG.IsNormalCenterZone(GNET::g_zoneid))
		{
			__PRINTF("GS:roleid="   FMT_I64":跨服禁止伙伴任务领奖\n", _imp->Parent()->ID.id);
			return;
		}
		int taskid = op.param1();
		int ret = AcceptTaskAward(taskid);
		if (ret > 0)
		{
			_imp->error_message(1, 1, ret);
		}
	}
	break;

	case PB::gp_retinue_operate::OT_COMBAT_SELECT:
	{
		CHECK_FUNC_SWITCH_AND_NOTIFY(kFuncCodeRetinue, pImp);
		CHECK_FUNC_SWITCH_AND_NOTIFY(kFuncCodeRetinueSelect, pImp);
		int retinue_id = op.retinue_id();
		int index = op.param1();
		int ret = SelectCombatRetinue(retinue_id, index);
		if (ret != 0)
		{
			_imp->error_message(1, 1, ret);
		}
	}
	break;

	case PB::gp_retinue_operate::OT_DECOMPOSE:
	{
		if (_imp->IsRoamIn() && !GLOBAL_CONFIG.IsNormalCenterZone(GNET::g_zoneid))
		{
			__PRINTF("GS:roleid="   FMT_I64":跨服禁止伙伴拆分\n", _imp->Parent()->ID.id);
			return;
		}
		CHECK_FUNC_SWITCH_AND_NOTIFY(kFuncCodeRetinue, pImp);
		CHECK_FUNC_SWITCH_AND_NOTIFY(kFuncCodeRetinueDecompose, pImp);
		for (int i = 0; i < op.decompose_size(); i++)
		{
			auto& decompose = op.decompose(i);
			if (decompose.retinue_id() > 0 && decompose.count() > 0)
			{
				int ret = DecomposeRetinue(decompose.retinue_id(), decompose.count());
				if (ret != 0)
				{
					_imp->error_message(1, 1, ret);
					return;
				}
			}
		}
	}
	break;

	case PB::gp_retinue_operate::OT_ACTIVE_PRIVATE:
	{
		int retinue_id = op.retinue_id();
		int item_id = op.param1();
		CHECK_FUNC_SWITCH_AND_NOTIFY(kFuncCodeRetinue, pImp);
		CHECK_FUNC_SWITCH_AND_NOTIFY(kFuncCodeRetinuePrivate, pImp);
		int ret = ActiveRetinuePrivateItem(retinue_id, item_id);
		if (ret != 0)
		{
			_imp->error_message(1, 1, ret);
		}
	}
	break;

	case PB::gp_retinue_operate::OT_FASHION_SELECT:
	{
		CHECK_FUNC_SWITCH_AND_NOTIFY(kFuncCodeRetinueFashion, pImp);
		int retinue_id = op.retinue_id();
		int fashion_index = op.param1();
		int color_index = op.param2();
		auto retinue = GetRetinue(retinue_id);
		if (!retinue)
		{
			_imp->error_message(1, 1, -1);
			return;
		}

		int ret = retinue->SelectFashion(pImp, fashion_index, color_index);
		if (ret != 0)
		{
			_imp->error_message(1, 1, ret);
			return;
		}
		NotifyRetinueInfo(retinue, PB::gp_retinue_data_notify::N_TYPE_SELECT_FASHION, fashion_index, color_index);
	}
	break;

	case PB::gp_retinue_operate::OT_FASHION_ACTIVE:
	{
		CHECK_FUNC_SWITCH_AND_NOTIFY(kFuncCodeRetinueFashion, pImp);
		int retinue_id = op.retinue_id();
		int fashion_index = op.param1();
		int color_index = op.param2();
		auto retinue = GetRetinue(retinue_id);
		if (!retinue)
		{
			_imp->error_message(1, 1, -1);
			return;
		}

		int ret = retinue->ActiveFashionColor(pImp, fashion_index, color_index);
		if (ret != 0)
		{
			_imp->error_message(1, 1, ret);
			return;
		}
		pImp->GetAchievement().OnCommonFightingCapacityUpdate(pImp, 2, _total_attach_fightcapacity);
		SendRetinue(retinue);
	}
	break;

	case PB::gp_retinue_operate::OT_RECRUIT:
	{
		CHECK_FUNC_SWITCH_AND_NOTIFY(kFuncCodeRetinueRecruit, pImp);
		int pool_id = op.param1();
		int is_ten = op.param2() == 1;
		int ret = RecruitRetinue(pool_id, is_ten);
		if (ret != 0)
		{
			_imp->error_message(1, 1, ret);
			return;
		}
	}
	break;

	case PB::gp_retinue_operate::OT_TAKE_GIFT:
	{
		int ret = RecieveGift(op.retinue_id(), op.param1());
		if (ret != 0)
		{
			_imp->error_message(1, 1, ret);
			return;
		}
	}
	break;

	case PB::gp_retinue_operate::OT_SEND_GIFT:
	{
		int retinue_id = op.retinue_id();
		int gift_id = op.param1();
		//CHECK_FUNC_SWITCH_AND_NOTIFY(kFuncCodeRetinuePrivate, pImp);
		int ret = ActiveRetinueGift(retinue_id, gift_id);
		if (ret != 0)
		{
			_imp->error_message(1, 1, ret);
		}
	}
	break;

	case PB::gp_retinue_operate::OT_CHAT:
	{
		CHECK_FUNC_SWITCH_AND_NOTIFY(kFuncCodeRetinueChat, pImp);
		int retinue_id = op.retinue_id();
		int chat_id = op.param1();
		int msg_id = op.param2();
		auto retinue_friend = GetRetinueFriend(retinue_id);
		if (!retinue_friend)
		{
			//_imp->error_message(1, 1, -1);
			return;
		}

		int old_amity = retinue_friend->GetAmity();
		int ret = retinue_friend->Chat(pImp, chat_id, msg_id);
		if (ret != 0)
		{
			_imp->error_message(1, 1, ret);
			return;
		}

		int new_amity = retinue_friend->GetAmity();
		if (old_amity != new_amity)
		{
			NotifyRetinueInfo(NULL, PB::gp_retinue_data_notify::N_TYPE_AMITY_CHANGE, retinue_id, new_amity);
		}
	}
	break;

	case PB::gp_retinue_operate::OT_EVENT_TRIGGER:
	{
		int event_type = op.param1();
		int tid = op.param2();
		EventTrigger(event_type, tid);
	}
	break;

	case PB::gp_retinue_operate::OT_CHAT_RECORD:
	{
		int retinue_id = op.retinue_id();
		int chat_id = op.param1();
		SendChatRecord(retinue_id, chat_id);
	}
	break;
	case PB::gp_retinue_operate::OT_UNLOCK_ASSIST_COMBAT:
	{
		int ret = UnlockAssistCombatSlot(op.param1());
		if (ret != 0)
		{
			_imp->error_message(1, 1, ret);
			return;
		}
	}
	case PB::gp_retinue_operate::OT_AWAKE:
	{
		CHECK_FUNC_SWITCH_AND_NOTIFY(kFuncCodeRetinueAwake, pImp);
		int retinue_id = op.retinue_id();
		auto retinue = GetRetinue(retinue_id);
		if (!retinue)
		{
			_imp->error_message(1, 1, -1);
			return;
		}
		bool awake_active = retinue->IsAwake();
		int ret = retinue->Awake(pImp);
		if (ret != 0)
		{
			_imp->error_message(1, 1, ret);
			return;
		}
		if (!awake_active)
		{
			_awake_retinues.insert(retinue_id);
		}
		pImp->GetAchievement().OnCommonFightingCapacityUpdate(pImp, 2, _total_attach_fightcapacity);
		NotifyRetinueInfo(retinue, PB::gp_retinue_data_notify::N_TYPE_AWAKE);
	}
	break;
	case PB::gp_retinue_operate::OT_SOLUTION_UNLOCK:
	case PB::gp_retinue_operate::OT_SOLUTION_SWITCH:
	case PB::gp_retinue_operate::OT_SOLUTION_SAVE:
	case PB::gp_retinue_operate::OT_SOLUTION_REQ_DATA:
	{
		RetinueSolutionOp(op);
	}
	break;

	default:
		break;
	}
}

gplayer_imp *player_retinue_manager::GetPlayerImp()
{
	if (_imp->IsPlayerClass())
	{
		return (gplayer_imp *)_imp;
	}
	return NULL;
}

void player_retinue_manager::ExTaskHeartBeat()
{
	int now = gmatrix::GetInstance().GetSysTime();
	if (_ex_task.taskid() != 0)
	{
		return;
	}

	if (_ex_task_id > 0 &&  now > _ex_task_deadline)
	{
		//紧急任务过了有效期 重新计算下下次紧急任务出现时间
		__PRINTF("player_retinue_manager::ExTask Deadline %d\n", _ex_task_id);
		_ex_task_id = 0;
		NotifyRetinueInfo(NULL, PB::gp_retinue_data_notify::N_TYPE_EX_TASK_REFRESH, 0);
		_next_ex_task_time = now + abase::Rand(RETINUE_EX_TASK_NEXT_MIN, RETINUE_EX_TASK_NEXT_MAX);
	}

	if (now < _next_ex_task_time || _ex_task_id > 0)
	{
		return;
	}

	gplayer_imp *pImp = GetPlayerImp();
	if (!pImp)
	{
		return;
	}

	//刷新紧急任务
	int taskid = retinue_template_manager::GetInstance().RandSelectExTask(pImp);
	if (taskid > 0)
	{
		auto pConfig = retinue_template_manager::GetInstance().GetRetinueExTaskTemplate(taskid);
		if (!pConfig)
		{
			return;
		}
		_ex_task_id = taskid;
		_ex_task_deadline = now + pConfig->deadline;
		//通知客户端
		NotifyRetinueInfo(NULL, PB::gp_retinue_data_notify::N_TYPE_EX_TASK_REFRESH, taskid, _ex_task_deadline);
		__PRINTF("player_retinue_manager::ExTaskRefresh %d deadline %d\n", taskid, _ex_task_deadline);
	}
}

int player_retinue_manager::GetRetinueQuality(int tid)
{
	const player_retinue *pretinue = GetRetinue(tid);
	if (pretinue)
	{
		return pretinue->GetQuality();
	}
	return 0;
}
#define MAX_RETINUE_TASK_COUNT 4
int player_retinue_manager::AddTask(const PB::retinue_task_info& req)
{
	gplayer_imp *pImp = GetPlayerImp();
	if (!pImp)
	{
		return S2C::ERR_SERVICE_UNAVILABLE;
	}

	bool is_ex_task = false;
	if (req.taskid() == _ex_task_id)
	{
		//是紧急任务
		if (_ex_task.taskid() != 0)
		{
			__PRINTF("AddRetinueTask already ex task! roleid=%ld cur_task=%d\n", _imp->Parent()->ID.id, _ex_task.taskid());
			return S2C::ERR_RETINUE_TASK_ALREADY_LIMIT;
		}

		const auto *ptask = retinue_template_manager::GetInstance().GetRetinueExTaskTemplate(req.taskid());
		std::set<int> retinue_set; //防止id重复
		for (int id : req.retinue_id())
		{
			retinue_set.insert(id);
		}
		if (!ptask || retinue_set.size() != req.retinue_id_size() || ptask->max_retinue != req.retinue_id_size())
		{
			__PRINTF("AddRetinueTask retinue count not match! roleid=%ld, taskid=%d, retinue_count=%d\n", _imp->Parent()->ID.id, req.taskid(), req.retinue_id_size());
			return S2C::ERR_RETINUE_TASK_NUM_NOT_MATCH;
		}
		is_ex_task = true;
	}
	else
	{
		if (_retinue_tasks.size() >= MAX_RETINUE_TASK_COUNT)
		{
			__PRINTF("AddRetinueTask, too much task! roleid=%ld, task_sized=%d\n", _imp->Parent()->ID.id, (int)_retinue_tasks.size());
			return S2C::ERR_RETINUE_TASK_ALREADY_LIMIT;
		}
		auto it = _retinue_tasks.find(req.taskid());
		if (it != _retinue_tasks.end())
		{
			__PRINTF("AddRetinueTask, task exist! roleid=%ld, taskid=%d\n", _imp->Parent()->ID.id, req.taskid());
			return S2C::ERR_RETINUE_TASK_NOT_EXIST;
		}
		/*const retinue_formation_template* pformation = NULL;
		  if (_formation_id)
		  {
		  pformation = retinue_template_manager::GetInstance().GetFormationTemplate(_formation_id);
		  }*/
		const retinue_task *ptask = retinue_template_manager::GetInstance().GetRetinueTaskTemplate(req.taskid());
		std::set<int> retinue_set; //防止id重复
		for (int id : req.retinue_id())
		{
			retinue_set.insert(id);
		}
		if (!ptask || retinue_set.size() != req.retinue_id_size() || ptask->max_retinue != req.retinue_id_size())
		{
			__PRINTF("AddRetinueTask, retinue count not match! roleid=%ld, taskid=%d, retinue_count=%d\n", _imp->Parent()->ID.id, req.taskid(), req.retinue_id_size());
			return S2C::ERR_RETINUE_TASK_NUM_NOT_MATCH;
		}
	}
	PB::retinue_task_info info;
	info.set_taskid(req.taskid());
	info.set_timestamp(gmatrix::GetInstance().GetSysTime());

	std::vector<player_retinue *> retinues;
	for (auto i = 0; i < req.retinue_id_size(); i ++)
	{
		int retinue_id = req.retinue_id(i);
		player_retinue *retinue = GetRetinue(retinue_id);
		if (!retinue)
		{
			__PRINTF("AddRetinueTask, retinue not exist! roleid=%ld, retinue_id=%d\n", _imp->Parent()->ID.id, retinue_id);
			return -4;
		}
		if (retinue->GetTaskID())
		{
			__PRINTF("AddRetinueTask, retinue is in job! roleid=%ld, retinue_id=%d, job=%d\n", _imp->Parent()->ID.id, retinue_id, retinue->GetTaskID());
			return S2C::ERR_RETINUE_HAS_TASK;
		}

		//if (pformation) //不检查接任务随从是否在出阵
		//{
		//	for (int i = 0; i < pformation->retinue_count; i ++)
		//	{
		//		if (_formation[i] == retinue_id)
		//		{
		//			__PRINTF("AddRetinueTask, retinue is in formation! roleid=%ld, retinue_id=%d, formationid=%d\n"
		//					, _imp->Parent()->ID.id, retinue_id, _formation_id);
		//			return S2C::ERR_RETINUE_IN_FORMATION;
		//		}
		//	}
		//}
		info.add_retinue_id(retinue_id);
		retinues.push_back(retinue);
	}

	bool rslt = (*pImp->GetTaskGuard())->OnTaskRetinueTrig(req.taskid(), is_ex_task);
	if (!rslt)
	{
		return S2C::ERR_RETINUE_TASK_FAILED;
	}

	std::stringstream ss;

	typedef std::vector<int> PROP_VEC;
	std::vector<PROP_VEC> retinue_props;
	for (auto rit = retinues.begin(); rit != retinues.end(); ++rit)
	{
		(*rit)->SetTaskID(req.taskid());

		retinue_props.push_back(PROP_VEC());
		PROP_VEC& one_retinue_prop = *retinue_props.rbegin();
		one_retinue_prop.push_back((*rit)->GetTID());                   //id
		for (int i = 0; i < RETINUE_STATS_COUNT; ++i)                   //feature
		{
			one_retinue_prop.push_back((*rit)->GetTemplate()->feature[i]);
		}

		if (rit != retinues.begin())
		{
			ss << ":";
		}
		ss << (*rit)->GetTID();
	}

	//预生成奖励
	int task_id = req.taskid();
	gmatrix::ScriptKeeper keeper(LUA_ENV_RETINUE);
	lua_State *L = keeper.GetState();
	if (!L)
	{
		return -5;
	}
	LuaWrapper lw(L);
	LuaParameter params(task_id);
	for (auto pit = retinue_props.begin(); pit != retinue_props.end(); ++pit)
	{
		params.AddParameter(*pit);
	}

	if (!lw.gExec("CheckTaskAwardInterface", params))
	{
		GLog::log(LOG_ERR, "CheckTaskAwardInterface failed. %s, task_id=%d\n", lw.ErrorMsg(), task_id);
		return S2C::ERR_RETINUE_TASK_AWARD_FAIL;
	}
	if (!lua_isnumber(L, -1) || !lua_isnumber(L, -2) )
	{
		GLog::log(LOG_ERR, "CheckTaskAwardInterface result failed. taskid=%d\n", task_id);
		return S2C::ERR_RETINUE_TASK_AWARD_FAIL;
	}

	int succ = LUA_TOINTEGER(L, -2);
	int grade = LUA_TOINTEGER(L, -1);
	if (succ != 1)
	{
		return S2C::ERR_RETINUE_TASK_AWARD_FAIL;
	}
	info.set_grade(grade);

	if (is_ex_task)
	{
		_ex_task.Clear();
		_ex_task.CopyFrom(info);
		_ex_task_id = 0; //清掉能接的紧急任务
	}
	else
	{
		_retinue_tasks[req.taskid()].CopyFrom(info);
	}

	PB::gp_retinue_data_notify pb;
	pb.set_notify_type(PB::gp_retinue_data_notify::N_TYPE_ADD_TASK);
	if (is_ex_task)
	{
		pb.mutable_ex_task()->CopyFrom(info);
	}
	else
	{
		pb.add_task()->CopyFrom(info);
	}
	_imp->Runner()->CommonSend<S2C::CMD::PBS2C>(pb);
	__PRINTF("player_retinue_manager::AddTask roleid=%ld:taskid=%d:retinue=(%s)\n", _imp->Parent()->ID.id, req.taskid(), ss.str().c_str());
	GLog::formatlog("retinue_task", "roleid=%ld:taskid=%d:retinue=(%s)", _imp->Parent()->ID.id, req.taskid(), ss.str().c_str());
	return 0;
}
int player_retinue_manager::AcceptTaskAward(int task_id)
{
	gplayer_imp *pImp = GetPlayerImp();
	if (!pImp)
	{
		return S2C::ERR_SERVICE_UNAVILABLE;
	}

	bool is_ex_task = false;
	PB::retinue_task_info *pTask = NULL;
	if (_ex_task.taskid() == task_id)
	{
		is_ex_task = true;
		pTask = &_ex_task;
	}
	else
	{
		auto it = _retinue_tasks.find(task_id);
		if (it == _retinue_tasks.end())
		{
			__PRINTF("AcceptRetinueTaskAward, task not exist! roleid=%ld, taskid=%d\n", _imp->Parent()->ID.id, task_id);
			return S2C::ERR_RETINUE_TASK_NOT_EXIST;
		}
		pTask = &(it->second);
	}

	std::vector<player_retinue *> retinues;

	for (int i = 0; i < pTask->retinue_id_size(); i++)
	{
		player_retinue *retinue = GetRetinue(pTask->retinue_id(i));
		if (retinue == NULL)
		{
			continue;
		}
		retinues.push_back(retinue);
	}

	//取任务奖励数据
	int award_grade = pTask->grade();
	gmatrix::ScriptKeeper keeper(LUA_ENV_RETINUE);
	lua_State *L = keeper.GetState();
	if (!L)
	{
		return S2C::ERR_SERVICE_UNAVILABLE;
	}
	LuaWrapper lw(L);
	LuaParameter params(task_id, award_grade);

	if (!lw.gExec("GetTaskAwardInterface", params))
	{
		GLog::log(LOG_ERR, "GetTaskAwardInterface failed. %s, task_id=%d\n", lw.ErrorMsg(), task_id);
		return S2C::ERR_RETINUE_TASK_AWARD_FAIL;
	}
	if (!lua_isnumber(L, -1) || !lua_isnumber(L, -2) || !lua_isnumber(L, -3) || !lua_isnumber(L, -4))
	{
		GLog::log(LOG_ERR, "GetTaskAwardInterface result failed. taskid=%d\n", task_id);
		return S2C::ERR_RETINUE_TASK_AWARD_FAIL;
	}

	std::vector<int> award_ids; //获得的奖励模板id

	int private_item = LUA_TOINTEGER(L, -2);
	int has_special = LUA_TOINTEGER(L, -1);

	int retinue_exp = LUA_TOINTEGER(L, -3);
	int award_count = LUA_TOINTEGER(L, -4);

	if (award_count > 0)
	{
		for (int i = 0; i < award_count; ++i)
		{
			if (!lua_isnumber(L, -5 - i))
			{
				GLog::log(LOG_ERR, "GetTaskAwardInterface result failed. taskid=%d\n", task_id);
				return S2C::ERR_RETINUE_TASK_AWARD_FAIL;
			}
			int award_id = LUA_TOINTEGER(L, -5 - i);
			award_ids.push_back(award_id);
		}
	}

	if (has_special == 1) //获得了专属物品
	{
		//找一个伙伴还没获得的专属私有物
		std::vector<int> items;
		for (auto rit = retinues.begin(); rit != retinues.end(); ++rit)
		{
			(*rit)->GetAvailablePrivateItem(0, items);
		}
		//如果没找到，随机稀有的
		if (items.empty())
		{
			LuaParameter params2(task_id, award_grade);
			if (!lw.gExec("GetTaskAwardRandPrivateItem", params))
			{
				GLog::log(LOG_ERR, "GetTaskAwardRandPrivateItem failed. %s, task_id=%d\n", lw.ErrorMsg(), task_id);
				return S2C::ERR_RETINUE_TASK_AWARD_FAIL;
			}
			if (!lua_isnumber(L, -1))
			{
				GLog::log(LOG_ERR, "GetTaskAwardRandPrivateItem result failed. taskid=%d\n", task_id);
				return S2C::ERR_RETINUE_TASK_AWARD_FAIL;
			}

			private_item = LUA_TOINTEGER(L, -1);
		}
		else
		{
			int rand = abase::Rand(0, items.size() - 1);
			private_item = items[rand];
		}
	}

	bool rslt = (*pImp->GetTaskGuard())->OnTaskCheckRetinueAward(task_id);
	if (!rslt)
	{
		return S2C::ERR_RETINUE_RECV_TASK_AWARD_FAILED;
	}

	//发奖
	inc_exp_info info;
	info.type = INC_TYPE_TASK;
	info.param1 = task_id;
	for ( auto award : award_ids )
	{
		pImp->DeliverGeneralReward({kFuncCodeRetinueTaskAward, task_id}, GRANT_REWARD_TYPE_TASK, award, NULL, 0);
	}
	if (retinue_exp > 0)
	{
		for (auto rit = retinues.begin(); rit != retinues.end(); ++rit)
		{
			int ret = RetinueGainExp((*rit)->GetTID(), retinue_exp, info);
			if (ret != 0)
			{
				pImp->error_message(1, 1, ret);
			}
		}
	}
	AddPrivateItem(private_item, 1);
	for (auto rit = retinues.begin(); rit != retinues.end(); ++rit)
	{
		(*rit)->SetTaskID(0);
	}

	if (is_ex_task)
	{
		_ex_task.Clear();
		_ex_task_id = 0;
		_next_ex_task_time = gmatrix::GetInstance().GetSysTime() + abase::Rand(RETINUE_EX_TASK_NEXT_MIN, RETINUE_EX_TASK_NEXT_MAX); /*3天 ±8小时*/
	}
	else
	{
		_retinue_tasks.erase(task_id);
	}

	PB::gp_retinue_data_notify pb;
	pb.set_notify_type(PB::gp_retinue_data_notify::N_TYPE_DEL_TASK);
	pb.set_param1(task_id);
	pb.mutable_award()->set_taskid(task_id);
	//pb.mutable_award()->set_success(succ);
	pb.mutable_award()->add_private_item(private_item);
	pb.mutable_award()->set_retinue_exp(retinue_exp);
	for (int i = 0; i < award_ids.size(); ++i)
	{
		pb.mutable_award()->add_awardid(award_ids[i]);
	}
	_imp->Runner()->QuietSend<S2C::CMD::PBS2C>(pb);
	__PRINTF("player_retinue_manager::TaskAward roleid=%ld:taskid=%d:grade=%d:awardid=%d\n", _imp->Parent()->ID.id, task_id, award_grade, (int)award_ids.size());
	GLog::formatlog("retinue_recv_task_award", "roleid=%ld:taskid=%d:grade=%d:awardid=%d", _imp->Parent()->ID.id, task_id, award_grade, (int)award_ids.size());
	return 0;
}


int player_retinue_manager::RetinueGainExp(int retinue_id, int& exp_add, const inc_exp_info& info)
{
	if (!GET_FUNC_SWITCH(kFuncCodeRetinue))
	{
		return S2C::ERR_FUNC_CLOSED_TEMPORARY;
	}
	if (!GET_FUNC_SWITCH(kFuncCodeRetinueExp))
	{
		return S2C::ERR_FUNC_CLOSED_TEMPORARY;
	}

	if (_imp->IsRoamIn() && !GLOBAL_CONFIG.IsNormalCenterZone(GNET::g_zoneid))
	{
		__PRINTF("GS:roleid="   FMT_I64":跨服禁止伙伴获得经验\n", _imp->Parent()->ID.id);
		return S2C::ERR_SERVICE_UNAVILABLE;;
	}

	player_retinue *retinue = GetRetinue(retinue_id);
	if (!retinue)
	{
		return -1;
	}

	int exp_left = exp_add;
	int cur_level = retinue->GetLevel();
	int cur_exp = retinue->GetExp();

	int level_limit = std::min(retinue->GetTemplate()->level_limit, retinue_template_manager::GetInstance().GetRetinueLevelLimit(_imp->GetLevel()));
	if (cur_level >= level_limit)
	{
		return S2C::ERR_RETINUE_LEVEL_LIMIT;
	}

	while (true)
	{
		int lvl_exp = player_template::GetInstance().GetRetinueLvlupExp(cur_level);
		if (lvl_exp < 1)
		{
			lvl_exp = 1;
		}
		if (cur_level >= level_limit)
		{
			break;
		}
		int exp_cost = lvl_exp - cur_exp;
		if (exp_cost <= exp_left)
		{
			cur_level += 1;
			cur_exp = 0;
			exp_left -= exp_cost;
		}
		else
		{
			cur_exp += exp_left;
			exp_left = 0;
			break;
		}
	}
	exp_add -= exp_left;
	if (exp_add <= 0)
	{
		return -1;
	}
	bool levelup = retinue->GetLevel() < cur_level;
	retinue->SetExp(cur_exp);
	retinue->SetLevel(cur_level);
	if (levelup)
	{
		retinue->Dettach(_imp);
		retinue->RebuildProp(_imp);
		retinue->Attach(_imp);
		SendRetinue(retinue);
		GLog::formatlog("retinue_levelup", "rolelid=%ld:tid=%d:level:%d", _imp->GetParent()->ID.id, retinue_id, cur_level);

		gplayer_imp *pImp = GetPlayerImp();
		if (pImp)
		{
			pImp->GetAchievement().OnRetinueLevelChange(pImp);
			pImp->GetAchievement().OnCommonFightingCapacityUpdate(pImp, 2, _total_attach_fightcapacity);
		}
	}
	else
	{
		SendRetinue(retinue);
		NotifyRetinueInfo(retinue, PB::gp_retinue_data_notify::N_TYPE_GAIN_EXP, cur_exp);
	}
	// for BI
	BI_LOG_GLOBAL(GetPlayerImp()->GetParent()->account.ToStr());
	SLOG(FORMAT, "retinue_exp")
	.BI_HEADER2_GS(GetPlayerImp())
	.P("retinue_id", retinue_id)
	.P("reason", info.type)
	.P("param1", info.param1)
	.P("param2", info.param2)
	.P("new_level", cur_level)
	.P("new_exp", cur_exp);
	return 0;
}

int player_retinue_manager::RemoveCombatRetinue(int index)
{
	auto *slot = GetCombatSlot(index);
	if (!slot)
	{
		return -1;
	}

	auto retinue = slot->_retinue;
	if (!retinue)
	{
		return -2;
	}
	retinue->DeactiveSkill(_imp);
	retinue->ClearCombatSlot();
	if (index > 0)
	{
		auto main_combat = GetCombatRetinue(MAIN_COMBAT_RETINUE_SLOT);
		if (main_combat)
		{
			main_combat->OnAssistRetinueChange(_imp, index, retinue->GetTID(), 0);
		}
	}

	_combat_retinues.erase(index);

	//if (index == MAIN_COMBAT_RETINUE_SLOT)
	{
		retinue->Dettach(_imp);
		retinue->RebuildProp(_imp);
		retinue->Attach(_imp);
		SendRetinue(retinue);
	}

	NotifyRetinueInfo(NULL, PB::gp_retinue_data_notify::N_TYPE_SELECT_COMBAT, 0, index);

	__PRINTF("player_retinue_manager::RemoveCombatRetinue roleid=%ld index=%d retinue=%d\n", _imp->GetParent()->ID.id, index, retinue->GetTID());
	return 0;
}

int player_retinue_manager::SelectCombatRetinue(int retinue_id, int index)
{
	//先检查index是否合法
	if (index < 0 || index > ASSIST_COMBAT_RETINUE_NUM)
	{
		return -1;
	}
	if (index > 0 && !(_assist_combat_unlock_mask & 1 << (index - 1)))
	{
		return -2;
	}

	//战斗状态下不能更换
	if (GetNextSkillTime() > 0)
	{
		return S2C::ERR_RETINUE_SELECT_COMBAT;
	}

	if (retinue_id <= 0)
	{
		//把出战的伙伴收回去
		return RemoveCombatRetinue(index);
	}

	player_retinue *retinue = GetRetinue(retinue_id);
	if (!retinue)
	{
		return -1;
	}

	bool need_sync = false;
	for (auto& r : _combat_retinues)
	{
		if (r.second._retinue && r.second._retinue->GetTID() == retinue_id)
		{
			if (r.first == index)
			{
				return -2;
			}
			// 更新方案并同步方案
			int cur_solution_index = _retinue_solutions.cur_index();
			if (cur_solution_index < _retinue_solutions.solutions_size())
			{
				auto solution_ptr = _retinue_solutions.mutable_solutions(cur_solution_index);
				if (r.first < solution_ptr->retinue_id_size() && solution_ptr->retinue_id(r.first) != 0)
				{
					solution_ptr->set_retinue_id(r.first, 0);
					need_sync = true;
				}
			}

			//如果要选择的伙伴已经是出战状态 先去掉此伙伴
			RemoveCombatRetinue(r.first);
			break;
		}
	}

	int old_retinue_id = 0;
	auto old_retinue = _combat_retinues.find(index);
	if (old_retinue != _combat_retinues.end())
	{
		old_retinue_id = old_retinue->second._retinue->GetTID();
		RemoveCombatRetinue(index);
	}

	/*if (_combat_retinue)
	{
		//去掉之前出站的伙伴支援技能
		_combat_retinue->DeactiveSkill(_imp);
		old_retinue_id = _combat_retinue->GetTID();
	}

	_combat_retinue = retinue;*/

	//刷新下属性 主要是为了刷新_combat_prop
	_combat_retinues.insert(std::make_pair(index, RETINUE_SLOT_INFO(_imp, retinue)));
	retinue->SetCombatSlot(index);
	retinue->Dettach(_imp);
	retinue->RebuildProp(_imp);
	retinue->Attach(_imp);

	//激活支援技能
	retinue->ActiveSkill(_imp);

	auto *main_retinue = GetCombatRetinue(MAIN_COMBAT_RETINUE_SLOT);
	if (main_retinue)
	{
		//如果主战对此辅战有羁绊关系 属性值会有变化
		main_retinue->OnAssistRetinueChange(_imp, index, old_retinue_id, retinue_id);
	}

	gplayer_imp *pImp = GetPlayerImp();
	if (pImp)
	{
		pImp->GetAchievement().OnCommonFightingCapacityUpdate(pImp, 2, _total_attach_fightcapacity);
	}

	//通知客户端
	NotifyRetinueInfo(NULL, PB::gp_retinue_data_notify::N_TYPE_SELECT_COMBAT, retinue_id, index);

	if (index == MAIN_COMBAT_RETINUE_SLOT)
	{
		SendRetinue(retinue);
	}

	// for BI
	BI_LOG_GLOBAL(GetPlayerImp()->GetParent()->account.ToStr());
	SLOG(FORMAT, "select_comabt_retinue")
	.BI_HEADER2_GS(GetPlayerImp())
	.P("retinue_id", retinue_id)
	.P("index", index)
	.P("old_retinue_id", old_retinue_id);


	// 更新方案并同步方案
	int cur_solution_index = _retinue_solutions.cur_index();
	if (cur_solution_index < _retinue_solutions.solutions_size())
	{
		auto solution_ptr = _retinue_solutions.mutable_solutions(cur_solution_index);
		if (need_sync || (index < solution_ptr->retinue_id_size() && solution_ptr->retinue_id(index) != retinue_id))
		{
			solution_ptr->set_retinue_id(index, retinue_id);
			// 同步方案
			SyncSolution2Client();
		}
	}
	return 0;
}

int player_retinue_manager::UnlockAssistCombatSlot(int slot_index)
{
	if (slot_index <= 0 || slot_index > ASSIST_COMBAT_RETINUE_NUM)
	{
		return -1;
	}
	if ((_assist_combat_unlock_mask & 1 << (slot_index - 1)) > 0)
	{
		//已经解锁
		return -2;
	}
	int unlock_limit = retinue_template_manager::GetInstance().AssistCombatSlotUnlockLimit(slot_index);
	if (unlock_limit < 0 || unlock_limit > GetSize())
	{
		return -3;
	}

	_assist_combat_unlock_mask |= 1 << (slot_index - 1);

	NotifyRetinueInfo(NULL, PB::gp_retinue_data_notify::N_TYPE_UNLOCK_ASSIST_COMBAT, slot_index);
	LOG_TRACE("player_retinue_manager::UnlockAssistCombatSlot roleid=%ld index=%d", _imp->GetParent()->ID.id, slot_index);
	return 0;
}

int player_retinue_manager::UnlockSecondaryAssistCombatSlot(int slot_index)
{
	if (slot_index <= 0 || slot_index > ASSIST_COMBAT_RETINUE_NUM)
	{
		return -1;
	}
	if ((_assist_combat_unlock_mask & 1 << (slot_index - 1)) > 0)
	{
		//已经解锁
		return -2;
	}
	if (!SeniorTowerRewardManager::GetInstance().CanUnlockRetinueSecondaryAssistCombatSlot(_imp, slot_index))
	{
		return -3;
	}

	_assist_combat_unlock_mask |= 1 << (slot_index - 1);

	NotifyRetinueInfo(NULL, PB::gp_retinue_data_notify::N_TYPE_UNLOCK_ASSIST_COMBAT, slot_index);
	LOG_TRACE("player_retinue_manager::UnlockSecondaryAssistCombatSlot roleid=%ld index=%d", _imp->GetParent()->ID.id, slot_index);
	return 0;
}

int player_retinue_manager::AddPrivateItem(int item_id, int count)
{
	gplayer_imp *pImp = GetPlayerImp();
	if (!pImp)
	{
		return S2C::ERR_SERVICE_UNAVILABLE;
	}
	CHECK_FUNC_SWITCH_AND_NOTIFY_RETURN_INT(kFuncCodeRetinue, pImp, S2C::ERR_SERVICE_UNAVILABLE);
	CHECK_FUNC_SWITCH_AND_NOTIFY_RETURN_INT(kFuncCodeRetinuePrivate, pImp, S2C::ERR_SERVICE_UNAVILABLE);

	const retinue_private_item_template *pTemplate = retinue_template_manager::GetInstance().GetRetinuePrivateItem(item_id);
	if (!pTemplate)
	{
		return S2C::ERR_SERVICE_UNAVILABLE;
	}

	_private_item_map[item_id] += count;

	int total_count = _private_item_map[item_id];
	NotifyRetinueInfo(NULL, PB::gp_retinue_data_notify::N_TYPE_ADD_PRIVATE, item_id, total_count);
	__PRINTF("player_retinue_manager::AddPrivateItem rolelid=%ld:tid=%d:add:%dtotal=%d\n", _imp->GetParent()->ID.id, item_id, count, total_count);
	GLog::formatlog("add_retinue_private", "rolelid=%ld:tid=%d:add:%d:total=%d", _imp->GetParent()->ID.id, item_id, count, total_count);
	return 0;
}

int player_retinue_manager::ActiveRetinuePrivateItem(int retinue_id, int item_id)
{

	gplayer_imp *pImp = GetPlayerImp();
	if (!pImp)
	{
		return S2C::ERR_SERVICE_UNAVILABLE;
	}

	if (_imp->IsRoamIn() && !GLOBAL_CONFIG.IsNormalCenterZone(GNET::g_zoneid))
	{
		__PRINTF("GS:roleid="   FMT_I64":跨服禁止私有物操作\n", _imp->Parent()->ID.id);
		return S2C::ERR_SERVICE_UNAVILABLE;;
	}

	auto retinue = GetRetinue(retinue_id);
	if (!retinue)
	{
		return -1;
	}

	auto it = _private_item_map.find(item_id);
	if (it == _private_item_map.end() || it->second < 1)
	{
		return S2C::ERR_RETINUE_PRIVATE_LESS;
	}

	int ret = retinue->ActivePrivateItem(pImp, item_id);
	pImp->GetAchievement().OnCommonFightingCapacityUpdate(pImp, 2, _total_attach_fightcapacity);

	if (!ret)
	{
		_private_item_map[item_id] -= 1;
		NotifyRetinueInfo(retinue, PB::gp_retinue_data_notify::N_TYPE_ACTIVE_PRIVATE, item_id, _private_item_map[item_id]);
	}

	LOG_TRACE("player_retinue_manager::ActiveRetinuePrivateItem retinue_id=%d:item=%d:ret=%d", retinue_id, item_id, ret);
	return ret;
}

//给伙伴赠送礼物
int player_retinue_manager::ActiveRetinueGift(int retinue_id, int gift_id)
{
	gplayer_imp *pImp = GetPlayerImp();
	if (!pImp)
	{
		return S2C::ERR_SERVICE_UNAVILABLE;
	}

	CHECK_FUNC_SWITCH_AND_RETURN_INT(kFuncCodeRetinueGift, pImp, S2C::ERR_SERVICE_UNAVILABLE);

	if (_imp->IsRoamIn() && !GLOBAL_CONFIG.IsNormalCenterZone(GNET::g_zoneid))
	{
		__PRINTF("GS:roleid="   FMT_I64":跨服禁止礼物操作\n", _imp->Parent()->ID.id);
		return S2C::ERR_SERVICE_UNAVILABLE;;
	}

	if (!pImp->GetCommonUseLimit().TestUseLimit(RETINUE_GIFT_LIMIT_ID))
	{
		return S2C::ERR_ITEM_CAN_NOT_USE;
	}

	auto retinue = GetRetinueFriend(retinue_id);
	if (!retinue)
	{
		return -1;
	}

	auto it = _gift_map.find(gift_id);
	if (it == _gift_map.end() || it->second < 1)
	{
		return S2C::ERR_RETINUE_GIFT_LESS;
	}

	auto& data = retinue->GetData();
	int count_today = data.active_gift_count_today();
	if (data.active_gift_timestamp() < (int)GetLocalDayBegin())
	{
		count_today = 0;
	}
	if (count_today >= RETINUE_ACTIVE_GIFT_LIMIT)
	{
		return S2C::ERR_ITEM_CAN_NOT_USE;
	}

	/*if (!retinue->GetTemplate()->can_be_friend)
	{
		//只有能加好友的才能送礼物
		return S2C::ERR_RETINUE_DONT_NEED_GIFT;
	}*/

	int amity_base_add = retinue_template_manager::GetInstance().GetRetinueGiftConfig(gift_id);
	if (amity_base_add < 0)
	{
		return -3;
	}

	int amity_gift_add = 0;
	if (!retinue_template_manager::GetInstance().GetRetinueGiftAmity(retinue_id, gift_id, retinue->GetMood(), amity_gift_add))
	{
		return -4;
	}

	int amity_add = amity_base_add + amity_gift_add;
	retinue->AddAmity(amity_add);

	pImp->GetCommonUseLimit().AddUseLimit(pImp, RETINUE_GIFT_LIMIT_ID);
	data.set_active_gift_count_today(count_today + 1);
	data.set_active_gift_timestamp(gmatrix::GetInstance().GetSysTime());

	int gift_left = it->second - 1;
	if (gift_left <= 0)
	{
		_gift_map.erase(gift_id);
	}
	else
	{
		it->second = gift_left;
	}
	NotifyRetinueInfo(NULL, PB::gp_retinue_data_notify::N_TYPE_GIVE_GIFT, retinue_id, gift_id, retinue->GetAmity());

	pImp->GetAchievement().OnRetinueGift(pImp, retinue_id, gift_id);
	InteractLog(retinue_id, IL_TYPE_GIFT, amity_add);

	LOG_TRACE("player_retinue_manager::ActiveRetinueGift retinue_id=%d:item=%d:amity_add=%d", retinue_id, gift_id, amity_add);
	return 0;
}

int player_retinue_manager::DecomposeRetinue(int retinue_id, int count)
{
	gplayer_imp *pImp =  GetPlayerImp();
	if (!pImp)
	{
		return S2C::ERR_SERVICE_UNAVILABLE;
	}
	if (!GET_FUNC_SWITCH(kFuncCodeRetinueDecompose))
	{
		return S2C::ERR_SERVICE_UNAVILABLE;
	}
	if (count <= 0)
	{
		return -1;
	}

	const retinue_template *pTemplate = retinue_template_manager::GetInstance().GetRetinueTemplate(retinue_id);
	if (!pTemplate)
	{
		return S2C::ERR_SERVICE_UNAVILABLE;
	}

	//检查是否有足够的空间
	std::vector<gen_item_t> gens;
	std::stringstream ss;
	for (int i = 0; i < RETINUE_DECOMPOSE_COUNT ; ++i)
	{
		auto& reward = pTemplate->decompose_item[i];
		if (reward.item_id > 0 && reward.item_count > 0)
		{
			gens.emplace_back();
			gens.back().tid = reward.item_id;
			gens.back().count = reward.item_count * count;
			ss << reward.item_id << "," << reward.item_count *count << ",";
		}
	}
	if (pImp->GetBackpackEmptySlot() < gens.size())
	{
		return S2C::ERR_INVENTORY_IS_FULL;
	}

	if (pTemplate->is_special)
	{
		int cur_count = _special_retinues[retinue_id];
		if (cur_count < count)
		{
			return S2C::ERR_RETINUE_COMPOSE_LESS;
		}
		cur_count -= count;
		_special_retinues[retinue_id] = cur_count;

		FuncInfo func_info{kFuncCodeRetinueDecompose, retinue_id};
		if (!pImp->GenAndIncItems(IGT_RETINUE_DECOMPOSE, func_info, gens))
		{
			return S2C::ERR_FATAL_ERR;
		}
		if (pTemplate->decompose_repu_value > 0)
		{
			pImp->ModifyReputation(func_info, pTemplate->decompose_repu_id, pTemplate->decompose_repu_value * count);
			ss << "[" << GNET::REPUID_RETINUE_SOUL << "," << pTemplate->decompose_repu_value *count << "," << ss.str() << "]";
		}
		NotifyRetinueInfo(NULL, PB::gp_retinue_data_notify::N_TYPE_SPECIAL_DECOMPOSE, retinue_id, cur_count);
	}
	else
	{
		auto retinue = GetRetinue(retinue_id);
		if (!retinue)
		{
			return -1;
		}

		if (retinue->GetCount() <= count)
		{
			return S2C::ERR_RETINUE_COMPOSE_LESS;
		}

		retinue->SetCount(retinue->GetCount() - count);
		FuncInfo func_info{kFuncCodeRetinueDecompose, retinue_id};
		if (!pImp->GenAndIncItems(IGT_RETINUE_DECOMPOSE, func_info, gens))
		{
			return S2C::ERR_FATAL_ERR;
		}
		if (retinue->GetTemplate()->decompose_repu_value > 0)
		{
			pImp->ModifyReputation(func_info, retinue->GetTemplate()->decompose_repu_id, retinue->GetTemplate()->decompose_repu_value * count);
			ss << "[" << GNET::REPUID_RETINUE_SOUL << "," << retinue->GetTemplate()->decompose_repu_value *count << "," << ss.str() << "]";
		}
		NotifyRetinueInfo(retinue, PB::gp_retinue_data_notify::N_TYPE_DECOMPOSE, retinue->GetCount());
	}
	// for BI
	BI_LOG_GLOBAL(pImp->GetParent()->account.ToStr());
	SLOG(FORMAT, "decompose_retinue")
	.BI_HEADER2_GS(pImp)
	.P("retinue_id", retinue_id)
	.P("count", count)
	.P("result_str", ss.str());
	return 0;
}

int player_retinue_manager::RecieveGift(int retinue_id, int gift_level)
{
	gplayer_imp *pImp =  GetPlayerImp();
	if (!pImp)
	{
		return S2C::ERR_SERVICE_UNAVILABLE;
	}
	CHECK_FUNC_SWITCH_AND_RETURN_INT(kFuncCodeRetinueGift, pImp, S2C::ERR_SERVICE_UNAVILABLE);
	auto retinue = GetRetinueFriend(retinue_id);
	if (!retinue)
	{
		return -1;
	}

	SLOG(DEBUG, "player_retinue_manager::RecieveGift").P("roleid", _imp->GetParent()->ID.id).PS(retinue_id).PS(gift_level).P("mask", retinue->GetData().take_gift_mask());

	if (gift_level < 1 || gift_level > 32)
	{
		//只能领取下一级的奖励
		return S2C::ERR_RETINUE_TAKE_GIFT_LEVEL_ERR;
	}
	const auto *pConfigMap = retinue_template_manager::GetInstance().GetRecieveGiftConfig(retinue_id);
	if (!pConfigMap)
	{
		return -3;
	}
	auto it = pConfigMap->find(gift_level);
	if (it == pConfigMap->end())
	{
		return S2C::ERR_RETINUE_TAKE_GIFT_LEVEL_ERR;
	}

	int gift_mask = retinue->GetData().take_gift_mask();
	if (gift_mask & (1 << (gift_level - 1)))
	{
		return S2C::ERR_RETINUE_TAKE_GIFT_LEVEL_ERR;
	}

	auto& cfg = it->second;
	if (cfg.amity_need > retinue->GetAmity())
	{
		return S2C::ERR_RETINUE_TAKE_GIFT_AMITY_LESS;
	}

	std::stringstream ss;
	if (cfg.items.size() > 0)
	{
		if (pImp->GetBackpackEmptySlot() < cfg.items.size())
		{
			return S2C::ERR_INVENTORY_IS_FULL;
		}
		for (auto& item : cfg.items)
		{
			gen_item_t gen;
			gen.tid = item.item_id;
			gen.count = item.count;
			pImp->GenAndIncItem(IGT_RETINUE_TAKE_GIFT, {kFuncCodeRetinueGift, 0}, gen);
			ss << item.item_id << "-" << item.count << ";";
		}
	}

	gift_mask |= (1 << (gift_level - 1));
	//retinue->GetData().set_take_gift_level(gift_level);
	retinue->GetData().set_take_gift_mask(gift_mask);

	NotifyRetinueInfo(NULL, PB::gp_retinue_data_notify::N_TYPE_TAKE_GIFT, retinue_id, gift_mask);
	GLog::formatlog("retinue_take_gift", "roleid=%ld:tid=%d:gift=%d:item_id:%s", _imp->GetParent()->ID.id, retinue->GetTID(), gift_level, ss.str().c_str());
	return 0;
}

void player_retinue_manager::EventTrigger(int event_type, int tid)
{
	__PRINTF("player_retinue_manager::EventTrigger Begin type=%d:tid=%d\n", event_type, tid);
	//如果是任务或者成就先检查前提条件是否满足
	auto *pImp = GetPlayerImp();
	if (!pImp)
	{
		return;
	}

	if (event_type == RE_TASK_ACCEPT)
	{
		if (!(*pImp->GetTaskGuard())->HasTask(tid))
		{
			pImp->DebugUTF8Say((std::string("伙伴事件触发失败:任务未接取 ") + std::to_string(tid)).c_str());
			__PRINTF("player_retinue_manager::EventTrigger failed (task accept) tid=%d\n", tid);
			return;
		}
	}
	else if (event_type == RE_TASK_FINISH)
	{
		if (!(*(pImp->GetTaskGuard()))->HaveFinishedTask(tid))
		{
			pImp->DebugUTF8Say((std::string("伙伴事件触发失败:任务未完成 ") + std::to_string(tid)).c_str());
			__PRINTF("player_retinue_manager::EventTrigger failed (task finish) tid=%d\n", tid);
			return;
		}
	}
	else if (event_type == RE_ACHIEVEMENT)
	{
		if (!pImp->HasAchievement(tid))
		{
			pImp->DebugUTF8Say((std::string("伙伴事件触发失败:成就未完成 ") + std::to_string(tid)).c_str());
			__PRINTF("player_retinue_manager::EventTrigger failed (achievement) tid=%d\n", tid);
			return;
		}
	}

	auto *pConfig = retinue_template_manager::GetInstance().GetRetinueEventConfig(event_type, tid);
	if (!pConfig)
	{
		return;
	}

	if (pConfig->diary_retinue > 0 && pConfig->unlock_diary > 0)
	{
		//日记解锁只需要判断是否有伙伴
		auto *pFriend = GetRetinueFriend(pConfig->diary_retinue);
		if (pFriend && !pFriend->IsDiaryUnlock(pConfig->unlock_diary))
		{
			int now = gmatrix::GetInstance().GetSysTime();
			pFriend->UnlockDiary(pConfig->unlock_diary, now);
			//通知客户端结果
			NotifyRetinueInfo(NULL, PB::gp_retinue_data_notify::N_TYPE_UNLOCK_DIARY, pConfig->diary_retinue, pConfig->unlock_diary, now);
			LOG_TRACE("player_retinue_manager::EventTrigger UnlockDairy retinue_id=%d:diary=%d", pConfig->diary_retinue, pConfig->unlock_diary);
		}
	}

	//检查配置的事件触发条件
	if (!pConfig->condition.Check(pImp))
	{
		pImp->DebugUTF8Say((std::string("伙伴事件触发失败:触发条件不满足 ") + std::to_string(tid)).c_str());
		__PRINTF("player_retinue_manager::EventTrigger failed 触发条件不满足 tid=%d\n", tid);
		return;
	}

	//如果是固定事件 检查是否触发过
	int event_key = GET_RETINUE_EVENT_KEY(event_type, tid);
	if (pConfig->group_id == 0 && IsEventTrigger(event_key))
	{
		pImp->DebugUTF8Say((std::string("伙伴事件触发失败:固定事件已触发过 ") + std::to_string(tid)).c_str());
		__PRINTF("player_retinue_manager::EventTrigger failed 固定事件已触发过 tid=%d\n", tid);
		return;
	}

	//如果是随机事件 检查是否在触发cd中
	const retinue_event_group *pGroupConfig = NULL;
	if (pConfig->group_id > 0)
	{
		pGroupConfig = retinue_template_manager::GetInstance().GetEventGroupConfig(pConfig->group_id);
		if (!pGroupConfig)
		{
			return;
		}
		if (!pGroupConfig->CheckLevel(pImp->GetLevel()))
		{
			pImp->DebugUTF8Say((std::string("伙伴事件触发失败:随机事件等级不满足 ") + std::to_string(tid)).c_str());
			__PRINTF("player_retinue_manager::EventTrigger failed 随机事件等级不满足 tid=%d\n", tid);
			return;
		}
		if (pGroupConfig->cooldown_id > 0 && !pImp->TestCoolDown(pGroupConfig->cooldown_id))
		{
			pImp->DebugUTF8Say((std::string("伙伴事件触发失败:随机事件冷却中 ") + std::to_string(tid)).c_str());
			__PRINTF("player_retinue_manager::EventTrigger failed 随机事件冷却中 tid=%d\n", tid);
			return;
		}
	}

	//触发概率判定
	bool suc = false;
	if (abase::RandUniform() < pConfig->ratio)
	{
		int trigger_chat = 0;
		int trigger_ss = 0;
		retinue_template_manager::GetInstance().GetEventTriggerResult(pImp, pConfig, trigger_chat, trigger_ss);
		if (trigger_chat > 0)
		{
			AddActiveChat(trigger_chat);
			NotifyRetinueInfo(NULL, PB::gp_retinue_data_notify::N_TYPE_CHAT_ACTIVE, trigger_chat);
			suc = true;
		}
		if (trigger_ss > 0)
		{
			AddRetinueSsStatus(trigger_ss );
			suc = true;
		}
		if (suc)
		{
			SLOG(FORMAT, "retinue_trigger_event").P("role", _imp->Parent()->ID.id).P("chat", trigger_chat).P("ss", trigger_ss);
		}
	}
	else
	{
		pImp->DebugUTF8Say((std::string("伙伴事件触发失败:触发几率判定失败 ") + std::to_string(tid)).c_str());
		__PRINTF("player_retinue_manager::EventTrigger failed 触发几率判定失败 tid=%d\n", tid);
	}

	//成功失败都要置cd
	if (pGroupConfig && pGroupConfig->cooldown_id > 0)
	{
		int cd_time = 0;
		if (suc)
		{
			cd_time = abase::Rand(pGroupConfig->success_cd - pGroupConfig->success_cd_range, pGroupConfig->success_cd + pGroupConfig->success_cd_range);
		}
		else
		{
			cd_time = abase::Rand(pGroupConfig->fail_cd - pGroupConfig->fail_cd_range, pGroupConfig->fail_cd + pGroupConfig->fail_cd_range);
		}
		pImp->SetCoolDown(pGroupConfig->cooldown_id, cd_time * 1000);
	}

	if (suc && pConfig->group_id == 0)
	{
		AddTriggerEvent(event_key);
	}
}

void player_retinue_manager::SendChatRecord(int retinue_id, int chat_id)
{
	PB::gp_retinue_data_notify pb;
	pb.set_notify_type(PB::gp_retinue_data_notify::N_TYPE_CHAT_RECORD);
	pb.set_retinue_id(retinue_id);
	pb.set_param1(chat_id);

	if (retinue_id == 0)
	{
		//取所有的伙伴数据
		for (auto& it : _retinue_friends)
		{
			it.second->GetChatRecord(chat_id, pb);
		}
	}
	else
	{
		auto *pRetinue = GetRetinueFriend(retinue_id);
		if (pRetinue)
		{
			pRetinue->GetChatRecord(chat_id, pb);
		}
	}

	_imp->Runner()->CommonSend<S2C::CMD::PBS2C>(pb);
}

void player_retinue_manager::OnLeaveCombat()
{
	_next_main_skill_time = 0;
	_next_assist_skill_time = 0;
	_ready_casting = false;
	_skill_target.Clear();
	_skill_delay = 0;
	_combat_begin_time = 0;
	_casting_skill_slot = INVALID_COMBAT_INDEX;
}

int player_retinue_manager::GetNextSkillTime() const
{
	int time =  std::min(_next_main_skill_time > 0 ? _next_main_skill_time : INT_MAX, _next_assist_skill_time > 0 ? _next_assist_skill_time : INT_MAX);
	if (time == INT_MAX)
	{
		return 0;
	}
	return time;
}

int player_retinue_manager::GetNextSkillSlot() const
{
	int time = GetNextSkillTime();
	if (time == _next_main_skill_time)
	{
		return 0;
	}
	if (time == _next_assist_skill_time)
	{
		return _next_assist_skill_slot;
	}
	return -1;
}

void player_retinue_manager::CalcSkillTime()
{
	int now = gmatrix::GetInstance().GetSysTime();
	if (GetNextSkillTime() == 0)
	{
		//主战伙伴固定作为第一个释放技能
		int main_time = 0;
		if (GetCombatRetinue(MAIN_COMBAT_RETINUE_SLOT))
		{
			main_time = abase::Rand(retinue_template_manager::GetInstance().main_combat_skill_cd_min, retinue_template_manager::GetInstance().main_combat_skill_cd_max);
		}
		int assist_time = 0;
		for (auto it = _combat_retinues.begin(); it != _combat_retinues.end(); ++it)
		{
			int slot = it->first;
			if (slot == 0)
			{
				continue;
			}
			int single_time = retinue_template_manager::GetInstance().assist_combat_skill_cd_single;
			assist_time = main_time + single_time * (slot - 1) + abase::Rand(0, single_time - 5);
			_next_assist_skill_slot = slot;
			break;
		}

		if (main_time > 0)
		{
			_next_main_skill_time = now + main_time;
		}
		if (assist_time > 0)
		{
			_next_assist_skill_time = now + assist_time;
		}
		__PRINTF("RetinueCalcSkillTimeBegin now=%d main=%d ass=%d slot=%d ass_slot=%d\n", now, _next_main_skill_time, _next_assist_skill_time, GetNextSkillSlot(), _next_assist_skill_slot);
		return;
	}

	if (_next_main_skill_time > 0 && _casting_skill_slot == 0)
	{
		_next_main_skill_time = now + abase::Rand(retinue_template_manager::GetInstance().main_combat_skill_cd2_min, retinue_template_manager::GetInstance().main_combat_skill_cd2_max);
	}
	if (_next_assist_skill_time > 0 && _casting_skill_slot > 0)
	{
		//辅战伙伴分成4段时间 每段时间内随机一个值 如果该段时间没有辅战伙伴 留空
		int slot = _next_assist_skill_slot + 1;
		if (slot > ASSIST_COMBAT_RETINUE_NUM)
		{
			slot = 1;
		}
		int single_time = retinue_template_manager::GetInstance().assist_combat_skill_cd_single;
		int next_time = 0;
		for (auto it = _combat_retinues.begin(); it != _combat_retinues.end(); ++it)
		{
			int st = it->first;
			if (st < slot)
			{
				continue;
			}
			next_time = _next_assist_skill_time - ((_next_assist_skill_time - _combat_begin_time) % single_time) + (st - slot + 1) * single_time;
			_next_assist_skill_time = abase::Rand(next_time, next_time + single_time - 1);
			_next_assist_skill_slot = st;
			break;
		}
		if (next_time == 0) //再从头开始找一遍
		{
			for (auto it = _combat_retinues.begin(); it != _combat_retinues.end(); ++it)
			{
				int st = it->first;
				if (st == 0)
				{
					continue;
				}
				next_time = _next_assist_skill_time - ((_next_assist_skill_time - _combat_begin_time) % single_time) + (st + ASSIST_COMBAT_RETINUE_NUM - slot + 1) * single_time;
				_next_assist_skill_time = abase::Rand(next_time, next_time + single_time - 1);
				_next_assist_skill_slot = st;
				break;
			}
		}
	}
	__PRINTF("RetinueCalcSkillTime now=%d main=%d ass=%d slot=%d ass_slot=%d\n", now, _next_main_skill_time, _next_assist_skill_time, GetNextSkillSlot(), _next_assist_skill_slot);
}
void player_retinue_manager::TryCastActiveSkill(const XID& target)
{
	if (_imp->GetParent()->IsForBidRetinueSkill())
	{
		return;
	}
	if (_ready_casting)
	{
		return;
	}

	int now = gmatrix::GetInstance().GetSysTime();
	if (_combat_begin_time == 0)
	{
		_combat_begin_time = now;
	}

	if (GetNextSkillTime() == 0)
	{
		CalcSkillTime();
		return;
	}

	if (now < GetNextSkillTime())
	{
		return;
	}

	auto combat_retinue = GetCombatRetinue(GetNextSkillSlot());
	if (!combat_retinue || !combat_retinue->GetCombatSkill())
	{
		return;
	}

	PB::gp_retinue_cast_skill proto;
	proto.set_player_id(_imp->GetParent()->ID.id);
	proto.mutable_retinue()->set_retinue_id(combat_retinue->GetTID());
	proto.mutable_retinue()->set_fashion_id(combat_retinue->GetFashionIndex());
	proto.mutable_retinue()->set_color_id(combat_retinue->GetFashionColor());
	proto.set_skill_id(combat_retinue->GetCombatSkill());
	proto.set_skill_target(target.id);
	proto.set_skill_type(0);
	_imp->Runner()->RegionSend<S2C::CMD::PBS2C>(proto);

	_ready_casting = true;
	_skill_target = target;
	_skill_delay = RETINUE_SKILL_DELAY;
	_casting_skill_slot = GetNextSkillSlot();

	//__PRINTF("TryCastRetinueActiveSkill now=%d main=%d ass=%d slot=%d ass_slot=%d\n", now, _next_main_skill_time, _next_assist_skill_time, GetNextSkillSlot(), _next_assist_skill_slot);
}

void player_retinue_manager::DoCastActiveSkill()
{
	if (_imp->GetParent()->IsForBidRetinueSkill())
	{
		return;
	}

	if (!_ready_casting)
	{
		return;
	}

	auto combat_retinue = GetCombatRetinue(_casting_skill_slot);
	if (!combat_retinue || !combat_retinue->GetCombatSkill())
	{
		return;
	}

	if (!_imp->CheckCanAttack(_skill_target))
	{
		_next_main_skill_time = 0;
		_next_assist_skill_time = 0;
		return;
	}

	if (GNET::FuncSwitchManager::Instance().getFuncSwitch(kFuncCodeRetinue) &&
	        GNET::FuncSwitchManager::Instance().getFuncSwitch(kFuncCodeRetinueSelect))
	{
		ConditionSuppressCombatStat(_imp, SuppressCombatSystem::RETINUE)
			.Then(nullptr, "player_retinue_manager::DoCastActiveSkill: suppress combat skill cast")
			.Else([&](gcreature_imp*) {
				_imp->PartnerCastSkill(RETINUE_CAST_SKILL, combat_retinue->GetTID(), combat_retinue->GetCombatSkill(), _skill_target);
			}, "player_retinue_manager::DoCastActiveSkill: normal combat skill cast");
	}

	__PRINTF("DoCastRetinueActiveSkill roleid=%ld, now=%ld, casting_slot=%d retinue_id=%d\n", _imp->Parent()->ID.id, gmatrix::GetInstance().GetSysTime(), _casting_skill_slot, combat_retinue->GetTID());
}

void player_retinue_manager::FillAttackMsg(const XID& target, attack_msg& attack)
{
	int pos = 0;
	int slot_index = -1;
	if (_cur_active_buffskill > 0)
	{
		auto it1 = _active_extra_buff_skill_relation.find(_cur_active_buffskill);
		if (it1 != _active_extra_buff_skill_relation.end())
		{
			slot_index = it1->second.second;
			pos = 1;
		}
		else
		{
			auto it2 = _active_buff_skill_relation.find(_cur_active_buffskill);
			if (it2 != _active_buff_skill_relation.end())
			{
				slot_index = it2->second.second;
				pos = 2;
			}
			else
			{
				auto it3 = _active_extra_awake_skill_relation.find(_cur_active_buffskill);
				if (it3 != _active_extra_awake_skill_relation.end())
				{
					slot_index = it3->second.second;
					pos = 3;
				}
				else
				{
					slot_index = _casting_skill_slot;
					pos = 4;
				}
			}
		}
	}
	else
	{
		slot_index = _casting_skill_slot;
		pos = 5;
	}
	__PRINTF("player_retinue_manager::FillAttackMsg _cur_active_buffskill=%d pos=%d slot=%d\n", _cur_active_buffskill, pos, slot_index);
	auto it = _combat_retinues.find(slot_index);
	if (it == _combat_retinues.end() || !it->second._retinue)
	{
		return;
	}

	attack.attack_physic = std::max(attack.attack_physic, attack.attack_magic);
	attack.attack_magic = attack.attack_physic;

	creature_prop& prop = it->second._prop;

	attack.base_attack_physic = prop.GetGProperty()->PROP_NAME_GET(curPhyAtk);
	attack.base_attack_magic = prop.GetGProperty()->PROP_NAME_GET(curMagAtk);
}

bool player_retinue_manager::HasRetinueSkill(int skill_id) const
{
	const auto combat_retinue = GetCombatRetinue(_casting_skill_slot);
	if (!combat_retinue)
	{
		return false;
	}

	const auto& data = combat_retinue->GetData();
	for (int i = 0; i < data.relation_skill_size(); ++i)
	{
		if (skill_id == data.relation_skill(i))
		{
			return true;
		}
	}

	return false;
}

void player_retinue_manager::OnBuffSkillCast(int skill_id, const XID& target)
{
	//LOG_TRACE("player_retinue_manager::OnBuffSkillCast player=%ld:skill_id=%d", _imp->GetParent()->ID.id, skill_id);
	auto it = _active_extra_buff_skill_relation.find(skill_id);
	if (it == _active_extra_buff_skill_relation.end())
	{
		return;
	}

	auto retinue = GetRetinue(it->second.first);
	if (!retinue)
	{
		return;
	}

	/*if (!retinue->IsRetinueSkillExtra(skill_id))
	{
		return;
	}*/

	//通知客户端支援技触发的技能释放
	PB::gp_retinue_cast_skill proto;
	proto.set_player_id(_imp->GetParent()->ID.id);
	proto.mutable_retinue()->set_retinue_id(retinue->GetTID());
	proto.mutable_retinue()->set_fashion_id(retinue->GetFashionIndex());
	proto.mutable_retinue()->set_color_id(retinue->GetFashionColor());
	proto.set_skill_id(skill_id);
	proto.set_skill_target(target.id);
	proto.set_skill_type(1);
	_imp->Runner()->RegionSend<S2C::CMD::PBS2C>(proto);
	//LOG_TRACE("player_retinue_manager::OnBuffSkillCast2 player=%ld:skill_id=%d", _imp->GetParent()->ID.id, skill_id);
}

bool player_retinue_manager::CanActiveBuffSkill(int skill_id) const
{
	auto it = _active_buff_skill_relation.find(skill_id);
	if (it == _active_buff_skill_relation.end())
	{
		if (_imp->IsPlayerClass())
		{
			gplayer_imp *pImp =  (gplayer_imp *)_imp;
			std::ostringstream oss;
			oss << "伙伴言灵触发失败 未找到skill_id=" << skill_id;
			pImp->DebugUTF8Say(oss.str().c_str());
		}
		return false;
	}

	int slot = it->second.second;
	int ci = slot == 0 ? CI_RETINUE_MAIN_COMBAT_SKILL : CI_RETINUE_ASSIST_COMBAT_SKILL;
	if (_imp->TestCoolDown(ci))
	{
		return true;
	}

	if (_imp->IsPlayerClass())
	{
		gplayer_imp *pImp =  (gplayer_imp *)_imp;
		std::ostringstream oss;
		oss << "伙伴言灵触发失败 cd中 skillid=" << skill_id;
		pImp->DebugUTF8Say(oss.str().c_str());
	}

	return false;
}

void player_retinue_manager::OnBuffSkillActive(int skill_id)
{
	auto it = _active_buff_skill_relation.find(skill_id);
	if (it == _active_buff_skill_relation.end())
	{
		return;
	}

	int slot = it->second.second;
	int ci = slot == 0 ? CI_RETINUE_MAIN_COMBAT_SKILL : CI_RETINUE_ASSIST_COMBAT_SKILL;
	int cd = slot == 0 ? retinue_template_manager::GetInstance().main_combat_yanling_cd : retinue_template_manager::GetInstance().assist_combat_yanling_cd;
	if (_imp->IsPlayerClass())
	{
		gplayer_imp *pImp =  (gplayer_imp *)_imp;
		std::ostringstream oss;
		oss << "伙伴言灵触发完成 skill_id=" << skill_id;
		pImp->DebugUTF8Say(oss.str().c_str());
	}
	_imp->SetCoolDown(ci, cd);
}

int player_retinue_manager::InnerSeqRecruit(int pool_id, int seq_index, int count, std::vector<int>& result)
{
	gplayer_imp *pImp = GetPlayerImp();
	if (!pImp)
	{
		return S2C::ERR_SERVICE_UNAVILABLE;
	}

	gmatrix::ScriptKeeper keeper(LUA_ENV_RETINUE);
	lua_State *L = keeper.GetState();
	if (!L)
	{
		return S2C::ERR_SERVICE_UNAVILABLE;
	}
	LuaWrapper lw(L);

	for (int i = 0; i < count; ++i)
	{
		int index = pImp->GetDrawSeqInfo().Draw(pImp, seq_index);
		if (index < 0)
		{
			return S2C::ERR_SERVICE_UNAVILABLE;
		}

		LuaParameter param(pool_id, index);
		if (!lw.gExec("GetSeqRecruitResult", param))
		{
			GLog::log(LOG_ERR, "伙伴招募 执行脚本出错 GetSeqRecruitResult failed(tid: %d %d),%s", pool_id, index, lw.ErrorMsg());
			return S2C::ERR_SERVICE_UNAVILABLE;
		}

		if (!lua_isnumber(L, -1))
		{
			GLog::log(LOG_ERR, "GetSeqRecruitResult result failed. pool_id=%d index=%d", pool_id, index);
			return S2C::ERR_SERVICE_UNAVILABLE;
		}

		int retinue_id = LUA_TOINTEGER(L, -1);
		result.push_back(retinue_id);
	}
	return 0;
}

int player_retinue_manager::InnerRecruitOnce(int pool_id, bool for_pay, int& retinue_id)
{
	gplayer_imp *pImp = GetPlayerImp();
	if (!pImp)
	{
		return S2C::ERR_SERVICE_UNAVILABLE;
	}

	int now = gmatrix::GetInstance().GetSysTime();
	gmatrix::ScriptKeeper keeper(LUA_ENV_RETINUE);
	lua_State *L = keeper.GetState();
	if (!L)
	{
		return S2C::ERR_SERVICE_UNAVILABLE;
	}
	LuaWrapper lw(L);
	bool is_active_player = pImp->GetSwitch().Get(SERVER_SWITCH_TYPE, SERVER_SWITCH_INDEX_YESTERDAY_ACTIVE);

	if (_recruit.pay_timestamp < GetLocalDayBegin())
	{
		_recruit.pay_today = 0;
		_recruit.pay_timestamp = now;
	}

	int special_mask[RR_SPECIAL_COUNT] = {0};
	if ((_recruit.free_total < 3 && !for_pay) || (_recruit.pay_6_total < 3 && for_pay))
	{
		//前三次免费抽卡需要给玩家三个不同的伙伴 玩家前三次抽到的6星需要是不一样的
		//这两种情况特殊处理下
		special_mask[RR_SPECIAL_0] = 1;
		__PRINTF("player_retinue_manager::RecruitRetinue 特殊处理 rolelid=%ld:pool_id=%d:free_total:%d:pay_6_total:%d\n", _imp->GetParent()->ID.id, pool_id, _recruit.free_total, _recruit.pay_6_total);
	}
	if ( _special_pay_recruit_count == _recruit.pay_total + 1 && for_pay)
	{
		__PRINTF("player_retinue_manager::RecruitRetinue 特殊处理2 rolelid=%ld:pool_id=%d:pay_total:%d\n", _imp->GetParent()->ID.id, pool_id, _recruit.pay_total);
		special_mask[RR_SPECIAL_1] = 1;
	}
	if ( _special_free_recruit_count == _recruit.free_total + 1 && !for_pay)
	{
		__PRINTF("player_retinue_manager::RecruitRetinue 特殊处理3 rolelid=%ld:pool_id=%d:free_total:%d\n", _imp->GetParent()->ID.id, pool_id, _recruit.free_total);
		special_mask[RR_SPECIAL_2] = 1;
	}
	if ( _special_pay2_recruit_count == _recruit.pay_total + 1 && for_pay)
	{
		__PRINTF("player_retinue_manager::RecruitRetinue 特殊处理4 rolelid=%ld:pool_id=%d:pay_total:%d\n", _imp->GetParent()->ID.id, pool_id, _recruit.pay_total);
		special_mask[RR_SPECIAL_3] = 1;
	}

	LuaParameter param(pool_id, _recruit.pay_total, _recruit.pay_last, _recruit.pay_today, is_active_player);//, special_mask);
	std::vector<int> masks(special_mask, special_mask + RR_SPECIAL_COUNT);
	param.AddParameter(masks);
	std::vector<int> cur_retinues;
	if (special_mask[RR_SPECIAL_0] > 0)
	{
		//把当前已有的伙伴传进参数
		for (auto& it : _retinues)
		{
			cur_retinues.push_back(it.first);
		}
	}
	param.AddParameter(cur_retinues);
	SLOG(TRACE, "InnerRecruitOnce").P("roleid", _imp->GetParent()->ID.id).PS(pool_id).P("pay_total", _recruit.pay_total).P("pay_6_total", _recruit.pay_6_total).P("pay_last", _recruit.pay_last);
	if (!lw.gExec("GetRecruitResult", param))
	{
		GLog::log(LOG_ERR, "伙伴招募 执行脚本出错 GetRecruitResult failed(tid: %d),%s", pool_id, lw.ErrorMsg());
		return S2C::ERR_SERVICE_UNAVILABLE;
	}

	if (!lua_isnumber(L, -1))
	{
		GLog::log(LOG_ERR, "GetRecruitResult result failed. pool_id=%d", pool_id);
		return S2C::ERR_SERVICE_UNAVILABLE;
	}

	retinue_id = LUA_TOINTEGER(L, -1);

	//记录玩家付费池抽卡次数
	if (for_pay)
	{
		++ _recruit.pay_total;

		++ _recruit.pay_today;
		_recruit.pay_timestamp = now;

		const retinue_template *pTemplate = retinue_template_manager::GetInstance().GetRetinueTemplate(retinue_id);
		if (pTemplate)
		{
			if (pTemplate->star >= 6)
			{
				_recruit.pay_last = 0;
				++ _recruit.pay_6_total;
			}
			else
			{
				++ _recruit.pay_last;
			}
		}
	}
	else
	{
		++ _recruit.free_total;
	}

	return 0;
}

int player_retinue_manager::RecruitRetinue(int pool_id, bool is_ten)
{
	gplayer_imp *pImp = GetPlayerImp();
	if (!pImp)
	{
		return S2C::ERR_SERVICE_UNAVILABLE;
	}

	if (pImp->IsRoamIn() && !GLOBAL_CONFIG.IsNormalCenterZone(GNET::g_zoneid))
	{
		__PRINTF("GS:roleid="   FMT_I64":跨服禁止伙伴招募\n", _imp->GetParent()->ID.id);
		return S2C::ERR_SERVICE_UNAVILABLE;
	}

	const recruit_pool_config *pool_config = retinue_template_manager::GetInstance().GetRecruitPoolConfig(pool_id);
	if (!pool_config)
	{
		return S2C::ERR_SERVICE_UNAVILABLE;
	}

	const recruit_type_config *type_config = retinue_template_manager::GetInstance().GetRecruitTypeConfig(pool_config->recruit_type);
	if (!type_config)
	{
		return S2C::ERR_SERVICE_UNAVILABLE;
	}
	if (pImp->GetLevel() < type_config->level_limit)
	{
		return S2C::ERR_LEVEL_NOT_MATCH;
	}

	if (pool_config->activity_id > 0 && !activity_manager::GetInstance().IsActivityOpen(pool_config->activity_id))
	{
		return S2C::ERR_SERVICE_UNAVILABLE;
	}
	//if (is_ten && !type_config->for_pay)
	//{
	//	return S2C::ERR_SERVICE_UNAVILABLE;
	//}

	//检查下有没有免费次数
	bool use_free = false;
	do
	{
		//如果没有设置免费次数限制和CD 不能进行免费使用
		//使用免费服务
		if (is_ten)
		{
			break; //十连抽不能用免费次数
		}
		if (!type_config->free_cool_down_id)
		{
			break;
		}

		if (!pImp->TestCoolDown(type_config->free_cool_down_id))
		{
			break;
		}
		if (!pImp->GetCommonUseLimit().TestUseLimit(type_config->free_common_uselimit_id))
		{
			break;
		}
		use_free = true;
	}
	while (0);


	const int total_count = is_ten ? 10 : 1;
	const int cost_count = is_ten ? (IsFirst10Recruit(pool_id) ? type_config->first_10_cost_count : 10) : 1;
	ITEM_CHECK_INFO info;
	if (!use_free)
	{
		if (!pImp->CheckItemExistAtBackpack2(info, type_config->cost_item_id, is_ten ? type_config->cost_item_count *cost_count : type_config->cost_item_count))
		{
			return S2C::ERR_NOT_ENOUGH_MATERIAL;
		}
	}
	if (type_config->gain_item_id > 0 && type_config->gain_item_count > 0)
	{
		if (pImp->GetInventory().GetBackpack().IsFull())
		{
			return S2C::ERR_INVENTORY_IS_FULL;
		}
	}

	if (!use_free && type_config->total_uselimit_id > 0)
	{
		if (!pImp->GetCommonUseLimit().TestUseLimit(type_config->total_uselimit_id, total_count))
		{
			return S2C::ERR_OPERATION_IS_COOLING;
		}
	}

	int retinue_ids[total_count];
	memset(retinue_ids, 0, sizeof(retinue_ids));

	if (pool_config->seq_index > 0)
	{
		//序列抽奖
		std::vector<int> rets;
		int ret = InnerSeqRecruit(pool_id, pool_config->seq_index, total_count, rets);
		if (ret != 0)
		{
			return ret;
		}
		for (int index = 0; index < rets.size(); ++ index)
		{
			retinue_ids[index] = rets[index];
		}
	}
	else if (is_ten)
	{
		RECRUIT_INFO tmp = _recruit;

		// 普通奖池的baodi_star设为1就不会出发保底了
		const int baodi_star = pool_id == 1 ? 1 : (IsFirst10Recruit(pool_id) ? 5 : 4);
		bool has_special = false;
		for (int i = 0; i < total_count; ++i)
		{
			int ret = InnerRecruitOnce(pool_id, pool_config->recruit_type, retinue_ids[i]);
			if (ret != 0)
			{
				_recruit = tmp;
				return ret;
			}
			const retinue_template *pTemplate = retinue_template_manager::GetInstance().GetRetinueTemplate(retinue_ids[i]);
			if (!pTemplate )
			{
				_recruit = tmp;
				return S2C::ERR_SERVICE_UNAVILABLE;
			}

			if (pTemplate->star >= baodi_star)
			{
				has_special = true;
			}
		}

		if (!has_special)
		{
			//十连抽如果没抽到4星及以上的伙伴 特殊处理
			int another_retinue_id = 0;
			do
			{
				gmatrix::ScriptKeeper keeper(LUA_ENV_RETINUE);
				lua_State *L = keeper.GetState();
				if (!L)
				{
					break;;
				}
				LuaWrapper lw(L);
				if (!lw.gExec("GetSpecialStarRetinue", LuaParameter(pool_id, baodi_star)))
				{
					GLog::log(LOG_ERR, "伙伴招募 执行脚本出错 GetSpecialStarRetinue failed(tid: %d),%s", pool_id, lw.ErrorMsg());
					break;
				}

				if (!lua_isnumber(L, -1))
				{
					GLog::log(LOG_ERR, "GetSpecialStarRetinue result failed. pool_id=%d", pool_id);
					break;
				}

				another_retinue_id = LUA_TOINTEGER(L, -1);
			}
			while (0);

			if (another_retinue_id > 0)
			{
				//最后一个替换为4星伙伴
				retinue_ids[total_count - 1] = another_retinue_id;
			}
		}
	}
	else
	{
		int ret = InnerRecruitOnce(pool_id, type_config->for_pay, retinue_ids[0]);
		if (ret != 0)
		{
			return ret;
		}

	}

	//如果不是免费扣除消耗
	FuncInfo func_info{kFuncCodeRetinueRecruit, retinue_ids[0], pool_id};
	if (!use_free)
	{
		pImp->DecItem2(info, func_info);
	}
	else
	{
		pImp->SetCoolDown(type_config->free_cool_down_id, type_config->free_cool_down_time * 1000);
		pImp->GetCommonUseLimit().AddUseLimit(pImp, type_config->free_common_uselimit_id);
	}

	if (!use_free && type_config->total_uselimit_id > 0)
	{
		pImp->GetCommonUseLimit().AddUseLimit(pImp, type_config->total_uselimit_id, total_count);
	}

	bool is_auto_decompose[total_count];
	memset(is_auto_decompose, 0, sizeof(is_auto_decompose));
	for (int j = 0; j < total_count; ++j)
	{
		if (AddRetinue(retinue_ids[j], 1, INC_RETINUE_TYPE_RECRUIT, is_auto_decompose[j], pool_id) != 0)
		{
			GLog::log(LOG_ERR, "Recruit Add Retinue failed. pool_id=%d:retinue_id=%d", pool_id, retinue_ids[j]);
			return S2C::ERR_SERVICE_UNAVILABLE;
		}
	}

	if (type_config->gain_rep_id > 0)
	{
		pImp->ModifyReputation(func_info, type_config->gain_rep_id, is_ten ? total_count : 1);
	}

	if (is_ten)
	{
		SetFirst10Recruit(pool_id);
	}

	//给抽奖奖励道具
	if (type_config->gain_item_id > 0 && type_config->gain_item_count > 0)
	{
		gen_item_t gen;
		gen.tid = type_config->gain_item_id;
		gen.count = is_ten ? type_config->gain_item_count * total_count : type_config->gain_item_count;
		pImp->GenAndIncItem(IGT_RETINUE_RECRUIT, func_info, gen);
	}

	pImp->GetAchievement().OnDrawPartner(pImp, type_config->cost_item_id, total_count);

	//通知客户端结果
	if (is_ten)
	{
		BI_LOG_GLOBAL(pImp->GetParent()->account.ToStr());
		PB::gp_retinue_data_notify pb;
		pb.set_notify_type(PB::gp_retinue_data_notify::N_TYPE_RECRUIT_TEN);
		pb.set_param1(pool_id);

		for (int i = 0; i < total_count; ++i)
		{
			auto *tmp = pb.add_recruit_result();
			tmp->set_retinue_id(retinue_ids[i]);
			tmp->set_auto_decompose(is_auto_decompose[i]);

			SLOG(FORMAT, "recruit_retinue")
			.BI_HEADER2_GS(pImp)
			.P("pool_id", pool_id)
			.P("use_free", use_free)
			.P("retinue_id", retinue_ids[i]);
		}
		pImp->Runner()->CommonSend<S2C::CMD::PBS2C>(pb);
	}
	else
	{
		NotifyRetinueInfo(NULL, PB::gp_retinue_data_notify::N_TYPE_RECRUIT, retinue_ids[0], pool_id, is_auto_decompose[0] ? 1 : 0);

		// for BI
		BI_LOG_GLOBAL(pImp->GetParent()->account.ToStr());
		SLOG(FORMAT, "recruit_retinue")
		.BI_HEADER2_GS(pImp)
		.P("pool_id", pool_id)
		.P("use_free", use_free)
		.P("retinue_id", retinue_ids[0]);
	}
	return 0;
}

int player_retinue_manager::GetPropByIdx(int skill_id, int prop_idx) const
{
	//先判断是不是支援技buff触发的技能 再找被动技触发的技能 如果不是再找当前释放技能的伙伴
	int slot_index = -1;
	int pos = 0;
	do
	{
		auto it = _active_extra_buff_skill_relation.find(skill_id);
		if (it != _active_extra_buff_skill_relation.end())
		{
			slot_index = it->second.second;
			pos = 1;
			break;
		}
		auto it2 = _active_buff_skill_relation.find(skill_id);
		if (it2 != _active_buff_skill_relation.end())
		{
			slot_index = it2->second.second;
			pos = 2;
			break;
		}
		auto it3 = _active_extra_awake_skill_relation.find(skill_id);
		if (it3 != _active_extra_awake_skill_relation.end())
		{
			slot_index = it3->second.second;
			pos = 3;
			break;
		}

		slot_index = _casting_skill_slot;
		pos = 4;
	}
	while (0);
	__PRINTF("player_retinue_manager::GetPropByIdx skill_id=%d pos=%d slot=%d\n", skill_id, pos, slot_index);
	auto slot = GetCombatSlot(slot_index);
	if (slot && slot->_retinue)
	{
		return slot->_prop.PropGet<int>(prop_idx);
	}
	return 0;
}

bool CheckNewStatusExt1Str(const std::string& ext1, COMMENT_MAP& comment_map)
{
	std::vector<std::string> vv = StringUtils::split(ext1, '$');
	for (auto& vstr : vv)
	{
		// 时间|发送者id|对谁进行回复|评论id$
		std::vector<std::string> vSingleInfo = StringUtils::split(vstr, '|');
		if (vSingleInfo.size() != 4)
		{
			__PRINTF("CheckNewStatusExt1Str fail ==> %s\n", vstr.c_str());
			return false;
		}
		int timestamp = std::stoi(vSingleInfo[0]);
		if (timestamp <= 0)
		{
			return false;
		}
		comment_info newInfo;
		newInfo.comment_id = std::stoi(vSingleInfo[3]);
		newInfo.comment_target_id = std::stoi(vSingleInfo[2]);
		newInfo.timestamp = timestamp;
		newInfo.comment_id = std::stoi(vSingleInfo[3]);
		comment_map[timestamp].push_back(newInfo);
	}
	return true;
}

bool CheckNewStatusExt2Str(const std::string& ext2, FAV_MAP& fav_map)
{
	std::vector<std::string> vv = StringUtils::split(ext2, '$');
	for (auto& vstr : vv)
	{
		//时间|点赞者id$
		std::vector<std::string> vSingleInfo = StringUtils::split(vstr, '|');
		if (vSingleInfo.size() != 2)
		{
			__PRINTF("CheckNewStatusExt2Str fail ==> %s\n", vstr.c_str());
			return false;
		}
		int timestamp = std::stoi(vSingleInfo[0]);
		if (timestamp <= 0)
		{
			return false;
		}
		auto& newInfo = fav_map[timestamp];
		newInfo.set_retinue_id(std::stoi(vSingleInfo[1]));
		newInfo.set_timestamp(timestamp);
	}
	return true;
}

int player_retinue_manager::CheckCommentList(int gender, const retinue_ss_status_config *pCfg, COMMENT_MAP& comment_map, std::unordered_set<int>& comment_line_nodes, bool is_init)
{
	// 验证起始评论
	std::unordered_set<int> legal_next_comment_ids;
	std::map<int/*comment_tid*/, std::set<int>> comment_line_node_next_map;
	const std::vector<std::vector<int>>& first_group = pCfg->first_comment_group(gender);

	auto GetCommentNextIds = [&comment_line_nodes](const std::vector<std::vector<int>>& continue_group, std::set<int>& next_comment_ids)
	{
		next_comment_ids.clear();
		for (const std::vector<int>& group : continue_group )
		{
			bool group_used = false;
			for (int comment_id : group)
			{
				if (contains(comment_line_nodes, comment_id))
				{
					group_used = true;
					break;
				}
			}
			if (!group.empty() && !group_used) // 还有的评论组没有触发
			{
				next_comment_ids.insert(group.begin(), group.end());
			}
		}
	};
	auto RebuildLegalNextCommentIds = [&comment_line_node_next_map, &legal_next_comment_ids]() -> void
	{
		legal_next_comment_ids.clear();
		for (auto& next_kv : comment_line_node_next_map)
		{
			auto& comment_next_ids = next_kv.second;
			if (!comment_next_ids.empty())
			{
				legal_next_comment_ids.insert(comment_next_ids.begin(), comment_next_ids.end());
			}
		}
	};

	auto TryAddComment =
	    [gender, &first_group, is_init, &comment_line_node_next_map, &legal_next_comment_ids, &comment_line_nodes, &GetCommentNextIds, &RebuildLegalNextCommentIds, pCfg]
	    (int comment_id, int pre_comment_id, const retinue_ss_status_config::comment_config * pCommentCfg, bool pre_commit_in_map) -> bool
	{
		// 属于玩家的评论不能自动追加
		if (((is_init || pre_commit_in_map) && pCommentCfg->partner_id == 0) || pre_comment_id < 0)
		{
			return false;
		}
		const retinue_ss_status_config::comment_config *pPreCommentCfg = NULL;
		if (pre_comment_id > 0)
		{
			pPreCommentCfg = pCfg->GetComment(gender, pre_comment_id);
			if (!pPreCommentCfg)
			{
				return false;
			}
		}
		// 合法的回复
		comment_line_nodes.insert(comment_id);
		// 这次评论会影响升级评论的其他可用评论，重新收集一下
		if (pre_comment_id > 0)
		{
			GetCommentNextIds(pPreCommentCfg->continue_group, comment_line_node_next_map[pre_comment_id]);
		}
		else if (pre_comment_id == 0)
		{
			// 起始评论分组单独处理一下
			GetCommentNextIds(first_group, comment_line_node_next_map[0]);
		}
		// 收集一下本次评论的下级可用评论
		GetCommentNextIds(pCommentCfg->continue_group, comment_line_node_next_map[comment_id]);
		RebuildLegalNextCommentIds();
		return true;
	};

	// 根据已有comment_line_nodes 初始化一次
	for (int comment_id : comment_line_nodes)
	{
		auto pCommentCfg = pCfg->GetComment(gender, comment_id);
		if (!pCommentCfg)
		{
			continue;
		}
		GetCommentNextIds(pCommentCfg->continue_group, comment_line_node_next_map[comment_id]);
	}
	// 起始评论分组单独处理一下
	GetCommentNextIds(first_group, comment_line_node_next_map[0]);
	RebuildLegalNextCommentIds();

	auto *pImp = GetPlayerImp();
	if (!pImp)
	{
		return -1;
	}

	std::set<int> check_comment_set;
	for (auto& comment_kv : comment_map)
	{
		for (auto& comment : comment_kv.second)
		{
			check_comment_set.insert(comment.comment_id);
		}
	}
	for (auto& comment_kv : comment_map)
	{
		for (auto& comment : comment_kv.second)
		{
			int comment_id = comment.comment_id;
			auto pCommentCfg = pCfg->GetComment(gender, comment_id);
			if (!contains(legal_next_comment_ids, comment_id))
			{
				return comment_id;
			}
			ASSERT(pCommentCfg); //contains判断过了必定能找到
			int pre_comment_id = pCommentCfg->pre_comment_id;
			if (is_init)
			{
				// 起始评论里有comment.comment_target_id 没有pre_comment_id的信息
				comment.pre_comment_id = pre_comment_id;
			}
			else
			{
				// 后续评论里有pre_comment_id 没有comment.comment_target_id的信息
				if (comment.pre_comment_id != pre_comment_id)
				{
					return comment_id;
				}
				if (pre_comment_id > 0)
				{
					auto pPreCommentCfg = pCfg->GetComment(gender, pre_comment_id);
					ASSERT(pPreCommentCfg); //contains判断过了必定能找到
					comment.comment_target_id = pPreCommentCfg->partner_id;
				}
			}
			bool pre_commit_in_map = contains(check_comment_set, pre_comment_id);
			// 加上新节点
			if (!TryAddComment(comment_id, pre_comment_id, pCommentCfg, pre_commit_in_map))
			{
				return comment_id;
			}

		}
	}

	return 0;
}

bool CheckStatusFinish(int gender, const retinue_ss_status_config *pCfg, const PB::retinue_ss_status_info& status)
{
	// 2018-09-19 新增规则，评论可能配置多个后续评论分组，每一个分组都要执行
	int start_line_count = 0;
	std::vector<const std::vector<int>*> continue_group; // 后续评论分组

	std::unordered_set<int> comment_line_nodes;
	for (int comment_id : status.comment_line_nodes())
	{
		auto pCommentCfg = pCfg->GetComment(gender, comment_id);
		if (!pCommentCfg)
		{
			return false; // 配置有问题
		}
		comment_line_nodes.insert(comment_id);
		if (contains(pCfg->first_comment_set(gender), comment_id))
		{
			start_line_count++;
		}
		for (const std::vector<int>& group : pCommentCfg->continue_group)
		{
			continue_group.push_back(&group);
		}
	}

	// is all comment line started
	if (start_line_count < pCfg->comment_line_count(gender))
	{
		return false;
	}

	// is all comment finished
	for (auto group : continue_group)
	{
		bool group_used = false;
		for (int next_comment_id : *group)
		{
			if (contains(comment_line_nodes, next_comment_id))
			{
				group_used = true;
				break;
			}
		}
		if (!group_used) // 这个分组并没有被执行，所以status 并未完成
		{
			return false;
		}
	}

	return true;
}

bool player_retinue_manager::TryCreateStatus(const retinue_ss_status_config *pCfg, const std::string& ext1, const std::string& ext2)
{
	// 拆解字符串
	COMMENT_MAP comment_map;
	if (!CheckNewStatusExt1Str(ext1, comment_map))
	{
		return false;
	}
	FAV_MAP fav_map;
	if (!CheckNewStatusExt2Str(ext2, fav_map))
	{
		return false;
	}

	auto *pImp = GetPlayerImp();
	if (!pImp)
	{
		return false;
	}

	int gender = _imp->GetGender();
	// 验证起始评论
	std::unordered_set<int> comment_line_nodes;
	int ret = 0;
	if ((ret = CheckCommentList(gender, pCfg, comment_map, comment_line_nodes, true/*is_init*/)) != 0)
	{
		SLOG(ERR, "create_status_check_comment_fail")
		.P("ret", ret)
		.P("roleid", pImp->GetParent()->ID.id)
		.P("status_id", pCfg->status_id)
		.P("ext1", ext1)
		.P("ext2", ext2);
		return false;
	}

	// 验证点赞
	for (auto& fav_kv : fav_map)
	{
		auto& fav_info = fav_kv.second;
		int partner_id = fav_info.retinue_id();
		if (partner_id <= 0)
		{
			return false;
		}
		//if (!pImp->IsRetinueFriend(partner_id))
		//{
		//// 还没有这个伙伴
		//return false;
		//}
		if (!contains(pCfg->fav_partners(gender), partner_id))
		{
			return false;
		}
	}

	// 检测通过，构建数据
	PB::retinue_ss_status_info pb;
	pb.set_status_id(pCfg->status_id);
	pb.set_gender(gender);
	for (auto& comment_kv : comment_map)
	{
		for (auto& comment : comment_kv.second)
		{
			pb.add_comment_line_nodes(comment.comment_id);
			auto pb_comment = pb.add_comment_nodes();
			pb_comment->set_comment_id(comment.comment_id);
			pb_comment->set_timestamp(comment.timestamp);
			pb_comment->set_pre_comment_id(comment.pre_comment_id);
		}
	}
	for (auto& fav_kv : fav_map)
	{
		pb.add_fav_nodes()->CopyFrom(fav_kv.second);
	}
	int status_id = pCfg->status_id;
	_retinue_status[status_id] = pb;
	ConsumeSsStatus(status_id);
	return true;
}

void player_retinue_manager::CreateStatusResult(int retcode, int status_id, int64_t moment_id)
{
	auto pCfg = retinue_template_manager::GetInstance().GetRetinueSssConfig(status_id);
	if (pCfg && contains(_retinue_status, status_id))
	{
		if (retcode == 0)
		{
			// success
			// add moment_id
			int now = gmatrix::GetInstance().GetSysTime();
			auto& status = _retinue_status[status_id];
			status.set_moment_id(moment_id);
			status.set_start_time(now);
			int gender  = status.gender();

			// add amity
			std::map<int/*retinue_tid*/, int/*add_amity*/> add_amities;
			if (pCfg->amity_partner_id > 0 && pCfg->amity_add > 0)
			{
				add_amities[pCfg->amity_partner_id] = pCfg->amity_add;
			}
			for (auto& comment : status.comment_nodes())
			{
				pCfg->SelectCommentAmity(gender, add_amities, comment.comment_id());
			}
			SLOG(TRACE, "retinue_ss_status")
			.P("roleid", _imp->Parent()->ID.id)
			.P("function", "create_status")
			.P("status_id", status_id)
			.P("cps_id", moment_id)
			.P("action", "add_amity")
			.P("data", add_amities);
			for (auto& kv : add_amities)
			{
				auto retinue = GetRetinueFriend(kv.first);
				if (!retinue)
				{
					continue;
				}
				retinue->AddAmity(kv.second);
				NotifyRetinueInfo(NULL, PB::gp_retinue_data_notify::N_TYPE_AMITY_CHANGE, kv.first, retinue->GetAmity());
				InteractLog(kv.first, IL_TYPE_STATUS, kv.second);
			}
			if (pCfg->partner_id > 0 && !contains(add_amities, pCfg->partner_id))
			{
				InteractLog(pCfg->partner_id, IL_TYPE_STATUS, 0);
			}
			status.set_first_amity_added(1);//标识已经发放了伙伴好友度
			for (auto& comment : *status.mutable_comment_nodes())
			{
				comment.set_has_reward(1);//标志已经发放了伙伴好友度
			}

			if (CheckStatusFinish(gender, pCfg, status))
			{
				FinishStatus(status_id);
			}
			else
			{
				CalcStatusNotifyTime(status, _status_notify_time[status_id]);
				CalcNotifyTime(now);
			}
		}
		else
		{
			_retinue_status.erase(status_id);
			AddRetinueSsStatus(status_id);
		}
	}
}

void FormatExt1(int gender, const retinue_ss_status_config *pCfg, const PB::retinue_ss_status_info& status, std::string& ext1)
{
	// 时间|发送者id|对谁进行回复|评论id$
	std::stringstream ss;
	for (auto& comment : status.comment_nodes())
	{
		int comment_id = comment.comment_id();
		auto pCommentCfg = pCfg->GetComment(gender, comment_id);
		if (!pCommentCfg)
		{
			continue;
		}
		int pre_comment_id = comment.pre_comment_id();
		int target_id = 0;
		if (pre_comment_id == 0/*server_init*/ || pre_comment_id == -1/*client_init*/)
		{
			target_id = -1;
		}
		else
		{
			auto pPreCommentCfg = pCfg->GetComment(gender, pre_comment_id);
			if (!pPreCommentCfg)
			{
				continue;
			}
			target_id = pPreCommentCfg->partner_id;
		}
		ss << comment.timestamp() << "|" << pCommentCfg->partner_id << "|" << target_id << "|" << comment_id << "$";
	}
	ext1 = ss.str();
}

void FormatExt2(const retinue_ss_status_config *pCfg, const PB::retinue_ss_status_info& status, std::string& ext2)
{
	//时间|点赞者id$
	std::stringstream ss;
	for (auto& fav : status.fav_nodes())
	{
		ss << fav.timestamp() << "|" << fav.retinue_id() << "$";
	}
	ext2 = ss.str();
}

bool player_retinue_manager::CheckAndFixMomentId(const retinue_ss_status_config *pCfg, PB::gp_social_space_op& cmd)
{
	int status_id = pCfg->status_id;
	if (!contains(_retinue_status, status_id))
	{
		return false;// 正常逻辑不会到这里
	}

	int64_t moment_id = cmd.moment_id();
	auto& status = _retinue_status[status_id];
	if (status.moment_id() == 0 && status.first_amity_added() == 0)
	{
		if (moment_id > 0)
		{
			// 服务器没有moment_id而客户端有,同时服务器的初始友好度还没有发放
			// 极大可能是这条状态发送成功，服务器由于网络的极端条件没有接到CSP成功的反馈
			// 因为CSP那里并没有根据status_id查询moment_id的功能
			// 这里用客户端的moment_id来纠正。
			// 确实还会存在服务器没有接到成功返回，而客户端又传来了错误的moment_id的情况
			// 这种情况误解，最坏的情况就是这条状态不能进行后续评论了

			CreateStatusResult(0/*success*/, status_id, moment_id);
		}
		else
		{
			return false; // 正常逻辑不会到这里
		}
	}

	if (status.moment_id() != moment_id || status.first_amity_added() == 0)
	{
		return false;
	}
	return true;
}

bool player_retinue_manager::TryCommentStatus(const retinue_ss_status_config *pCfg, PB::gp_social_space_op& cmd, std::string& ext1)
{
	if (!CheckAndFixMomentId(pCfg, cmd))
	{
		return false;
	}
	int status_id = pCfg->status_id;
	auto& status = _retinue_status[status_id];

	COMMENT_MAP comment_map;
	for (auto& comment : cmd.update_data())
	{
		int timestamp = comment.timestamp();
		comment_info newInfo;
		newInfo.comment_id = comment.comment_id();
		newInfo.pre_comment_id = comment.pre_comment_id();
		if (comment.pre_comment_id() == -1)
		{
			newInfo.pre_comment_id = 0; // 起始评论客户端用-1，服务器用0
		}
		newInfo.timestamp = timestamp;
		comment_map[timestamp].push_back(newInfo);
	}
	// 验证评论
	std::unordered_set<int> comment_line_nodes;
	for (int comment_id : status.comment_line_nodes())
	{
		comment_line_nodes.insert(comment_id);
	}
	int ret = 0;
	if ((ret = CheckCommentList(status.gender(), pCfg, comment_map, comment_line_nodes, false/*not_init*/)) != 0)
	{
		SLOG(ERR, "comment_status_check_comment_fail")
		.P("ret", ret)
		.P("roleid", _imp->Parent()->ID.id)
		.P("status_id", pCfg->status_id);
		return false;
	}

	for (auto& comment_kv : comment_map)
	{
		for (auto& comment : comment_kv.second)
		{
			status.add_comment_line_nodes(comment.comment_id);
			auto pb_comment = status.add_comment_nodes();
			pb_comment->set_comment_id(comment.comment_id);
			pb_comment->set_timestamp(comment.timestamp);
			pb_comment->set_pre_comment_id(comment.pre_comment_id);
		}
	}
	// 合成字符串
	FormatExt1(status.gender(), pCfg, status, ext1);
	return true;
}
void player_retinue_manager::CommentStatusResult(int retcode, int status_id)
{
	auto pCfg = retinue_template_manager::GetInstance().GetRetinueSssConfig(status_id);
	if (pCfg && contains(_retinue_status, status_id))
	{
		auto& status = _retinue_status[status_id];
		if (retcode == 0)
		{
			// success
			// add amity
			int gender = status.gender();
			std::map<int/*retinue_tid*/, int/*add_amity*/> add_amities;
			for (auto& comment : status.comment_nodes())
			{
				if (comment.has_reward() == 0)
				{
					pCfg->SelectCommentAmity(gender, add_amities, comment.comment_id());
				}
			}

			SLOG(TRACE, "retinue_ss_status")
			.P("roleid", _imp->Parent()->ID.id)
			.P("function", "comment_status")
			.P("status_id", status_id)
			.P("action", "add_amity")
			.P("data", add_amities);

			for (auto& kv : add_amities)
			{
				auto retinue = GetRetinueFriend(kv.first);
				if (!retinue)
				{
					continue;
				}
				retinue->AddAmity(kv.second);
				NotifyRetinueInfo(NULL, PB::gp_retinue_data_notify::N_TYPE_AMITY_CHANGE, kv.first, retinue->GetAmity());
				InteractLog(kv.first, IL_TYPE_STATUS, kv.second);
			}
			if (pCfg->partner_id > 0 && !contains(add_amities, pCfg->partner_id))
			{
				InteractLog(pCfg->partner_id, IL_TYPE_STATUS, 0);
			}
			for (auto& comment : *status.mutable_comment_nodes())
			{
				comment.set_has_reward(1);//标志已经发放了伙伴好友度
			}
			if (CheckStatusFinish(gender, pCfg, status))
			{
				FinishStatus(status_id);
			}
			else
			{
				CalcStatusNotifyTime(status, _status_notify_time[status_id]);
				CalcNotifyTime();
			}
		}
		else
		{
			// 移除所有尝试添加的评论
			PB::retinue_ss_status_info pb;
			for (auto& comment : status.comment_nodes())
			{
				if (comment.has_reward() == 1)
				{
					pb.add_comment_nodes()->CopyFrom(comment);
				}
			}
			status.clear_comment_nodes();
			status.mutable_comment_nodes()->CopyFrom(pb.comment_nodes());
		}
	}
}
void player_retinue_manager::FinishStatus(int status_id)
{
	if (contains(_retinue_status, status_id))
	{
		auto& status = _retinue_status[status_id];
		status.set_finish_time(gmatrix::GetInstance().GetSysTime());
		status.clear_comment_line_nodes(); //这些信息已经没用了，清除掉节省空间
		status.clear_comment_nodes(); //这些信息已经没用了，清除掉节省空间
		_finish_status_map[status.moment_id()] = status;
		_finish_ss_status.push_back(&(_finish_status_map[status.moment_id()]));
		_finish_ss_status_ids.insert(status.status_id());
		_retinue_status.erase(status_id);
	}
}

bool IsSelfFav(const PB::retinue_ss_status_info *pStatus)
{
	if (pStatus->fav_nodes().size() == 0)
	{
		return false;
	}
	auto fav = pStatus->fav_nodes(pStatus->fav_nodes().size() - 1);
	return fav.retinue_id() == 0;
}


bool player_retinue_manager::TryFavStatus(const retinue_ss_status_config *pCfg, PB::gp_social_space_op& cmd, std::string& ext2)
{
	int status_id = pCfg->status_id;
	if (contains(_retinue_status, status_id) && !CheckAndFixMomentId(pCfg, cmd))
	{
		return false;
	}
	int64_t moment_id = cmd.moment_id();
	PB::retinue_ss_status_info *pStatus = NULL;
	if (StatusInUse(status_id))
	{
		pStatus = &_retinue_status[status_id];
	}
	else if (MomentFinished(moment_id))
	{
		pStatus = &_finish_status_map[moment_id];
	}

	if (!pStatus)
	{
		return false;
	}

	if (IsSelfFav(pStatus)) // 自己已经点赞
	{
		// 取消点赞
		pStatus->mutable_fav_nodes()->RemoveLast();
	}
	else
	{
		auto new_fav = pStatus->add_fav_nodes();
		new_fav->set_retinue_id(0);
		new_fav->set_timestamp(gmatrix::GetInstance().GetSysTime());
	}
	// 合成字符串
	FormatExt2(pCfg, *pStatus, ext2);
	return true;
}
bool player_retinue_manager::FavStatusResult(int retcode, int status_id, int64_t moment_id)
{
	auto pCfg = retinue_template_manager::GetInstance().GetRetinueSssConfig(status_id);
	if (pCfg && contains(_retinue_status, status_id))
	{
		PB::retinue_ss_status_info *pStatus = NULL;
		if (StatusInUse(status_id))
		{
			pStatus = &_retinue_status[status_id];
		}
		else if (MomentFinished(moment_id))
		{
			pStatus = &_finish_status_map[moment_id];
		}
		if (!pStatus)
		{
			return false;
		}
		bool curIsSelfFav = IsSelfFav(pStatus);
		if (retcode == 0)
		{
			// success
			return curIsSelfFav;
		}
		else
		{
			// fail, revert pre change
			if (curIsSelfFav) // 自己已经点赞
			{
				// 取消点赞
				pStatus->mutable_fav_nodes()->RemoveLast();
			}
			else
			{
				auto new_fav = pStatus->add_fav_nodes();
				new_fav->set_retinue_id(0);
				new_fav->set_timestamp(gmatrix::GetInstance().GetSysTime());
			}
			return !curIsSelfFav;
		}
	}
	return false;
}
void player_retinue_manager::DebugRetinue(int debug, int param1, int param2)
{
	switch (debug)
	{
	case 1:
	{
		_next_ex_task_time = 0;
		break;
	}
	case 2:
	{
		_next_main_skill_time = 1;
		break;
	}
	case 3:
	{
		int chat_id = param1;
		AddActiveChat(chat_id);
		NotifyRetinueInfo(NULL, PB::gp_retinue_data_notify::N_TYPE_CHAT_ACTIVE, chat_id);
	}
	break;
	case 4:
	{
		player_retinue_friend *pfriend = GetRetinueFriend(param1);
		if (pfriend)
		{
			pfriend->AddAmity(param2);
			NotifyRetinueInfo(NULL, PB::gp_retinue_data_notify::N_TYPE_AMITY_CHANGE, param1, pfriend->GetAmity());
		}
	}
	break;
	case 5:
	{
		AddRetinueFriend(param1);
		break;
	}
	case 6:
	{
		player_retinue_friend *pfriend = GetRetinueFriend(param1);
		if (pfriend)
		{
			pfriend->GetData().set_mood(param2);
			NotifyRetinueInfo(NULL, PB::gp_retinue_data_notify::N_TYPE_MOOD_CHANGE, param1, param2);
		}
	}
	break;
	case 7:
	{
		for (int i = 0; i < param2; ++i)
		{
			int ret = RecruitRetinue(param1);
			if (ret != 0)
			{
				_imp->error_message(1, 1, ret);
				return;
			}
		}
	}
	break;
	case 8:
	{
		_recruit.pay_last = param1;
		break;
	}
	case 9:
	{
		gplayer_imp *pImp = GetPlayerImp();
		if (pImp)
		{
			std::stringstream ss;
			ss << _special_free_recruit_count << ";" << _special_pay2_recruit_count << ";" << _special_pay_recruit_count << "; ";
			ss << "pay_total:" << _recruit.pay_total << ";"; //伙伴付费招募总次数
			ss << "free_total:" << _recruit.free_total << ";";
			ss << "pay_6_total:" << _recruit.pay_6_total << ";";
			ss << "pay_last:" << _recruit.pay_last << ";";
			pImp->DebugUTF8Say(ss.str().c_str());
		}
	}
	break;
	case 10:
	{
		auto retinue = GetRetinue(param1);
		if (retinue && GetPlayerImp())
		{
			gplayer_imp *pImp = GetPlayerImp();
			retinue->DeactiveSkill(pImp);
			retinue->Dettach(pImp);
			retinue->GetData().set_awake_active(true);
			retinue->GetData().set_awake_level(param2);
			retinue->RebuildProp(pImp);
			retinue->Attach(pImp);
			retinue->ActiveSkill(pImp);
			NotifyRetinueInfo(retinue, PB::gp_retinue_data_notify::N_TYPE_AWAKE);
		}
	}
	break;

	default:
		return;
	}
}
void player_retinue_manager::UpdateStarCounter()
{
	_star_counter.Update(_retinues.begin(), _retinues.end(), [](decltype(_retinues.begin()) iter)
	{
		return iter->second->GetStar();
	});
}

void player_retinue_manager::UpdateMaxQuality(const player_retinue *pRetinue)
{
	if (pRetinue)
	{
		if (pRetinue->GetQuality() > _max_quality)
		{
			_max_quality = pRetinue->GetQuality();
		}
		return;
	}
	for (auto it = _retinues.begin(); it != _retinues.end(); ++it)
	{
		if (it->second->GetQuality() > _max_quality)
		{
			_max_quality = it->second->GetQuality();
		}
	}
}

void player_retinue_manager::AddRetinueAmity(int tid, int offset)
{
	player_retinue_friend *pfriend = GetRetinueFriend(tid);
	if (pfriend)
	{
		pfriend->AddAmity(offset);
		NotifyRetinueInfo(NULL, PB::gp_retinue_data_notify::N_TYPE_AMITY_CHANGE, tid, pfriend->GetAmity());
	}
}

void player_retinue_manager::CalcStatusNotifyTime(const PB::retinue_ss_status_info& status, std::map<int/*timestamp*/, int/*comment_id*/>& result)
{
	int status_id = status.status_id();
	auto pCfg = retinue_template_manager::GetInstance().GetRetinueSssConfig(status_id);
	if (!pCfg)
	{
		return ;
	}
	std::unordered_set<int> comment_line_nodes;
	for (int comment_id : status.comment_line_nodes())
	{
		comment_line_nodes.insert(comment_id);
	}

	auto IsAllCommentGroupUsed = [&comment_line_nodes](const std::vector<std::vector<int>>& groups) -> bool
	{
		for (auto& group : groups)
		{
			bool group_used = false;
			for (int comment_id : group)
			{
				if (contains(comment_line_nodes, comment_id))
				{
					group_used = true;
					break;
				}
			}
			if (group.size() > 0 && !group_used) // 还有的评论组没有触发
			{
				// 已状态发布的时间为通知时间点
				return false;
			}
		}
		return true;
	};

	// 检查状态的起始评论是否都已经触发了
	if (!IsAllCommentGroupUsed(pCfg->first_comment_group(status.gender())))
	{
		// 还有起始评论没有触发
		// 状态的发布时间为通知时间点, 考虑到发状态时的程序执行时间,刚刚发的评论记录的start_time可能比basetime早1s
		result[status.start_time()] = 0;
	}

	// 评论的每一个后续评论分组是否都已经触发了
	for (auto& comment : status.comment_nodes())
	{
		int comment_id = comment.comment_id();
		auto pCommentCfg = pCfg->GetComment(status.gender(), comment_id);
		if (!pCommentCfg)
		{
			continue;
		}
		if (!IsAllCommentGroupUsed(pCommentCfg->continue_group))
		{
			// 还有后续评论分组没有触发, 取最小的时间戳就是最近的通知时间点
			result[comment.timestamp()] = comment_id;
		}
	}
}

void player_retinue_manager::CalcNotifyTime(int now)
{
	if (now == 0)
	{
		now = gmatrix::GetInstance().GetSysTime();
	}
	int notify_time = 0;
	std::vector<int> status_ids_to_del;
	for (auto& kv : _status_notify_time)
	{
		int status_id = kv.first;
		// kv.second std::map<int/*timestamp*/,int/*comment_id*/>
		auto itorTime = kv.second.lower_bound(now);
		if (itorTime == kv.second.end())
		{
			status_ids_to_del.push_back(status_id);
		}
		else
		{
			if (notify_time == 0 || notify_time > itorTime->first)
			{
				notify_time = itorTime->first;
			}
		}
	}
	for (int id : status_ids_to_del)
	{
		_status_notify_time.erase(id);
	}
	if (notify_time > 0)
	{
		last_ss_need_notify_time = notify_time;
	}
}

void player_retinue_manager::SendPlayerSsNotify()
{
	PB::gp_social_space_op_re notify;
	notify.set_op(PB::SS_OP_ACTION_WATI_NOTIFY);
	notify.set_retcode(0);
	_imp->Runner()->QuietSend<S2C::CMD::PBS2C>(notify);
	last_ss_notify_time = gmatrix::GetInstance().GetSysTime();
	notified_after_login = true;
}

void player_retinue_manager::ConfirmSsNotify(gplayer_imp *imp, int confirm_type)
{
	if (confirm_type == 1) // confirm ss_status_content notify
	{
		last_ss_notify_confirm_time = gmatrix::GetInstance().GetSysTime();
		CalcNotifyTime();
	}
	else if (confirm_type == 2) // confirm active ss_status notify
	{
		imp->SetServerSwitch(SERVER_SWITCH_INDEX_RETINUE_ACTIVE_SSS_CONFIRMED, true);
	}
}

void player_retinue_manager::DebugPrintCurStatusIds()
{
	gplayer_imp *pImp = GetPlayerImp();
	if (pImp)
	{
		std::stringstream ss;
		ss << "当前还未结束的状态id列表:[";
		for (auto& status_kv : _retinue_status)
		{
			ss << status_kv.first << ",";
		}
		ss << "]";
		pImp->DebugUTF8Say(ss.str().c_str());
	}
}

void player_retinue_manager::DebugPrintCurStatusCommentIds(int status_id)
{
	std::stringstream ss;
	gplayer_imp *pImp = GetPlayerImp();
	if (!pImp)
	{
		return;
	}
	auto it = _retinue_status.find(status_id);
	if (it == _retinue_status.end())
	{
		ss << "当前没有id=" << status_id << "的状态";
	}
	else
	{
		ss << "当前id=" << status_id << "的状态，评论列表[";
		auto& status = it->second;
		for (int comment_id : status.comment_line_nodes())
		{
			ss << comment_id << ",";
		}
		ss << "], 二次确认[";
		for (auto& comment : status.comment_nodes())
		{
			ss << comment.comment_id() << ",";
		}
		ss << "]";

	}
	pImp->DebugUTF8Say(ss.str().c_str());
}
void player_retinue_manager::MakeInfoLog(std::stringstream& allRetinue, std::stringstream& combatRetinues, std::stringstream& awakeRetinues)
{
	// 伙伴tid,等级,品质,星级,好感度,时装id,时装颜色#羁绊技1,羁绊技2...#私有物1,私有物2...#时装1,时装颜色1,时装2,时装颜色2...
	for (auto& kv : _retinues)
	{
		int retinue_tid = kv.first;
		auto *retinue = kv.second;
		allRetinue << retinue_tid << "," << (int)retinue->GetLevel() << "," << (int)retinue->GetQuality()
		           << "," << (int)retinue->GetStar() << "," <<  GetRetinueAmity(retinue_tid) << "," << retinue->GetData().privates_size()
		           << "," << retinue->GetData().awake_level() << "#";
		//for (int skill_id : retinue->GetData().relation_skill())
		//{
		//	allRetinue << skill_id << ",";
		//}
		//allRetinue << "#";
		//for (int item_id : retinue->GetData().privates())
		//{
		//allRetinue << item_id << ",";
		//}
		//allRetinue << "#";
		//for (const auto& fashion : retinue->GetData().fashions())
		//{
		//	allRetinue << fashion.fashion_index() << "," << fashion.active_color_plan() << ",";
		//}
		allRetinue << ";";
	}
	std::vector<int> combatVec(ASSIST_COMBAT_RETINUE_NUM + 1, 0);
	for (auto& combatKv : _combat_retinues)
	{
		if (combatKv.second._retinue)
		{
			combatVec[combatKv.first] = combatKv.second._retinue->GetTID();
		}
	}
	GNET::AppendLogValue(combatRetinues, combatVec, ';');
	GNET::AppendLogValue(awakeRetinues, _awake_retinues, ';');
}

std::pair<int, int> player_retinue_manager::GetRetinueMaxAmitySpecial()
{
	int tid = 0;
	int amity = 0;
	for (auto& kv : _retinue_friends)
	{
		auto it = GLOBAL_CONFIG.wedding_retinue_tid.find(kv.first);
		if (it == GLOBAL_CONFIG.wedding_retinue_tid.end())
		{
			continue;
		}

		if (kv.second->GetAmity() >= amity)
		{
			amity = kv.second->GetAmity();
			tid = kv.first;
		}
	}
	return std::make_pair(tid, amity);
}

void player_retinue_manager::InteractLog(int retinue_id, INTERACT_LOG_TYPE type, int add_amity)
{
	gplayer_imp *pImp = GetPlayerImp();
	if (!pImp)
	{
		return;
	}
	// for BI
	BI_LOG_GLOBAL(pImp->GetParent()->account.ToStr());
	SLOG(FORMAT, "retinue_interact")
	.BI_HEADER2_GS(pImp)
	.P("prof", pImp->GetProf())
	.P("retinue_tid", retinue_id)
	.P("type", (int)type)
	.P("add_amity", add_amity)
	.P("total_amity", GetRetinueAmity(retinue_id));
}

void player_retinue_manager::UpdateRetinueAttachFightCapacity(int retinue_id, int attach_fight_capacity)
{
	auto it = _retinue_attach_fightcapacity.find(retinue_id);
	if (it == _retinue_attach_fightcapacity.end())
	{
		_total_attach_fightcapacity += attach_fight_capacity;
		_retinue_attach_fightcapacity[retinue_id] = attach_fight_capacity;
	}
	else
	{
		_total_attach_fightcapacity += (attach_fight_capacity - it->second);
		it->second = attach_fight_capacity;
	}
}

void player_retinue_manager::GetCombatRetinueFightCapacity(int& team_fc, int& main_fc)
{
	main_fc = 0;
	team_fc = 0;
	for (auto& kv : _combat_retinues)
	{
		int retinue_id = kv.second._retinue->GetTID();
		int retinue_fc = _retinue_attach_fightcapacity[retinue_id];
		int slot = kv.second._retinue->CombatSlot();
		if (slot == MAIN_COMBAT_RETINUE_SLOT)
		{
			main_fc = retinue_fc;
		}
		team_fc += retinue_fc;
	}
}

void player_retinue_manager::GetAwakeRetinueTotalInfo(int& fc, int& total_level)
{
	fc = 0;
	total_level = 0;
	for (auto retinue : _awake_retinues)
	{
		int retinue_fc = _retinue_attach_fightcapacity[retinue];
		fc += retinue_fc;
		auto pRetinue = GetRetinue(retinue);
		if (pRetinue)
		{
			total_level += pRetinue->GetData().awake_level();
		}
	}
}

int player_retinue_manager::GetRetinueTotalLevel()
{
	int level = 0;
	for (auto& it : _retinues)
	{
		level += it.second->GetLevel();
	}
	return level;
}

void player_retinue_manager::FixOldRecruit()
{
	if (_recruit_10_mask_old >> 31 == 0)
	{
		_recruit_10_mask_old &= 0xC0000007;
		_recruit_10_mask_old |= (1 << 31);
	}
	if (!(_recruit_10_mask_old & 0x40000000))
	{
		if (!retinue_template_manager::GetInstance().IsInRecruitWhitelist(_imp->GetParent()->ID.id))
		{
			_recruit_10_mask_old &= ~0x04; //第3位清0
			LOG_TRACE("player_retinue_manager::RecruitReset roleid=%ld", _imp->GetParent()->ID.id);
		}
		_recruit_10_mask_old |= 0x40000000;
	}
}

void player_retinue_manager::FixDelayRetinueEvent()
{
	if (_imp->TestCoolDown(CI_RETINUE_FIX_DELAY_EVENT))
	{
		auto *pImp = GetPlayerImp();
		auto triggerEvent = [this, pImp](const retinue_event_config * pConfig, int event_key)
		{
			//日记解锁只需要判断是否有伙伴
			auto *pFriend = GetRetinueFriend(pConfig->diary_retinue);
			if (pFriend && !pFriend->IsDiaryUnlock(pConfig->unlock_diary))
			{
				int now = gmatrix::GetInstance().GetSysTime();
				pFriend->UnlockDiary(pConfig->unlock_diary, now);
				//通知客户端结果
				NotifyRetinueInfo(NULL, PB::gp_retinue_data_notify::N_TYPE_UNLOCK_DIARY, pConfig->diary_retinue, pConfig->unlock_diary, now);
				LOG_TRACE("player_retinue_manager::EventTrigger UnlockDairy retinue_id=%d:diary=%d", pConfig->diary_retinue, pConfig->unlock_diary);
			}
			//触发概率判定
			bool suc = false;
			if (abase::RandUniform() < pConfig->ratio)
			{
				int trigger_chat = 0;
				int trigger_ss = 0;
				retinue_template_manager::GetInstance().GetEventTriggerResult(pImp, pConfig, trigger_chat, trigger_ss);
				if (trigger_chat > 0)
				{
					AddActiveChat(trigger_chat);
					NotifyRetinueInfo(NULL, PB::gp_retinue_data_notify::N_TYPE_CHAT_ACTIVE, trigger_chat);
					suc = true;
				}
				if (trigger_ss > 0)
				{
					AddRetinueSsStatus(trigger_ss );
					suc = true;
				}
				if (suc)
				{
					SLOG(FORMAT, "retinue_trigger_event").P("role", _imp->Parent()->ID.id).P("chat", trigger_chat).P("ss", trigger_ss);
				}
			}

			if (suc && pConfig->group_id == 0)
			{
				AddTriggerEvent(event_key);
			}
		};
		auto foreachEvent = [this, &triggerEvent](const std::map<int, retinue_event_config>& eventMap, int event_type, const std::function<bool(int)>& tid_check)
		{
			for (const auto& eventKv : eventMap)
			{
				int event_key = GET_RETINUE_EVENT_KEY(event_type, eventKv.first);
				if (eventKv.second.group_id == 0 && !IsEventTrigger(event_key) && tid_check(eventKv.first))
				{
					triggerEvent(&eventKv.second, event_key);
				}
			}
		};
		foreachEvent(RETINUE_TEMPL._task_accept_map, RE_TASK_ACCEPT, [pImp](int tid) -> bool { return (*pImp->GetTaskGuard())->HasTask(tid); });
		foreachEvent(RETINUE_TEMPL._task_finish_map, RE_TASK_FINISH, [pImp](int tid) -> bool { return (*pImp->GetTaskGuard())->HaveFinishedTask(tid); });
		foreachEvent(RETINUE_TEMPL._achievement_map, RE_ACHIEVEMENT, std::bind(&gplayer_imp::HasAchievement, pImp, std::placeholders::_1));

		_imp->SetCoolDown(CI_RETINUE_FIX_DELAY_EVENT, CT_RETINUE_FIX_DELAY_EVENT);
	}
}

int player_retinue_manager::GetStar6Num()
{
	int num = 0;
	for (auto& a : _retinues)
	{
		if (a.second && a.second->GetStar() >= 6)
		{
			++num;
		}
	}
	return num;
}

float player_retinue_manager::GetStar6QualityAverage()
{
	int num = 0;
	int quality = 0;
	for (auto& a : _retinues)
	{
		if (a.second && a.second->GetStar() >= 6)
		{
			++num;
			quality += a.second->GetQuality();
		}
	}
	if (num == 0)
	{
		return 0;
	}
	return (float)quality / num;
}

float player_retinue_manager::GetStar6LevelAverage()
{
	int num = 0;
	int level = 0;
	for (auto& a : _retinues)
	{
		if (a.second && a.second->GetStar() >= 6)
		{
			++num;
			level += a.second->GetLevel();
		}
	}
	if (num == 0)
	{
		return 0;
	}
	return (float)level / num;
}

void player_retinue_manager::RetinueSolutionOp(const PB::gp_retinue_operate& cmd, bool finish_check/* = false*/)
{
	gplayer_imp *pImp = GetPlayerImp();
	if (!pImp)
	{
		return;
	}

	if (!GNET::FuncSwitchManager::Instance().getFuncSwitch(kFuncCodeRetinue))
	{
		return;
	}

	if (!GNET::FuncSwitchManager::Instance().getFuncSwitch(kFuncCodeRetinueSolution))
	{
		return;
	}

	switch (cmd.oper())
	{
	case PB::gp_retinue_operate::OT_SOLUTION_UNLOCK:
	{
		RetinueSolutionOp_Unlock(cmd);
	}
	break;
	case PB::gp_retinue_operate::OT_SOLUTION_SWITCH:
	{
		RetinueSolutionOp_Switch(cmd);
	}
	break;
	case PB::gp_retinue_operate::OT_SOLUTION_SAVE:
	{
		RetinueSolutionOp_Save(cmd, finish_check);
	}
	break;
	case PB::gp_retinue_operate::OT_SOLUTION_REQ_DATA:
	{
		RetinueSolutionOp_ReqData(cmd);
	}
	break;
	default:
	{
		LOG_TRACE("player_retinue_manager::RetinueSolutionOp::roleid=" FMT_I64", unkown op=%d", pImp->GetRoleID(), (int)cmd.oper());
	}
	break;
	}
}

void player_retinue_manager::RetinueSolutionOp_Unlock(const PB::gp_retinue_operate& cmd)
{
	gplayer_imp *pImp = GetPlayerImp();
	if (!pImp)
	{
		return;
	}

	int err_code = 0;
	int money_num = 0;
	int money_type = 0;
	do
	{
		if (!cmd.has_solution_index() || cmd.solution_index() < GLOBAL_CONFIG.partner_solution_free_num || cmd.solution_index() >= GLOBAL_CONFIG.partner_solution_max_num)
		{
			err_code = S2C::ERR_RETINUE_SOLUTION_INVALID_INDEX;
			break;
		}

		if (cmd.solution_index() >=  _retinue_solutions.solutions_size())
		{
			err_code = S2C::ERR_RETINUE_SOLUTION_INVALID_INDEX;
			break;
		}

		if (pImp->IsCombatState())
		{
			err_code = S2C::ERR_RETINUE_SOLUTION_COMBAT_STATE;
			break;
		}

		if (GetNextSkillTime() > 0)
		{
			err_code = S2C::ERR_RETINUE_SOLUTION_COMBAT_STATE;
			break;
		}

		int solution_index = cmd.solution_index();
		auto solution_ptr = _retinue_solutions.mutable_solutions(solution_index);
		if (solution_ptr->state() == PB::RETINUE_SOLUTION_STATE::RSS_UNLOCK)
		{
			err_code = S2C::ERR_RETINUE_SOLUTION_ALREADY_UNLOCK;
			break;
		}

		auto iter = GLOBAL_CONFIG.partner_solution_unlock_cfg.find(solution_index);
		if (iter == GLOBAL_CONFIG.partner_solution_unlock_cfg.end())
		{
			err_code = S2C::ERR_RETINUE_SOLUTION_INVALID_INDEX;
			break;
		}

		money_num = iter->second.money_num;
		money_type = iter->second.money_type;
		if (!pImp->CheckMoneyAndCash((EXP_SHOPSELL_MODE)money_type, money_num))
		{
			err_code = S2C::ERR_RETINUE_SOLUTION_MONEY_NOT_ENOUGH;
			break;
		}

		FuncInfo func_info{kFuncCodeRetinue, solution_index};
		pImp->CostMoneyAndCash((EXP_SHOPSELL_MODE)money_type, func_info, money_num);

		solution_ptr->set_state(PB::RETINUE_SOLUTION_STATE::RSS_UNLOCK);
		_retinue_solutions.set_cur_index(solution_index);
		for (int retinue_index = 0; retinue_index < solution_ptr->retinue_id_size(); ++retinue_index)
		{
			RemoveCombatRetinue(retinue_index);
		}
	}
	while (false);

	PB::gp_retinue_op_re request_re;
	request_re.set_op(cmd.oper());
	request_re.set_solution_index(cmd.solution_index());
	request_re.set_retcode(err_code);
	pImp->Runner()->QuietSend<S2C::CMD::PBS2C>(request_re);
	LOG_TRACE("player_retinue_manager::RetinueSolutionOp_Unlock::roleid=" FMT_I64", solution_index=%d, cost_money_type=%d, cost_money_num=%d, retcode=%d", pImp->GetRoleID(), (int)cmd.solution_index(), money_type, money_num, err_code);
}

void player_retinue_manager::RetinueSolutionOp_Switch(const PB::gp_retinue_operate& cmd)
{
	gplayer_imp *pImp = GetPlayerImp();
	if (!pImp)
	{
		return;
	}

	int err_code = 0;
	int old_solution_index = 0;
	do
	{
		if (!cmd.has_solution_index() || cmd.solution_index() < 0 || cmd.solution_index() >= GLOBAL_CONFIG.partner_solution_max_num)
		{
			err_code = S2C::ERR_RETINUE_SOLUTION_INVALID_INDEX;
			break;
		}

		if (cmd.solution_index() >=  _retinue_solutions.solutions_size())
		{
			err_code = S2C::ERR_RETINUE_SOLUTION_INVALID_INDEX;
			break;
		}

		old_solution_index = _retinue_solutions.cur_index();
		int solution_index = cmd.solution_index();
		if (solution_index == old_solution_index)
		{
			err_code = S2C::ERR_RETINUE_SOLUTION_SAME_INDEX;
			break;
		}

		if (pImp->IsCombatState())
		{
			err_code = S2C::ERR_RETINUE_SOLUTION_COMBAT_STATE;
			break;
		}

		if (GetNextSkillTime() > 0)
		{
			err_code = S2C::ERR_RETINUE_SOLUTION_COMBAT_STATE;
			break;
		}

		auto solution_ptr = _retinue_solutions.mutable_solutions(solution_index);
		if (solution_ptr->state() == PB::RETINUE_SOLUTION_STATE::RSS_LOCK)
		{
			err_code = S2C::ERR_RETINUE_SOLUTION_LOCK;
			break;
		}

		for (int retinue_index = 0; retinue_index < solution_ptr->retinue_id_size(); ++retinue_index)
		{
			int retinue_id = solution_ptr->retinue_id(retinue_index);
			if (retinue_id > 0)
			{
				if (retinue_index > 0 && !(_assist_combat_unlock_mask & 1 << (retinue_index - 1)))
				{
					err_code = S2C::ERR_RETINUE_SOLUTION_INDEX_LOCK;
					break;
				}

				player_retinue *retinue = GetRetinue(retinue_id);
				if (!retinue)
				{
					err_code = S2C::ERR_RETINUE_SOLUTION_RETINUE_NOT_COLLECTED;
					break;
				}
			}
		}

		if (err_code)
		{
			break;
		}

		auto old_solution_ptr = _retinue_solutions.mutable_solutions(old_solution_index);
		if (old_solution_ptr != NULL)
		{
			for (int retinue_index = 0; retinue_index < old_solution_ptr->retinue_id_size(); ++retinue_index)
			{
				RemoveCombatRetinue(retinue_index);
			}
		}

		_retinue_solutions.set_cur_index(solution_index);
		for (int retinue_index = 0; retinue_index < solution_ptr->retinue_id_size(); ++retinue_index)
		{
			int retinue_id = solution_ptr->retinue_id(retinue_index);
			SelectCombatRetinue(retinue_id, retinue_index);
		}
	}
	while (false);

	PB::gp_retinue_op_re request_re;
	request_re.set_op(cmd.oper());
	request_re.set_solution_index(cmd.solution_index());
	request_re.set_retcode(err_code);
	pImp->Runner()->QuietSend<S2C::CMD::PBS2C>(request_re);
	LOG_TRACE("player_retinue_manager::RetinueSolutionOp_Switch::roleid=" FMT_I64", old_solution_index=%d, new_solution_index=%d, retcode=%d", pImp->GetRoleID(), old_solution_index, (int)cmd.solution_index(), err_code);
}

void player_retinue_manager::RetinueSolutionOp_Save(const PB::gp_retinue_operate& cmd, bool finish_check/* = false*/, int check_result/* = 0*/)
{
	gplayer_imp *pImp = GetPlayerImp();
	if (!pImp)
	{
		return;
	}

	int err_code = 0;
	do
	{
		if (check_result)
		{
			err_code = S2C::ERR_RETINUE_SOLUTION_INVALID_NAME;
			break;
		}

		if (!cmd.has_solution_index() || cmd.solution_index() < 0 || cmd.solution_index() >= GLOBAL_CONFIG.partner_solution_max_num)
		{
			err_code = S2C::ERR_RETINUE_SOLUTION_INVALID_INDEX;
			break;
		}

		if (cmd.solution_index() >= _retinue_solutions.solutions_size())
		{
			err_code = S2C::ERR_RETINUE_SOLUTION_INVALID_INDEX;
			break;
		}

		if (!cmd.has_solution_name() || cmd.solution_name().empty() || cmd.solution_name().length() > MAX_RETINUE_SOLUTION_NAME_LEN)
		{
			err_code = S2C::ERR_RETINUE_SOLUTION_INVALID_NAME;
			break;
		}

		int solution_index = cmd.solution_index();
		auto solution_ptr = _retinue_solutions.mutable_solutions(solution_index);
		if (solution_ptr->state() == PB::RETINUE_SOLUTION_STATE::RSS_LOCK)
		{
			err_code = S2C::ERR_RETINUE_SOLUTION_LOCK;
			break;
		}

		if (!finish_check)
		{
			GNET::Octets pb_data = GNET::PB2Octets(cmd);
			PB::ipt_gs_check_bad_word proto;
			proto.set_roleid(pImp->GetParent()->ID.id);
			proto.set_check_type(PB::ipt_gs_check_bad_word::CHECK_PARTNER_SOLUTION);
			proto.set_msg(cmd.solution_name());
			proto.set_other_data(pb_data.begin(), pb_data.size());

			GSP::GetInstance().SendDSMsg(pImp->Parent()->ID.id, proto);
			return;
		}
		else
		{
			// 这边是整体拷贝还是只设置名字
			solution_ptr->set_name(cmd.solution_name());
		}
	}
	while (false);

	PB::gp_retinue_op_re request_re;
	request_re.set_op(cmd.oper());
	request_re.set_solution_index(cmd.solution_index());
	request_re.set_solution_name(cmd.solution_name());
	request_re.set_retcode(err_code);
	pImp->Runner()->QuietSend<S2C::CMD::PBS2C>(request_re);
	LOG_TRACE("player_retinue_manager::RetinueSolutionOp_Save::roleid=" FMT_I64", solution_index=%d, solution_name=%s, retcode=%d", pImp->GetRoleID(), (int)cmd.solution_index(), cmd.solution_name().c_str(), err_code);
}

void player_retinue_manager::RetinueSolutionOp_ReqData(const PB::gp_retinue_operate& cmd)
{
	gplayer_imp *pImp = GetPlayerImp();
	if (!pImp)
	{
		return;
	}

	int err_code = 0;
	SyncSolution2Client();

	PB::gp_retinue_op_re request_re;
	request_re.set_op(cmd.oper());
	request_re.set_retcode(err_code);
	pImp->Runner()->QuietSend<S2C::CMD::PBS2C>(request_re);
}

void player_retinue_manager::SyncSolution2Client()
{
	gplayer_imp *pImp = GetPlayerImp();
	if (!pImp)
	{
		return;
	}

	PB::gp_retinue_notify notify;
	std::string str_equip = "cur_equip:solution" + std::to_string(_retinue_solutions.cur_index()) + ":";
	for (int retinue_index = 0; retinue_index <= ASSIST_COMBAT_RETINUE_NUM; ++retinue_index)
	{
		int retinue_id = 0;
		auto iter_retinue = _combat_retinues.find(retinue_index);
		if (iter_retinue != _combat_retinues.end())
		{
			retinue_id = iter_retinue->second._retinue->GetTID();
		}
		str_equip += std::to_string(retinue_index) + "=" + std::to_string(retinue_id) + ", ";
	}

	std::string str = "";
	for (int solutions_index = 0; solutions_index < _retinue_solutions.solutions_size(); ++solutions_index)
	{
		auto solutions_ptr = notify.add_solutions();
		solutions_ptr->CopyFrom(_retinue_solutions.solutions(solutions_index));
		str += "solution" + std::to_string(solutions_index) + ": name=" + solutions_ptr->name() + ", ";
		for (int retinue_index = 0; retinue_index < solutions_ptr->retinue_id_size(); ++retinue_index)
		{
			str += std::to_string(retinue_index) + "=" + std::to_string(solutions_ptr->retinue_id(retinue_index)) + ", ";
		}
	}
	notify.set_cur_solution_index(_retinue_solutions.cur_index());
	LOG_TRACE("player_retinue_manager::retinue solution roleid=" FMT_I64", %s, %s", pImp->GetRoleID(), str.c_str(), str_equip.c_str());
	pImp->Runner()->QuietSend<S2C::CMD::PBS2C>(notify);
}

void player_retinue_manager::MakeRoleTradeRetinueInfo(PB::role_trade_retinue_info&  info)
{
	for (auto it = _retinues.begin(); it != _retinues.end(); ++it)
	{
		if (it->second)
		{
			auto *p_add = info.add_retinues();
			p_add->set_id(it->first);
			p_add->set_lv(it->second->GetLevel());
			p_add->set_score(it->second->GetFightCapacity());
			p_add->set_quality(it->second->GetQuality());
		}
	}
}

void  player_retinue_manager::IDIPResetQuality(int retinue_id, int quality)
{
	LOG_TRACE("player_retinue_manager::IDIPResetQuality::player=%ld,retinue_id=%d quality=%d.", _imp->GetParent()->ID.id, retinue_id, quality);
	player_retinue *retinue = GetRetinue(retinue_id);
	if (!retinue)
	{
		LOG_TRACE("player_retinue_manager::IDIPResetQuality::player=%ld,retinue_id=%d is  not found.", _imp->GetParent()->ID.id, retinue_id);
		return;
	}
	if (retinue->IsAwake())
	{
		retinue->GetData().clear_awake_level();
		retinue->GetData().set_awake_active(false);
	}
	int quality_limit = std::min(retinue->GetTemplate()->max_quality, retinue_template_manager::GetInstance().GetRetinueUpgradeLimit(_imp->GetLevel()));
	if (quality > quality_limit)
	{
		quality = quality_limit;
		LOG_TRACE("player_retinue_manager::IDIPResetQuality::roleid=%ld:retinue_id=%d:quality=%d over limit", _imp->Parent()->ID.id, retinue->GetTID(), quality);
	}
	if (quality < retinue->GetTemplate()->init_quality)
	{
		quality = retinue->GetTemplate()->init_quality;
		LOG_TRACE("player_retinue_manager::IDIPResetQuality::roleid=%ld:retinue_id=%d:quality=%d, lower limit", _imp->Parent()->ID.id, retinue->GetTID(), quality);
	}

	retinue->DeactiveSkill(_imp);
	retinue->Dettach(_imp);

	if (retinue->IsAwake())
	{
		retinue->GetData().clear_awake_level();
		retinue->GetData().set_awake_active(false);
	}

	_awake_retinues.erase(retinue_id);
	retinue->SetQuality(quality);
	retinue->RebuildProp(_imp);
	retinue->Attach(_imp);
	retinue->ActiveSkill(_imp);

	UpdateMaxQuality(retinue);

	gplayer_imp *pImp = GetPlayerImp();
	if (pImp)
	{
		pImp->GetAchievement().OnRetinueQualityChange(pImp);
	}
	SendRetinue(retinue);
	return;
}
void  player_retinue_manager::IDIPResetLevel(int retinue_id, int level)
{
	LOG_TRACE("player_retinue_manager::IDIPModifyLevel::player=%ld,retinue_id=%d level=%d", _imp->GetParent()->ID.id, retinue_id, level);
	player_retinue *retinue = GetRetinue(retinue_id);
	if (!retinue)
	{
		LOG_TRACE("player_retinue_manager::IDIPModifyLevel::player=%ld,retinue_id=%d is  not found.", _imp->GetParent()->ID.id, retinue_id);
		return;
	}
	if (level < retinue->GetTemplate()->init_level)
	{
		level = retinue->GetTemplate()->init_level;
		LOG_TRACE("player_retinue_manager::IDIPModifyLevel::player=%ld,retinue_id=%d,lower level=%d, limit.", _imp->GetParent()->ID.id, retinue_id, level);
	}

	int level_limit = std::min(retinue->GetTemplate()->level_limit, retinue_template_manager::GetInstance().GetRetinueLevelLimit(_imp->GetLevel()));
	if (level >= level_limit)
	{
		level = level_limit;
		LOG_TRACE("player_retinue_manager::IDIPModifyLevel::player=%ld,retinue_id=%d,over level=%d, limit.", _imp->GetParent()->ID.id, retinue_id, level_limit);
	}

	retinue->Dettach(_imp);
	retinue->SetLevel(level);
	retinue->RebuildProp(_imp);
	retinue->Attach(_imp);
	SendRetinue(retinue);

	gplayer_imp *pImp = GetPlayerImp();
	if (pImp)
	{
		pImp->GetAchievement().OnRetinueLevelChange(pImp);
	}

}

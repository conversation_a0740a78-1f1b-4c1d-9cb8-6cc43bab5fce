#include "player_guard.h"
#include "slog.h"
#include "gprotoc/gp_guard_next_phase_prop_result.pb.h"
#include "gprotoc/gp_guard_star_slot_levelup_result.pb.h"
#include "gprotoc/gp_guard_house_op.pb.h"
#include "gprotoc/gp_guard_select_gfx.pb.h"
#include "gprotoc/role_complicated_data_guard.pb.h"
#include "gprotoc/gp_result_notify.pb.h"
#include "gprotoc/gp_guard_break_result.pb.h"
#include "gprotoc/gp_guard_divorce_result.pb.h"
#include "gprotoc/role_complicated_data.pb.h"
#include "gprotoc/gp_guard_generation_update.pb.h"
#include "gprotoc/gp_guard_breed_update.pb.h"
#include "gprotoc/gp_guard_star_levelup_result.pb.h"
#include "gprotoc/gp_item_combine_advance.pb.h"
#include "gprotoc/gp_guard_name_change.pb.h"
#include "gprotoc/gp_guard_select_gfx_notify.pb.h"
#include "gprotoc/gp_guard_star_levelup.pb.h"
#include "gprotoc/gp_guard_star_slot_levelup.pb.h"
#include "gprotoc/gp_guard_prop_update.pb.h"
#include "gprotoc/gp_guard_next_phase_prop.pb.h"
#include "gprotoc/gp_guard_data.pb.h"
#include "gprotoc/gp_guard_select_skin_notify.pb.h"
#include "gprotoc/gp_item_combine.pb.h"
#include "gprotoc/player_guard_toplist.pb.h"
#include "gprotoc/gp_guard_change_shape.pb.h"
#include "gprotoc/player_guard_essence.pb.h"
#include "gprotoc/data_EnhanceBaseProp.pb.h"
#include "gprotoc/data_GuardAttachProp.pb.h"
#include "gprotoc/gp_guard_house_op_re.pb.h"
#include "gprotoc/gp_guard_add_exp_result.pb.h"
#include "gprotoc/ipt_gs_check_bad_word.pb.h"
#include "gprotoc/guard_essence.pb.h"
#include "gprotoc/gp_guard_break.pb.h"
#include "gprotoc/gp_guard_add_exp.pb.h"
#include "gprotoc/gp_guard_breed.pb.h"
#include "gprotoc/gp_guard_marriage_notify.pb.h"
#include "gprotoc/gp_guard_divorce.pb.h"
#include "gprotoc/guard_slot_essence.pb.h"
#include "gprotoc/data_GuardProp.pb.h"
#include "gprotoc/gp_guard_slot_update.pb.h"
#include "gprotoc/guard_client_data.pb.h"
#include "gprotoc/gp_guard_cast_skill.pb.h"
#include "gprotoc/item_info.pb.h"
#include "gprotoc/gp_guard_get.pb.h"
#include "gprotoc/gp_guard_select_skin.pb.h"
#include "gprotoc/gp_guard_summon.pb.h"
#include "gprotoc/gp_guard_change_shape_notice.pb.h"
#include "gprotoc/gp_guard_summon_notice.pb.h"
#include "gprotoc/ipt_guard_breed_notify.pb.h"
#include "gprotoc/role_trade_pet_info.pb.h"

#include "config.h"
#include "item_manager.h"
#include "item/item_guard.h"
#include "player.h"
#include "glog.h"
#include "funcswitchmgr.h"
#include "item/item_general.h"
#include "senior_tower_reward_manager.h"
#include "conv_charset.h"
#include "player_misc.h"
//#include "bilog.h"

#define SKILL_CD_ENTER_COMBAT_MIN 1
#define SKILL_CD_ENTER_COMBAT_MAX 10
#define SKILL_CD_DURING_COMBAT_MIN 15
#define SKILL_CD_DURING_COMBAT_MAX 25
#define ACTIVE_SKILL_DELAY 3
#define DEFAULT_LEVELUP_EXP(level) (level * level * 500)

//--------------------------------------------------------------------------------------------
// guard_template
//
bool guard_template_mgr::Load()
{
	LIST_DATA_BEGIN(ID_SPACE_CONFIG, GUARD_CONFIG, config)
	{
		ASSERT(config.id != 0 && _templates.find(config.id) == _templates.end());

		guard_template& _template = _templates[config.id];
		_template._id = config.id;
		_template._name = config.name;
		_template._init_growth = config.growth;
		_template._proto_level = config.proto;
		_template._prop_level = config.prop_level;
		_template._exp_table_id = config.levelup_exp_id;
		_template._player_prop_type = config.player_prop_index;
		_template._player_prop_ratio = config.player_prop_ratio;
		_template._element = (int)config.element;
		_template._fight_type = (int)config.fight_type;
		_template._level_aptitude = config.level_aptitude;
		_template._growth_aptitude = config.growth_aptitude;
		_template._breed_days = config.breed_days;
		_template._breed_add_star_rate = config.breed_add_star_rate;
		_template._breed_two_star_rate = config.breed_two_star_rate;
		int last_active_skill_id = 0;
		int last_shape = 0;
		for (int i = 0; i < config.phase_config.size() && i < GUARD_PHASE_COUNT; i++)
		{
			const GUARD_PHASE_CONFIG& src_phase_cfg = config.phase_config[i];
			guard_phase_cfg& dst_phase_cfg = _template._phase_cfg[i];

			dst_phase_cfg._break_growth_value = src_phase_cfg.break_value;
			dst_phase_cfg._base_transfer_rate = src_phase_cfg.base_transfer_rate;
			dst_phase_cfg._active_skill_id = src_phase_cfg.active_skill_id;
			if (dst_phase_cfg._active_skill_id > 0)
			{
				last_active_skill_id = dst_phase_cfg._active_skill_id;
			}
			else
			{
				dst_phase_cfg._active_skill_id = last_active_skill_id;
			}
			dst_phase_cfg._shape = (int)src_phase_cfg.shape;
			if (dst_phase_cfg._shape >= last_shape)
			{
				last_shape = dst_phase_cfg._shape;
			}
			else
			{
				dst_phase_cfg._shape = last_shape;
			}

			int index = 0;
#define READ_APTITUDE_CFG(name) \
			if (index < GUARD_APTITUDE_COUNT) { \
			dst_phase_cfg._base_aptitude[index] = src_phase_cfg.aptitude_##name##_base; \
			dst_phase_cfg._max_train_aptitude[index] = src_phase_cfg.aptitude_##name##_max; \
			index++; \
		}

			READ_APTITUDE_CFG(hp);
			READ_APTITUDE_CFG(phy_att);
			READ_APTITUDE_CFG(magic_att);
			READ_APTITUDE_CFG(phy_def);
			READ_APTITUDE_CFG(magic_def);
			READ_APTITUDE_CFG(crit_att);
			READ_APTITUDE_CFG(arp);
			READ_APTITUDE_CFG(dodge);
			READ_APTITUDE_CFG(psy);

			dst_phase_cfg._assist_skill_ratio = src_phase_cfg.assist_skill_ratio;
			std::unordered_map<int, int> break_item_map;
			for (int j = 0; j < GUARD_BREAK_ITEM_COUNT; ++j)
			{
				if (j >= src_phase_cfg.break_cost.size())
				{
					break;
				}
				if (src_phase_cfg.break_cost[j].id <= 0 || src_phase_cfg.break_cost[j].count <= 0)
				{
					continue;
				}
				break_item_map[src_phase_cfg.break_cost[j].id] += src_phase_cfg.break_cost[j].count;
			}
			int item_index = 0;
			for (auto& kv : break_item_map)
			{
				dst_phase_cfg._break_item_cfg[item_index]._item_id = kv.first;
				dst_phase_cfg._break_item_cfg[item_index]._item_count = kv.second;
				if (++item_index >= GUARD_BREAK_ITEM_COUNT)
				{
					break;
				}
			}
			_template._max_growth_value = std::max(dst_phase_cfg._break_growth_value, _template._max_growth_value);

		}
		std::set<int> available_skin_set, available_gfx_set;
		for (int i = 0; i < config.star_config.size(); ++i)
		{
			auto& src_star_level_cfg = config.star_config[i];
			_template._star_level_cfg.push_back(guard_star_level_cfg());
			auto& dst_star_level_cfg = 	_template._star_level_cfg.back();
#define SET_CFG_PROPERTY(NAME) \
			if (src_star_level_cfg.property_add.size() >= index + 1) \
			{ \
				dst_star_level_cfg._property_add.NAME = src_star_level_cfg.property_add[index++]; \
			}
			unsigned int index = 0;
			SET_CFG_PROPERTY(baseHP);
			SET_CFG_PROPERTY(basePhyAtk);
			SET_CFG_PROPERTY(baseMagAtk);
			SET_CFG_PROPERTY(basePhyDef);
			SET_CFG_PROPERTY(baseMagDef);
			SET_CFG_PROPERTY(baseCritLevel);
			SET_CFG_PROPERTY(basePierceLevel);
			SET_CFG_PROPERTY(baseCDReduLevel);
			SET_CFG_PROPERTY(basePsychokinesisLevel);
			/*int *dst_prop = (int *)(void *)&dst_star_level_cfg._property_add;
			for (int j = 0; j < src_star_level_cfg.property_add.size(); ++j)
			{
				dst_prop[j] = src_star_level_cfg.property_add[j];
			}*/
			dst_star_level_cfg._unlock_skin_id = src_star_level_cfg.unlock_skin_id;
			dst_star_level_cfg._unlock_gfx_id = src_star_level_cfg.unlock_gfx_id;
			dst_star_level_cfg._unlock_skill_slot = src_star_level_cfg.unlock_skill_slot;
			if (src_star_level_cfg.unlock_skin_id > 0)
			{
				available_skin_set.insert(src_star_level_cfg.unlock_skin_id);
			}
			if (src_star_level_cfg.unlock_gfx_id > 0)
			{
				available_gfx_set.insert(src_star_level_cfg.unlock_gfx_id);
			}
			dst_star_level_cfg._available_skin_set = available_skin_set;
			dst_star_level_cfg._available_gfx_set = available_gfx_set;
		}
		if (config.auto_get_level > 0)
		{
			_auto_get_map.insert(std::make_pair(config.auto_get_level, config.id));
		}
		ASSERT(_template._exp_table_id > 0);
		auto iter = _guard_exp_map.find(_template._exp_table_id);
		if (iter != _guard_exp_map.end())
		{
			continue;
		}
		auto& exp_table = _guard_exp_map[_template._exp_table_id];
		auto iter_src = CONVEX_MAP(PLAYER_LEVELEXP_CONFIG).find(_template._exp_table_id);
		ASSERT(iter_src != CONVEX_MAP(PLAYER_LEVELEXP_CONFIG).end());
		const PLAYER_LEVELEXP_CONFIG *plevel = iter_src->second.get();
		ASSERT(plevel);
		for (size_t i = 0; i <= (size_t)MAX_LEVEL; ++i)
		{
			uint64_t exp = i < plevel->exp.size() ? (uint64_t)plevel->exp[i] : DEFAULT_LEVELUP_EXP(i);
			exp_table.push_back(exp);
		}
	}
	LIST_DATA_END

	using LEVEL_GROWTH_MAP = std::unordered_map<int, int>;
	LEVEL_GROWTH_MAP _level_growth_map;

	memset(_star_slot_element, 0, sizeof(_star_slot_element));
	for (int i = 0; i < GUARD_STAR_SLOT_COUNT; ++i)
	{
		auto ptempl = GUARD_STAR_SLOT_CONFIG::get(SERVER_CONFIG.guard_star_slot_config_id[i]);
		if (!ptempl)
		{
			continue;
		}
		_star_slot_element[i] = ptempl->guard_element;
	}

	LuaTableNode root;
	try
	{
		if (!LuaConfReader::parseFromFile("./config/script/guard_sev_func.lua", "_G", root))
		{
			return false;
		}
		GROWTH_WEIGHT_MAP temp_map;
		root["growth"].Fetch(temp_map);
		for (auto& kv : temp_map)
		{
			_total_growth_weight += kv.second;
			_growth_weight_map[_total_growth_weight] = kv.first;
		}
		root["standard_level_growth"].Fetch(_standard_level_growth);
	}
	catch (std::exception& e)
	{
		LOG_TRACE("guard_template_mgr load script exception:%s\n", e.what());
		return false;
	}
	catch (...)
	{
		LOG_TRACE("guard_template_mgr load script failed!\n");
		return false;
	}
	return true;
}

int guard_template_mgr::GetGrowth(int new_level, int current_growth)
{
	int rand = abase::Rand(1, _total_growth_weight);
	auto iter_growth = _growth_weight_map.lower_bound(rand);
	if (iter_growth == _growth_weight_map.end())
	{
		return 0;
	}
	int growth = current_growth + iter_growth->second;
	auto iter = _standard_level_growth.find(new_level);
	if (iter == _standard_level_growth.end())
	{
		return growth;
	}
	int standard_growth = iter->second;
	return std::max(growth, standard_growth);
}

//--------------------------------------------------------------------------------------------
// guard_info
//
guard_info::guard_info(gcreature_imp *imp, guard_template *templ)
	: _templ(templ), _self_fight_capacity(0), _attach_fight_capacity(0), _base_attach_fight_capacity(0)
	, _slot_index(-1), _active_skill_id(0), _attached(false)
	, _passive_skill_prop(GUARD_PROP_COUNT, 0.0f)
{
	ASSERT(_templ);
	memset(_assist_skill_id, 0, sizeof(_assist_skill_id));
	memset(_assist_skill_fighttype, 0, sizeof(_assist_skill_fighttype));
}

void guard_info::OnGenerate()
{
	SetLevel(_templ->_init_level);
	int assist_count = 0, passive_count = 0;
	SERVER_CONFIG.GetUnlockSkillCount(0, assist_count, passive_count);
	for (int i = 0; i < GUARD_PASSIVE_SKILL_SLOT_COUNT; ++i)
	{
		if (i < passive_count)
		{
			GetEssence().add_passive_skill_id(0);
		}
		else
		{
			GetEssence().add_passive_skill_id(-1);
		}
	}
	for (int i = 0; i < GUARD_ASSIST_SKILL_SLOT_COUNT; ++i)
	{
		if (i < assist_count)
		{
			GetEssence().add_assist_skill_id(0);
		}
		else
		{
			GetEssence().add_assist_skill_id(-1);
		}
	}
	GetEssence().set_growth_value(_templ->_init_growth);

	int nature = abase::Rand(1, SERVER_CONFIG.guard_nature_count);
	GetEssence().set_nature(nature);
	int born_time = gmatrix::GetInstance().GetSysTime();
	GetEssence().set_born_time(born_time);

	InitGeneration();
}

void guard_info::InitGeneration()
{
	if (GetEssence().star_level() > 0)
	{
		return;
	}
	GetEssence().set_star_level(1);
	GetEssence().set_generation(1);
}

float guard_info::GetAptitude(int index) const
{
	const guard_phase_cfg *phase_cfg = GetPhaseCfg();
	if (!phase_cfg)
	{
		return 0;
	}
	if (index < 0 || index >= GUARD_APTITUDE_COUNT)
	{
		return 0;
	}
	float base_aptitude = phase_cfg->_base_aptitude[index];
	int level_aptitude = GetLevel() * _templ->_level_aptitude;
	int growth_aptitude = (GetGrowth() - _templ->_init_growth) * _templ->_growth_aptitude;
	return base_aptitude + level_aptitude + growth_aptitude;
}

float guard_info::GetBaseTransferRate() const
{
	const guard_phase_cfg *phase_cfg = GetPhaseCfg();
	if (!phase_cfg)
	{
		return 0;
	}
	return phase_cfg->_base_transfer_rate;
}

int guard_info::GetPassiveSkillBook(int slot) const
{
	if (slot < 0 || slot >= GetEssence().passive_skill_id_size())
	{
		return -1;
	}
	return GetEssence().passive_skill_id(slot);
}

void guard_info::SetPassiveSkillBook(int slot, int skill_id)
{
	if (slot < 0 || slot >= GetEssence().passive_skill_id_size())
	{
		return;
	}
	GetEssence().set_passive_skill_id(slot, skill_id);
}

int guard_info::GetAssistSkillBook(int slot) const
{
	if (slot < 0 || slot >= GetEssence().assist_skill_id_size())
	{
		return -1;
	}
	return GetEssence().assist_skill_id(slot);
}

void guard_info::SetAssistSkillBook(int slot, int skill_id)
{
	if (slot < 0 || slot >= GetEssence().assist_skill_id_size())
	{
		return;
	}
	GetEssence().set_assist_skill_id(slot, skill_id);
}

bool guard_info::IsAssistSkillUsed(int skill_id) const
{
	for (int i = 0; i < GetEssence().assist_skill_id_size(); i++)
	{
		if (GetEssence().assist_skill_id(i) == skill_id)
		{
			return true;
		}
	}
	return false;
}

int guard_info::GetAssistSkillID(int slot, int& fight_type) const
{
	if (slot < 0 || slot >= GUARD_ASSIST_SKILL_SLOT_COUNT)
	{
		return 0;
	}
	fight_type = _assist_skill_fighttype[slot];
	return _assist_skill_id[slot];
}

int guard_info::ChangeSkill(gcreature_imp *imp, bool assist, int skill_slot, item_index_t item_index, int& skill_id)
{
	if (!imp || !imp->IsPlayerClass())
	{
		return S2C::ERR_SERVICE_UNAVILABLE;
	}
	gplayer_imp *pPlayer = (gplayer_imp *)imp;
	item_list& backpack = pPlayer->GetInventory().GetInventory(GNET::IL_BACKPACK);
	if (item_index < 0 || item_index >= backpack.Size())
	{
		return S2C::ERR_NOT_ENOUGH_MATERIAL;
	}
	item *it = backpack[item_index];
	if (!it || it->GetTemplateType() != ITT_GUARD_SKILL)
	{
		return S2C::ERR_NOT_ENOUGH_MATERIAL;
	}
	const item_template_guard_skill *guard_skill_temp = ((item_guard_skill *)it)->GetTemplate();
	if (!guard_skill_temp || guard_skill_temp->GetTID() <= 0)
	{
		return S2C::ERR_NOT_ENOUGH_MATERIAL;
	}
	if (guard_skill_temp->_guard_element != GUARD_ELEM_NONE && guard_skill_temp->_guard_element != GetElement())
	{
		return S2C::ERR_GUARD_SKILL_ELEMENT;
	}
	if (assist != (guard_skill_temp->_skill_category == GUARD_SKILL_ASSIST))
	{
		return S2C::ERR_GUARD_INVALID_SKILL_SLOT;
	}
	int skill_slot_size = assist ? GetEssence().assist_skill_id_size() : GetEssence().passive_skill_id_size();
	if (skill_slot < 0 || skill_slot >= skill_slot_size)
	{
		return S2C::ERR_GUARD_INVALID_SKILL_SLOT;
	}
	if (GetProto() < guard_skill_temp->_guard_proto)
	{
		return S2C::ERR_GUARD_PROTO_LOW;
	}
	if (!assist && skill_slot < GUARD_PASSIVE_SKILL_SLOT_COUNT && guard_skill_temp->_skill_quality == SERVER_CONFIG.guard_star_skill_quality)
	{
		return S2C::ERR_GUARD_INVALID_SKILL_SLOT;
	}

	skill_id = guard_skill_temp->GetTID();
	int old_skill_id = 0;
	for (int i = 0; i < skill_slot_size; ++i)
	{
		int used_skill_id = assist ? GetEssence().assist_skill_id(i) : GetEssence().passive_skill_id(i);
		if (used_skill_id < 0)
		{
			if (i == skill_slot)
			{
				return S2C::ERR_GUARD_SKILL_NOT_OPEN;
			}
		}
		else if (used_skill_id > 0)
		{
			if (used_skill_id == skill_id)
			{
				return S2C::ERR_GUARD_SKILL_CONFLICTS;
			}
			if (i == skill_slot)
			{
				old_skill_id = used_skill_id;
			}
			else if (!assist && guard_template_mgr::Instance().GetSkillType(used_skill_id) == guard_skill_temp->_skill_type)
			{
				return S2C::ERR_GUARD_SKILL_CONFLICTS;
			}
		}
	}
	if (assist)
	{
		GetEssence().set_assist_skill_id(skill_slot, skill_id);
	}
	else
	{
		GetEssence().set_passive_skill_id(skill_slot, skill_id);
	}

	FuncInfo fi = {kFuncCodeGuardChangeSkill, GetTID(), skill_id};
	imp->DecItemAtIndex(GNET::IL_BACKPACK, item_index, 1, fi);
	pPlayer->GetAchievement().OnGuardLearnSkill(pPlayer);

	// for BI
	BI_LOG_GLOBAL(pPlayer->GetParent()->account.ToStr());
	SLOG(FORMAT, "guard_learn_skill")
	.BI_HEADER2_GS(pPlayer)
	.P("tid", GetTID())
	.P("skill_id", skill_id)
	.P("old_skill_id", old_skill_id)
	.P("slot", skill_slot)
	.P("cost_tid", skill_id);
	return 0;
}

void guard_info::CalcProp(gcreature_imp *imp, player_guard *mgr, int slot_index, property_template::data_GuardProp& guard_prop,
                          property_template::data_GuardProp& base_attached_prop, property_template::data_GuardProp& attached_prop,
                          int& self_fight_capacity, int& base_attach_fight_capacity, int& attach_fight_capacity)
{
	const baseprop_t *base_prop = prof_template_manager::GetInstance().Get(GUARD_DEFAULT_PROF, GetPropLevel());
	ASSERT(base_prop);

	guard_prop.baseHP = base_prop->baseHP;
	guard_prop.basePhyAtk = base_prop->basePhyAtk;
	guard_prop.baseMagAtk = base_prop->baseMagAtk;
	guard_prop.basePhyDef = base_prop->basePhyDef;
	guard_prop.baseMagDef = base_prop->baseMagDef;
	guard_prop.baseCritLevel = base_prop->baseCritLevel;
	guard_prop.basePierceLevel = base_prop->basePierceLevel;
	guard_prop.baseCDReduLevel = base_prop->baseCDReduLevel;
	guard_prop.basePsychokinesisLevel = base_prop->basePsychokinesisLevel;

	int propCount = property_template::data_GuardProp::GetCount();
	for (int i = 0; i < propCount; ++i)
	{
		// 0, 1, 2, 3, ... , 8 -> 0, 1, 1, 3, ..., 8
		int aptitude_index = i == 2 ? 1 : i;
		float rate = GetAptitude(aptitude_index) / 10000.0f * (1.0 + _passive_skill_prop[i]);
		creature_prop::ChangePropertyByRate(guard_prop, i, rate);
	}
	auto star_level_cfg = GetStarLevelCfg();
	if (star_level_cfg)
	{
		guard_prop += star_level_cfg->_property_add;
	}

	float base_transfer_rate = GetBaseTransferRate();
	base_attached_prop = guard_prop;
	creature_prop::IncAllPropertyByRate(base_attached_prop, base_transfer_rate);
	base_attach_fight_capacity = CalcFightCapacity(imp, base_attached_prop);

	if (slot_index >= 0
	        && slot_index != GUARD_SLOT_SECONDARY_1_IDX
	        && slot_index != GUARD_SLOT_SECONDARY_2_IDX)
	{
		for (int i = 0; i < GUARD_STAR_SLOT_COUNT; ++i)
		{
			if (guard_template_mgr::Instance().GetStarSlotElement(i) != slot_index)
			{
				continue;
			}
			guard_prop += mgr->GetStarSlotProp(i);
		}

		float transfer_rate = base_transfer_rate * mgr->GetSlotTransferRate(slot_index);
		attached_prop = guard_prop;
		creature_prop::IncAllPropertyByRate(attached_prop, transfer_rate);
		attach_fight_capacity = CalcFightCapacity(imp, attached_prop);
	}
	else
	{
		attached_prop = base_attached_prop;
		attach_fight_capacity = base_attach_fight_capacity;
	}
	self_fight_capacity = CalcFightCapacity(imp, guard_prop);
}

void guard_info::RebuildProp(gcreature_imp *imp, player_guard *mgr)
{
	if (!imp || !mgr)
	{
		return;
	}
	CalcSkillProp();
	CalcProp(imp, mgr, _slot_index, _guard_prop, _base_attached_prop, _attached_prop, _self_fight_capacity, _base_attach_fight_capacity, _attach_fight_capacity);

	creature_prop *prop = NULL;
	if (_slot_index >= 0 && mgr->GetSlot(_slot_index))
	{
		prop = &mgr->GetSlot(_slot_index)->GetProp();
	}
	if (prop)
	{
		prop->Import(&_guard_prop);
		const baseprop_t *base_prop = prof_template_manager::GetInstance().Get(GUARD_DEFAULT_PROF, GetPropLevel());
		if (base_prop)
		{
			prop->SET_PROP_BY_NAME(baseCritRatio, base_prop->baseCritRatio, creature_prop::CPM_BASE);
			prop->Update();
		}
	}

	memset(_assist_skill_id, 0, sizeof(_assist_skill_id));
	memset(_assist_skill_fighttype, 0, sizeof(_assist_skill_fighttype));
	if (_slot_index >= 0)
	{
		const guard_phase_cfg *phase_cfg = GetPhaseCfg();
		_active_skill_id = phase_cfg ? phase_cfg->_active_skill_id : 0;
		for (int i = 0; i < GUARD_ASSIST_SKILL_SLOT_COUNT; ++i)
		{
			int assist_skill_book = GetAssistSkillBook(i);
			if (assist_skill_book > 0)
			{
				_assist_skill_id[i] = guard_template_mgr::Instance().GetAssistSkillID(assist_skill_book, _assist_skill_fighttype[i]);
			}
		}
	}
	else
	{
		_active_skill_id = 0;
	}
	mgr->CheckMaxFightCapacity(imp, this);
}

int guard_info::CalcFightCapacity(gcreature_imp *imp, property_template::data_GuardProp& prop)
{
	int atkType = imp->GetProperty().GetGProperty()->PROP_NAME_GET(atkType);
	// player 战斗力计算的简化版
	int64_t value = 0.025 * (int64_t)prop.baseHP + (1 - (int)(atkType == 2)) * 3 * prop.basePhyAtk +
	                2 * prop.basePhyDef + (1 - (int)(atkType == 1)) * 3 * prop.baseMagAtk +
	                2 * prop.baseMagDef + prop.baseCritLevel + prop.baseCDReduLevel + prop.basePierceLevel + prop.basePsychokinesisLevel;
	value /= 5;
	if (value > INT_MAX)
	{
		return INT_MAX;
	}
	if (value < INT_MIN)
	{
		return INT_MIN;
	}
	return value;
}

bool guard_info::CanMarry()
{
	if (GetProto() <= 1)
	{
		return false;
	}
	if (GetPhase() < GUARD_MAX_PHASE)
	{
		return false;
	}
	if (IsInMarry())
	{
		return false;
	}
	return true;
}

void guard_info::Marry(gplayer_imp *imp, const gplayer_imp *ownerImp, guard_info *pSpouse)
{
	if (!ownerImp || !pSpouse)
	{
		return;
	}
	int now = gmatrix::GetInstance().GetSysTime();
	auto spouse_data = GetEssence().mutable_spouse();
	spouse_data->set_ownerid(ownerImp->GetRoleID());
	spouse_data->set_name(pSpouse->GetEssence().name());
	spouse_data->set_tid(pSpouse->GetTID());
	spouse_data->set_star_level(pSpouse->GetStarLevel());
	spouse_data->set_generation(pSpouse->GetEssence().generation());
	spouse_data->set_skin_id(pSpouse->GetEssence().skin_id());
	spouse_data->set_gfx_id(pSpouse->GetEssence().gfx_id());
	spouse_data->set_begin_time(now);
	spouse_data->set_today_state(PB::GUARD_BREED_NONE);
	spouse_data->clear_cycle_state();
	for (int i = 0; i < GetTemplate()->_breed_days; ++i)
	{
		spouse_data->add_cycle_state(PB::GUARD_BREED_NONE);
	}
	PB::gp_guard_marriage_notify notify;
	notify.set_guard_id(GetTID());
	notify.mutable_spouse()->CopyFrom(*spouse_data);
	imp->Runner()->QuietSend<S2C::CMD::PBS2C>(notify);

	//FOR BI
	BI_LOG_GLOBAL(imp->GetParent()->account);
	SLOG(FORMAT, "guard_marry")
	.BI_HEADER2_GS(imp)
	.P("guard_id", GetTID())
	.P("guard_level", GetLevel())
	.P("guard_star_level", GetStarLevel())
	.P("guard_generation", GetGeneration())
	.P("spouse_owner_openid", GetOpenid(ownerImp->GetParent()->account.ToStr()))
	.P("spouse_owner_roleid", ownerImp->GetRoleID())
	.P("spouse_guard_id", pSpouse->GetTID())
	.P("spouse_guard_level", pSpouse->GetLevel())
	.P("spouse_guard_star_level", pSpouse->GetStarLevel())
	.P("spouse_guard_generation", pSpouse->GetGeneration());
}

int guard_info::Divorce(gplayer_imp *imp, player_guard *mgr)
{
	if (!IsInMarry())
	{
		return S2C::ERR_GUARD_NOT_MARRY;
	}
	if (!IsBreedFinish())
	{
		return S2C::ERR_GUARD_BREED_NOT_FINISH;
	}
	auto spouse_data = GetEssence().mutable_spouse();
	int both_breed_days = 0;
	for (int i = 0; i < spouse_data->cycle_state_size(); ++i)
	{
		int day_state = spouse_data->cycle_state(i);
		if (day_state == PB::GUARD_BREED_BOTH)
		{
			++both_breed_days;
		}
	}
	int inc_star = 0;
	int rand = abase::Rand(1, 100);
	do
	{
		if (both_breed_days == spouse_data->cycle_state_size())
		{
			if (rand <= _templ->_breed_two_star_rate)
			{
				inc_star = 2;
				break;
			}
		}
		if (both_breed_days < _templ->_breed_add_star_rate.size())
		{
			if (rand <= _templ->_breed_add_star_rate[both_breed_days])
			{
				inc_star = 1;
				break;
			}
		}
	}
	while (false);

	int old_star = GetStarLevel();
	int old_skin = GetEssence().skin_id();
	int old_gfx = GetEssence().gfx_id();
	int new_skin = old_skin;
	int new_gfx = old_gfx;
	if (inc_star > 0)
	{
		IncStarLevel(imp, inc_star, new_skin, new_gfx);
	}

	// add history
	GetEssence().clear_history();

	auto history = GetEssence().add_history();
	history->set_star_level(old_star);
	history->set_skin_id(old_skin);
	history->set_gfx_id(old_gfx);
	history->set_spouse_name(spouse_data->name());
	history->set_spouse_generation(spouse_data->generation());
	history->set_spouse_ownerid(spouse_data->ownerid());
	history->set_self_name(GetEssence().name());
	history->set_self_generation(GetGeneration());
	history->set_spouse_star_level(spouse_data->star_level());
	history->set_spouse_skin_id(spouse_data->skin_id());
	history->set_spouse_gfx_id(spouse_data->gfx_id());

	int generation = GetGeneration() + 1;
	GetEssence().set_generation(generation);

	PB::gp_guard_divorce_result result;
	result.set_guard_id(GetTID());
	result.set_star_level(GetStarLevel());
	result.set_generation(GetGeneration());
	result.mutable_history()->CopyFrom(*history);
	imp->Runner()->QuietSend<S2C::CMD::PBS2C>(result);

	if (inc_star > 0)
	{
		Refresh(imp, mgr);
	}

	if (new_skin > 0)
	{
		mgr->SelectSkin(imp, GetTID(), new_skin);
	}

	//FOR BI
	BI_LOG_GLOBAL(imp->GetParent()->account);
	SLOG(FORMAT, "guard_divorce")
	.BI_HEADER2_GS(imp)
	.P("guard_id", GetTID())
	.P("guard_level", GetLevel())
	.P("guard_star_level", GetStarLevel())
	.P("guard_generation", GetGeneration())
	.P("spouse_owner_roleid", spouse_data->ownerid())
	.P("spouse_guard_id", spouse_data->tid())
	.P("breed_days", both_breed_days);

	spouse_data->Clear();
	return 0;
}

void guard_info::SendBreedUpdate(gcreature_imp *imp)
{
	auto& spouse_data = GetEssence().spouse();
	PB::gp_guard_breed_update proto;
	proto.set_guard_id(GetTID());
	proto.set_today_state(spouse_data.today_state());
	proto.mutable_cycle_state()->CopyFrom(spouse_data.cycle_state());
	imp->Runner()->QuietSend<S2C::CMD::PBS2C>(proto);
}

void guard_info::OnLevelUp(gcreature_imp *imp, int new_level)
{
	int new_growth = guard_template_mgr::Instance().GetGrowth(new_level, GetGrowth());
	new_growth = std::min(new_growth, _templ->_max_growth_value);
	SetGrowth(new_growth);
}

void guard_info::IncStarLevel(gcreature_imp *imp, int& delta, int& new_skin, int& new_gfx)
{
	int level = GetStarLevel();
	int i = 0;
	for (; i < delta; ++i)
	{
		if (IsMaxStarLevel())
		{
			break;
		}
		_essence.set_star_level(++level);
		OnStarLevelUp(imp, new_skin, new_gfx);
	}
	delta = i;
}

void guard_info::OnStarLevelUp(gcreature_imp *imp, int& new_skin, int& new_gfx)
{
	auto star_level_cfg = GetStarLevelCfg();
	if (!star_level_cfg)
	{
		return;
	}
	int unlock_skill = star_level_cfg->_unlock_skill_slot;
	int current_count = GetEssence().passive_skill_id_size();
	int total_count = GUARD_PASSIVE_SKILL_SLOT_COUNT + unlock_skill;
	for (int i = current_count; i < total_count; ++i)
	{
		GetEssence().add_passive_skill_id(0);
	}
	if (star_level_cfg->_unlock_skin_id > 0)
	{
		new_skin = star_level_cfg->_unlock_skin_id;
	}
	if (star_level_cfg->_unlock_gfx_id > 0)
	{
		new_gfx = star_level_cfg->_unlock_gfx_id;
	}
}

void guard_info::Refresh(gcreature_imp *imp, player_guard *mgr, bool notify)
{
	Dettach(imp, mgr);
	RebuildProp(imp, mgr);
	Attach(imp, mgr);
	if (notify)
	{
		PB::gp_guard_prop_update proto;
		proto.set_guard_id(GetTID());
		proto.set_self_fight_capacity(_self_fight_capacity);
		proto.set_attach_fight_capacity(_attach_fight_capacity);
		proto.set_base_attach_fight_capacity(_base_attach_fight_capacity);
		ExportProp(proto);
		imp->Runner()->QuietSend<S2C::CMD::PBS2C>(proto);
	}
}

void guard_info::MakeClientData(PB::guard_client_data& client_data)
{
	client_data.mutable_essence()->CopyFrom(GetEssence());
	client_data.set_self_fight_capacity(_self_fight_capacity);
	client_data.set_attach_fight_capacity(_attach_fight_capacity);
	client_data.set_base_attach_fight_capacity(_base_attach_fight_capacity);
	ExportProp(client_data);
}

void guard_info::Attach(gcreature_imp *imp, player_guard *mgr)
{
	if (!imp || !mgr)
	{
		return;
	}
	if (_attached)
	{
		return;
	}
	// prop
	property_template::data_GuardAttachProp& point_prop = *(property_template::data_GuardAttachProp *)&_attached_prop;
	ConditionSuppressCombatStat(imp, SuppressCombatSystem::PET)
		.Then(nullptr, "guard_info::Attach: suppress prop attach")
		.Else([&](gcreature_imp*) {
			imp->GetProperty().IncByStruct(point_prop);
		}, "guard_info::Attach: normal prop attach");

	// skill
	if (_active_skill_id > 0)
	{
				mgr->InsertSkill(imp, _active_skill_id);
		for (int i = 0; i < GUARD_ASSIST_SKILL_SLOT_COUNT; ++i)
		{
			if (_assist_skill_id[i] > 0)
			{
						mgr->InsertSkill(imp, _assist_skill_id[i]);
			}
		}
	}
	_attached = true;
}

void guard_info::Dettach(gcreature_imp *imp, player_guard *mgr)
{
	if (!imp || !mgr)
	{
		return;
	}
	if (!_attached)
	{
		return;
	}
	property_template::data_GuardAttachProp& point_prop = *(property_template::data_GuardAttachProp *)&_attached_prop;
	ConditionSuppressCombatStat(imp, SuppressCombatSystem::PET)
		.Then(nullptr, "guard_info::Dettach: suppress prop detach")
		.Else([&](gcreature_imp*) {
			imp->GetProperty().DecByStruct(point_prop);
		}, "guard_info::Dettach: normal prop detach");
	if (_active_skill_id > 0)
	{
				mgr->RemoveSkill(imp, _active_skill_id);
		for (int i = 0; i < GUARD_ASSIST_SKILL_SLOT_COUNT; ++i)
		{
			if (_assist_skill_id[i] > 0)
			{
						mgr->RemoveSkill(imp, _assist_skill_id[i]);
			}
		}
	}
	_attached = false;
}

void guard_info::CalcSkillProp()
{
	for (int i = 0; i < GUARD_PROP_COUNT; i++)
	{
		_passive_skill_prop[i] = 0;
	}
	for (int i = 0; i < GetEssence().passive_skill_id_size(); i++)
	{
		int skill_id = GetEssence().passive_skill_id(i);
		if (skill_id == 0)
		{
			continue;
		}
		const item *skill_item = item_manager::GetInstance().GetItem(skill_id);
		if (!skill_item || skill_item->GetTemplateType() != ITT_GUARD_SKILL)
		{
			continue;
		}
		const item_template_guard_skill *skill_temp = ((item_guard_skill *)skill_item)->GetTemplate();
		if (!skill_temp)
		{
			continue;
		}
		for (int j = 0; j < GUARD_PROP_COUNT && j < skill_temp->_skill_prop.size(); j++)
		{
			_passive_skill_prop[j] += skill_temp->_skill_prop[j];
		}
	}
}

void guard_info::DebugOutput() const
{
	std::ostringstream str_prop;
	std::ostringstream str_prop_attach;

	int propCount = property_template::data_GuardProp::GetCount();
	int *pGuardProp = (int *)&_guard_prop;
	int *pAttachedProp = (int *)&_attached_prop;
	for (int i = 0; i < propCount; ++i)
	{
		int type;
		int offset = property_template::data_GuardProp::GetOffsetAndType(i, type);
		switch (type)
		{
		case property_template::PT_TYPE_INT:
		{
			str_prop << *(int *)(pGuardProp + offset) << "|";
			str_prop_attach << *(int *)(pAttachedProp + offset) << "|";
		}
		break;
		case property_template::PT_TYPE_FLOAT:
		{
			str_prop << *(float *)(pGuardProp + offset) << "|";
			str_prop_attach << *(float *)(pAttachedProp + offset) << "|";
		}
		break;
		case property_template::PT_TYPE_INT64:
		{
			str_prop << *(int64_t *)(pGuardProp + offset) << "|";
			str_prop_attach << *(int64_t *)(pAttachedProp + offset) << "|";
		}
		break;
		case property_template::PT_TYPE_DOUBLE:
		{
			str_prop << *(double *)(pGuardProp + offset) << "|";
			str_prop_attach << *(double *)(pAttachedProp + offset) << "|";
		}
		break;
		default:
			break;
		}
	}
	SLOG(ERR, "guard_info").P("tid", GetTID()).P("level", GetLevel()).P("exp", GetExp()).P("phase", GetPhase())
	.P("shape", GetShape()).P("growth", GetGrowth())
	.P("prop", str_prop.str()).P("prop_attach", str_prop_attach.str())
	.P("self_fa", _self_fight_capacity).P("attach_fa", _attach_fight_capacity);

}

//--------------------------------------------------------------------------------------------
// guard_slot_info
//
guard_slot_info::guard_slot_info(gcreature_imp *imp) : _prop(imp)
{
}

//--------------------------------------------------------------------------------------------
// player_guard
//
player_guard::player_guard(gcreature_imp *imp)
	: _slots(GUARD_SLOT_COUNT, guard_slot_info(imp)), _min_slot_level(1), _summon_guard(NULL),
	  _max_fight_capacity(0), _max_guard(0), _star_transfer_rate(0.0)
{
	for (int i = 0; i < GUARD_SLOT_COUNT; i++)
	{
		_slots[i].SetLevel(1);
	}

	// 次战位
	_slots[GUARD_SLOT_SECONDARY_1_IDX].SetLevel(0);
	_slots[GUARD_SLOT_SECONDARY_2_IDX].SetLevel(0);

	for (int i = 0; i < GUARD_GROUP_COUNT; ++i)
	{
		_next_skill_time[i] = 0;
		_casting_skill_slot[i] = -1;
		_casting_active_skill[i] = false;
		_skill_delay[i] = 0;
	}
}

void player_guard::Load(gcreature_imp *imp, const PB::player_guard_essence& guard_data)
{
	_marry_guards.clear();
	for (int i = 0; i < guard_data.guards_size(); ++i)
	{
		const PB::guard_essence& essence = guard_data.guards(i);
		tid_t tid = essence.tid();
		auto cfg = guard_template_mgr::Instance().Get(tid);
		if (!cfg)
		{
			SLOG(ERR, "player_guard::AddGuard invalid tid").P("roleid", imp->GetParent()->ID.id).P("guardid", tid);
			continue;
		}
		if (GetGuard(tid))
		{
			SLOG(ERR, "player_guard::Load repeated").P("roleid", imp->GetParent()->ID.id).P("guardid", tid);
			continue;
		}
		auto it = _guards.insert(std::make_pair(tid, guard_info(imp, cfg))).first;
		guard_info& guard = it->second;
		guard.GetEssence().CopyFrom(essence);
		guard.InitGeneration();
		if (guard.IsInMarry())
		{
			_marry_guards.insert(tid);
		}
	}
	_min_slot_level = SERVER_CONFIG.guard_slot_max_level;
	for (int i = 0; i < guard_data.slots_size(); ++i)
	{
		if (i >= GUARD_SLOT_COUNT)
		{
			break;
		}
		_slots[i].GetEssence().CopyFrom(guard_data.slots(i));

		if (_slots[i].GetLevel() < _min_slot_level
		        && i != GUARD_SLOT_SECONDARY_1_IDX && i != GUARD_SLOT_SECONDARY_2_IDX) // 辅战位
		{
			_min_slot_level = _slots[i].GetLevel();
		}

		if (_slots[i].GetID() > 0)
		{
			guard_info *guard = GetGuard(_slots[i].GetID());
			if (guard)
			{
				guard->SetSlotIndex(i);
			}
			else
			{
				_slots[i].SetID(0);
			}
		}
	}
	if (guard_data.has_house())
	{
		_house_data.CopyFrom(guard_data.house());
	}
	else
	{
		_house_data.set_house_level(1);
		auto pHouseTempl = GUARD_ROOM_CONFIG::get(SERVER_CONFIG.guard_room_config_id);
		if (pHouseTempl && pHouseTempl->configs.size() > 0)
		{
			_house_data.mutable_guard_pos()->Clear();
			auto& house_config = pHouseTempl->configs[0];
			for (int i = 0; i < house_config.bed_num; ++i)
			{
				auto pGuard = _house_data.add_guard_pos();
				pGuard->set_guard_tid(0);
			}

			/*gplayer_imp *pImp = dynamic_cast<gplayer_imp *>(imp);
			if (pImp)
			{
				(*pImp->GetTaskGuard())->CheckDeliverTask(SERVER_CONFIG.guard_room_unlock_task_id);
			}*/
		}

	}
	if (guard_data.summon_guard_id() > 0)
	{
		_summon_guard = GetGuard(guard_data.summon_guard_id());
	}
	if (guard_data.has_star() && guard_data.star().level() >= 1)
	{
		_star_data.CopyFrom(guard_data.star());
	}
	else
	{
		_star_data.set_level(1);
		for (int i = 0; i < GUARD_STAR_SLOT_COUNT; ++i)
		{
			auto star_slot = _star_data.add_slot();
			star_slot->set_level(1);
		}
	}
	for (int i = 0; i < GUARD_STAR_SLOT_COUNT; ++i)
	{
		__CalcStarSlotProp(imp, i);
	}
	__CalcStarTransferRate(imp);
	SetParentData(imp);

	UpdatePhaseCount();

	if (_slots[GUARD_SLOT_SECONDARY_1_IDX].GetLevel() <= 1)
	{
		if (SeniorTowerRewardManager::GetInstance().IsGuardSecondarySlotUnlocked(imp, GUARD_SLOT_SECONDARY_1_IDX))
		{
			_slots[GUARD_SLOT_SECONDARY_1_IDX].SetLevel(1);
		}
		else
		{
			_slots[GUARD_SLOT_SECONDARY_1_IDX].SetLevel(0);
		}
	}
	if (_slots[GUARD_SLOT_SECONDARY_2_IDX].GetLevel() <= 1)
	{
		if (SeniorTowerRewardManager::GetInstance().IsGuardSecondarySlotUnlocked(imp, GUARD_SLOT_SECONDARY_2_IDX))
		{
			_slots[GUARD_SLOT_SECONDARY_2_IDX].SetLevel(1);
		}
		else
		{
			_slots[GUARD_SLOT_SECONDARY_2_IDX].SetLevel(0);
		}
	}
}

void player_guard::Save(gcreature_imp *imp, PB::player_guard_essence& guard_data)
{
	guard_data.clear_guards();
	for (auto it = _guards.begin(), ite = _guards.end(); it != ite; ++it)
	{
		guard_info& guard = it->second;
		PB::guard_essence *essence = guard_data.add_guards();
		essence->CopyFrom(guard.GetEssence());
	}
	guard_data.clear_slots();
	for (int i = 0; i < GUARD_SLOT_COUNT; i++)
	{
		auto slot = guard_data.add_slots();
		slot->CopyFrom(_slots[i].GetEssence());
	}
	if (_house_data.has_house_level() && _house_data.house_level() >= 1)
	{
		guard_data.mutable_house()->CopyFrom(_house_data);
	}
	if (_summon_guard)
	{
		guard_data.set_summon_guard_id(_summon_guard->GetTID());
	}
	guard_data.mutable_star()->CopyFrom(_star_data);
}

bool player_guard::MakeTopListData(gcreature_imp *imp, PB::player_guard_toplist& toplist_data)
{
	if (_max_guard == 0)
	{
		return false;
	}
	auto max_guard = GetGuard(_max_guard);
	if (!max_guard)
	{
		return false;
	}
	max_guard->MakeClientData(*toplist_data.mutable_max_guard());
	return true;
}

void player_guard::MakeComplicatedData(gcreature_imp *imp, PB::role_complicated_data_guard& complicated_data)
{
	for (auto it = _guards.begin(), ite = _guards.end(); it != ite; ++it)
	{
		guard_info& guard = it->second;
		//if (guard.GetSlotIndex() >= 0)
		//{
		PB::guard_client_data *guard_data = complicated_data.add_data();
		guard.MakeClientData(*guard_data);
		//}
	}
	complicated_data.mutable_house()->CopyFrom(_house_data);
	for (auto it = _slots.begin(), ite = _slots.end(); it != ite; ++it)
	{
		guard_slot_info& slot = *it;
		PB::guard_slot_essence *slot_essence = complicated_data.add_slots();
		slot_essence->CopyFrom(slot.GetEssence());
	}
	complicated_data.mutable_star()->CopyFrom(_star_data);
}

guard_info *player_guard::GetGuard(tid_t id)
{
	auto it = _guards.find(id);
	if (it == _guards.end())
	{
		return NULL;
	}
	return &it->second;
}

const guard_info *player_guard::GetGuard(tid_t id) const
{
	auto it = _guards.find(id);
	if (it == _guards.end())
	{
		return NULL;
	}
	return &it->second;
}

guard_slot_info *player_guard::GetSlot(int index)
{
	if (index < 0 || index >= _slots.size())
	{
		return NULL;
	}
	return &_slots[index];
}

const guard_slot_info *player_guard::GetSlot(int index) const
{
	if (index < 0 || index >= _slots.size())
	{
		return NULL;
	}
	return &_slots[index];
}

bool player_guard::CanAddGuard(gcreature_imp *imp, tid_t id)
{
	if (!GET_FUNC_SWITCH(kFuncCodeGuardGet))
	{
		return false;
	}
	auto cfg = guard_template_mgr::Instance().Get(id);
	if (!cfg)
	{
		return false;
	}
	if (GetGuard(id))
	{
		return false;
	}
	return true;
}

bool player_guard::CanGetAward(int get_award_ts)
{
	if (get_award_ts != 0)
	{
		int now = gmatrix::GetInstance().GetSysTime();
		bool isSameDay = InSameLocalDay(get_award_ts, now);
		if (isSameDay)
		{
			return false;
		}

		int curHour = GetCurHour(now);
		if (curHour < 20)
		{
			return false;
		}
		return true;
	}
	return true;
}
bool player_guard::AddGuard(gcreature_imp *imp, tid_t id)
{
	gplayer_imp *pImp = reinterpret_cast<gplayer_imp *>(imp);
	CHECK_FUNC_SWITCH_AND_RETURN_FALSE(kFuncCodeGuardGet, pImp);
	auto cfg = guard_template_mgr::Instance().Get(id);
	if (!cfg)
	{
		SLOG(ERR, "player_guard::AddGuard invalid tid")
		.P("roleid", imp->GetParent()->ID.id)
		.P("guardid", id);
		return false;
	}
	if (GetGuard(id))
	{
		SLOG(ERR, "player_guard::Load repeated")
		.P("roleid", imp->GetParent()->ID.id)
		.P("guardid", id);
		return false;
	}
	auto it = _guards.insert(std::make_pair(id, guard_info(imp, cfg))).first;
	guard_info& guard = it->second;
	guard.SetTID(id);
	guard.OnGenerate();
	guard.RebuildProp(imp, this);
	guard.Attach(imp, this);

	PB::gp_guard_get proto;
	guard.MakeClientData(*proto.mutable_data());
	imp->Runner()->QuietSend<S2C::CMD::PBS2C>(proto);

	// for BI
	BI_LOG_GLOBAL(pImp->GetParent()->account.ToStr());
	SLOG(FORMAT, "guard_add")
	.BI_HEADER2_GS(pImp)
	.P("tid", id);

	if (_guards.size() == 1)
	{
		SummonGuard(imp, id, 0);
	}

	if (imp->IsPlayerClass())
	{
		gplayer_imp *pPlayer = (gplayer_imp *)imp;
		pPlayer->GetAchievement().OnGetGuard(pPlayer);
		pPlayer->GetAchievement().OnGetGuardEvent(pPlayer, id);
	}
	return true;
}

bool player_guard::IsPutGuardFurniture(gplayer_imp *imp)
{
	auto *ipd = GetInterProcessData();
	if (!ipd)
	{
		return false;
	}

	int64_t hometown_id = imp->GetHomeTown().GetHometownID();
	if (hometown_id <= 0)
	{
		return false;
	}

	auto& hometown_cfg = hometown_config_manager::GetInstance().GetConfig();
	const std::vector<int>& furniture = hometown_cfg.guard_furniture;
	int ret = ipd->hometown_manager.GetHometowns().lock_and_run<int>(hometown_id,
	          [&furniture](INTERPROCESS::ip_hometown_t *phometown) -> int
	{
		if (!phometown)
		{
			return -1;
		}

		for (int i = 0; i < furniture.size(); ++i)
		{
			auto it_type = phometown->types.find(furniture[i]);
			if (it_type != phometown->types.end() && it_type->second.put_count > 0)
			{
				return 0;
			}
		}
		return -2;
	});

	if (ret == 0)
	{
		return true;
	}

	return false;
}
void player_guard::HandleOnHouseChange(gplayer_imp *imp)
{
	if (!GET_FUNC_SWITCH(kFuncCodeGuardHouse))
	{
		return;
	}

	if (!imp)
	{
		return;
	}

	auto *ipd = GetInterProcessData();
	if (!ipd)
	{
		return;
	}

	int64_t hometown_id = imp->GetHomeTown().GetHometownID();
	if (hometown_id <= 0)
	{
		return;
	}

	ipd->hometown_manager.GetHometowns().PutGuardHouse(hometown_id, _house_data.guard_pos_size());

	for (int i = 0; i < _house_data.guard_pos_size(); ++i)
	{
		int guard_tid = _house_data.guard_pos(i).guard_tid();
		auto it = _guards.find(guard_tid);
		if (it == _guards.end())
		{
			continue;
		}

		GNET::Octets name(it->second.GetEssence().name().data(), it->second.GetEssence().name().size());
		ipd->hometown_manager.GetHometowns().SetGuard(hometown_id,
		        i,
		        guard_tid,
		        it->second.GetShape(),
		        name,
		        it->second.GetGeneration(),
		        it->second.GetSkinID(),
		        it->second.GetGfxID());
	}
}

int player_guard::HandleGuardHouse(gplayer_imp *imp, const PB::gp_guard_house_op& cmd)
{
	if (!GET_FUNC_SWITCH(kFuncCodeGuardHouse))
	{
		return -1;
	}

	if (!imp)
	{
		return -1;
	}

	if (imp->GetLevel() < SERVER_CONFIG.guard_room_unlock_level)
	{
		return S2C::ERR_LEVEL_NOT_MATCH;
	}

	PB::gp_guard_house_op_re pb_re;

	auto pHouseTempl = GUARD_ROOM_CONFIG::get(SERVER_CONFIG.guard_room_config_id);
	if (!pHouseTempl)
	{
		return -1;
	}

	int cur_level = _house_data.house_level();
	switch (cmd.op())
	{
	case PB::gp_guard_house_op::OT_UPGRADE_HOUSE:
	{
		if (cur_level <= 0 || cur_level  > pHouseTempl->configs.size() || cur_level >= SERVER_CONFIG.guard_room_level_max)
		{
			return -1;
		}

		auto& house_config = pHouseTempl->configs[cur_level - 1];
		if (imp->GetLevel() < house_config.player_level)
		{
			return S2C::ERR_LEVEL_NOT_MATCH;
		}

		if (house_config.cost_money > 0 && house_config.cost_money > imp->GetMoney(MT_BIND))
		{
			return S2C::ERR_NOT_ENOUGH_MONEY;
		}

		int next_level = ++cur_level;
		if (next_level > pHouseTempl->configs.size())
		{
			return S2C::ERR_FATAL_ERR;
		}

		auto& next_house_config = pHouseTempl->configs[next_level - 1];

		if (next_house_config.tool_id > 0)
		{
			gen_item_t item;
			item.tid = next_house_config.tool_id;
			item.count = 1;

			gplayer_imp::TidCountMap tidmap;
			tidmap[item.tid] = 1;
			if (!imp->CheckCanIncItems(tidmap))
			{
				return S2C::ERR_PACKAGE_LESS_SLOT;
			}

			imp->GenAndIncItem(IGT_GUARD_HOUSE, {kFuncCodeGuardHouse, item.tid}, item);
		}

		if (_house_data.guard_pos_size() < next_house_config.bed_num)
		{
			for (int i = _house_data.guard_pos_size(); i < next_house_config.bed_num; ++i)
			{
				auto pGuard = _house_data.add_guard_pos();
				pGuard->set_guard_tid(0);
			}

			auto *ipd = GetInterProcessData();
			if (ipd)
			{
				int64_t hometown_id = imp->GetHomeTown().GetHometownID();
				if (hometown_id > 0)
				{
					ipd->hometown_manager.GetHometowns().PutGuardHouse(hometown_id, next_house_config.bed_num);
				}
			}
		}

		if (house_config.cost_money > 0)
		{
			FuncInfo func_info{kFuncCodeGuardHouse};
			imp->DecMoney(func_info, MT_BIND, house_config.cost_money);
		}
		_house_data.set_house_level(next_level);
		pb_re.mutable_guard_pos()->CopyFrom(_house_data.guard_pos());

	}
	break;

	case PB::gp_guard_house_op::OT_PUT_GUARD:
	{
		int index = cmd.put_guard().index();
		if (index >= _house_data.guard_pos_size() || index < 0)
		{
			return -1;
		}

		int guard_tid = cmd.put_guard().guard_tid();
		auto it = _guards.find(guard_tid);
		if (it == _guards.end())
		{
			return -1;
		}

		auto *ipd = GetInterProcessData();
		if (ipd)
		{
			int64_t hometown_id = imp->GetHomeTown().GetHometownID();
			if (hometown_id > 0)
			{
				GNET::Octets name(it->second.GetEssence().name().data(), it->second.GetEssence().name().size());
				ipd->hometown_manager.GetHometowns().SetGuard(hometown_id,
				        index,
				        guard_tid,
				        it->second.GetShape(),
				        name,
				        it->second.GetGeneration(),
				        it->second.GetSkinID(),
				        it->second.GetGfxID());
			}
		}

		auto pGuard = _house_data.mutable_guard_pos(index);
		pGuard->set_guard_tid(guard_tid);
		pb_re.mutable_guard_pos()->CopyFrom(_house_data.guard_pos());
	}
	break;

	case PB::gp_guard_house_op::OT_GIVE_AWARD:
	{
		if (cur_level <= 0 || cur_level  > pHouseTempl->configs.size())
		{
			return -1;
		}

		if (!CanGetAward(_house_data.give_award_ts()))
		{
			return S2C::ERR_MAFIA_WELFARE;
		}

		auto& house_config = pHouseTempl->configs[cur_level - 1];

		_house_data.set_give_award_ts(gmatrix::GetInstance().GetSysTime());
		if (IsPutGuardFurniture(imp))
		{
			imp->DeliverGeneralReward({kFuncCodeGuardHouse}, GRANT_REWARD_TYPE_ARENA_RANK_REWARD, house_config.reward_id, NULL, 0);
		}
		else
		{
			imp->DeliverGeneralReward({kFuncCodeGuardHouse}, GRANT_REWARD_TYPE_ARENA_RANK_REWARD, house_config.reward_id_unlock, NULL, 0);
		}
	}
	break;
	default:
		break;
	}

	pb_re.set_retcode(0);
	pb_re.set_op(cmd.op());
	imp->Runner()->QuietSend<S2C::CMD::PBS2C>(pb_re);
	return 0;
}

int player_guard::RenameGuard(gcreature_imp *imp, tid_t guard_id, const std::string& guard_name)
{
	if (!imp || guard_id == 0)
	{
		return 0;
	}
	guard_info *guard = GetGuard(guard_id);
	if (!guard)
	{
		return S2C::ERR_GUARD_NOT_FOUND;
	}
	if (guard_name.empty() || !GNET::CheckUserContentSize(guard_name, 0, GUARD_NAME_MAX_CHAR_LENGTH))
	{
		return S2C::ERR_GUARD_ILLEGAL_NAME;
	}
	if (guard_name == guard->GetEssence().name())
	{
		return 0;
	}

	if (imp->IsPlayerClass())
	{
		if (((gplayer_imp *)imp)->IsIdipForbidPlayerFunc(kFuncCodeIdipForbidPetName))
		{
			return S2C::ERR_PLAYER_FUNC_FORBID_MODIFY;
		}
	}

	PB::ipt_gs_check_bad_word proto;
	proto.set_roleid(imp->GetParent()->ID.id);
	proto.set_check_type(PB::ipt_gs_check_bad_word::CHECK_GUARD_NAME);
	proto.set_msg(guard_name);
	proto.set_param(guard_id);

	GSP::GetInstance().SendDSMsg(imp->Parent()->ID.id, proto);
	/*
	guard->GetEssence().set_name(guard_name);
	PB::gp_guard_name_change proto;
	proto.set_player_id(imp->GetParent()->ID.id);
	proto.set_guard_id(guard_id);
	proto.set_name(guard_name);
	if (guard == _summon_guard)
	{
		SetParentData(imp);
		imp->Runner()->RegionSend<S2C::CMD::PBS2C>(proto);
	}
	else
	{
		imp->Runner()->QuietSend<S2C::CMD::PBS2C>(proto);
	}
	*/
	return 0;
}
void player_guard::OnRenameGuard(gcreature_imp *imp, tid_t guard_id, const std::string& guard_name)
{
	if (!imp || guard_id == 0)
	{
		return;
	}
	guard_info *guard = GetGuard(guard_id);
	if (!guard)
	{
		return;
	}

	guard->GetEssence().set_name(guard_name);
	PB::gp_guard_name_change proto;
	proto.set_player_id(imp->GetParent()->ID.id);
	proto.set_guard_id(guard_id);
	proto.set_name(guard_name);
	if (guard == _summon_guard)
	{
		SetParentData(imp);
		imp->Runner()->RegionSend<S2C::CMD::PBS2C>(proto);
	}
	else
	{
		imp->Runner()->QuietSend<S2C::CMD::PBS2C>(proto);
	}

	GNET::Octets name((const void *)guard_name.data(), guard_name.size());
	GNET::CharsetConverter::conv_charset_u2t(name, name);
	BI_LOG_GLOBAL(((gplayer_imp *)imp)->GetParent()->account.ToStr());
	SLOG(FORMAT, "guard_rename").BI_HEADER2_GS(((gplayer_imp *)imp)).P("guard_id", guard_id).P("name", StripName( (char *)name.begin(), name.size(), "." ));
}

void player_guard::OnIdipRenameGuard(gcreature_imp *imp, const std::string& guard_name)
{
	for (auto iter = _guards.begin(); iter != _guards.end(); ++iter)
	{
		OnRenameGuard(imp, iter->first, guard_name);
	}
}

int player_guard::ChangeGuardShape(gcreature_imp *imp, tid_t guard_id, int shape)
{
	if (guard_id == 0)
	{
		return 0;
	}
	guard_info *guard = GetGuard(guard_id);
	if (!guard)
	{
		return S2C::ERR_GUARD_NOT_FOUND;
	}
	if (guard->GetShape() == shape)
	{
		return 0;
	}
	const guard_phase_cfg *phase_cfg = guard->GetPhaseCfg();
	if (!phase_cfg || shape > phase_cfg->_shape)
	{
		return S2C::ERR_GUARD_SHAPE_NOT_OWN;
	}
	guard->SetShape(shape);
	PB::gp_guard_change_shape_notice proto;
	proto.set_player_id(imp->GetParent()->ID.id);
	proto.set_guard_id(guard_id);
	proto.set_shape(shape);
	if (guard == _summon_guard)
	{
		SetParentData(imp);
		imp->Runner()->RegionSend<S2C::CMD::PBS2C>(proto);
	}
	else
	{
		imp->Runner()->QuietSend<S2C::CMD::PBS2C>(proto);
	}
	return 0;
}

int player_guard::SummonGuard(gcreature_imp *imp, tid_t guard_id, int shape)
{
	if (!imp)
	{
		return 0;
	}
	int action = 0;
	if (guard_id <= 0)
	{
		if (_summon_guard == NULL)
		{
			return 0;
		}
		int old_guard_id = _summon_guard->GetTID();
		_summon_guard = NULL;
		SetParentData(imp);
		PB::gp_guard_summon_notice proto;
		proto.set_player_id(imp->GetParent()->ID.id);
		proto.set_guard_id(guard_id);
		imp->Runner()->RegionSend<S2C::CMD::PBS2C>(proto);
		guard_id = old_guard_id;
		action = 2;
	}
	else
	{
		if (_summon_guard && guard_id == _summon_guard->GetTID() && shape == _summon_guard->GetShape())
		{
			return 0;
		}
		guard_info *guard = GetGuard(guard_id);
		if (!guard)
		{
			return S2C::ERR_GUARD_NOT_FOUND;
		}
		const guard_phase_cfg *phase_cfg = guard->GetPhaseCfg();
		if (!phase_cfg || shape > phase_cfg->_shape)
		{
			return S2C::ERR_GUARD_SHAPE_NOT_OWN;
		}
		if (imp->IsPlayerClass())
		{
			gplayer_imp *pImp = (gplayer_imp *)imp;
			//召回精灵
			pImp->GetStellarChartManager().SelectSpirit(0);
		}
		guard->SetShape(shape);
		_summon_guard = guard;
		SetParentData(imp);
		PB::gp_guard_summon_notice proto;
		proto.set_player_id(imp->GetParent()->ID.id);
		proto.set_guard_id(guard_id);
		proto.set_guard_shape(guard->GetShape());
		proto.set_guard_name(guard->GetEssence().name());
		proto.set_guard_skin_id(guard->GetSkinID());
		proto.set_guard_gfx_id(guard->GetGfxID());
		proto.set_guard_generation(guard->GetGeneration());
		imp->Runner()->RegionSend<S2C::CMD::PBS2C>(proto);
		action = 1;
	}

	// for BI
	if (imp->IsPlayerClass())
	{
		gplayer_imp *pImp = (gplayer_imp *)imp;
		BI_LOG_GLOBAL(pImp->GetParent()->account.ToStr());
		SLOG(FORMAT, "guard_summon")
		.BI_HEADER2_GS(pImp)
		.P("tid", guard_id)
		.P("action", action);
	}
	return 0;
}

void player_guard::SetParentData(gcreature_imp *imp)
{
	gcreature *creature = imp->GetParent();
	if (_summon_guard)
	{
		creature->SetObjectState(gobject::STATE_GUARD);
		if (creature->ID.IsPlayer())
		{
			((gplayer *)creature)->guard_id = _summon_guard->GetTID();
			((gplayer *)creature)->guard_shape = _summon_guard->GetShape();
			((gplayer *)creature)->guard_skin_id = _summon_guard->GetSkinID();
			((gplayer *)creature)->guard_gfx_id = _summon_guard->GetGfxID();
			((gplayer *)creature)->guard_generation = _summon_guard->GetGeneration();
			const std::string& guard_name = _summon_guard->GetEssence().name();
			unsigned int buff_size = std::min(guard_name.length(), sizeof(((gplayer *)creature)->guard_name));
			((gplayer *)creature)->guard_name_size = buff_size;
			memcpy(((gplayer *)creature)->guard_name, guard_name.c_str(), buff_size);
		}
		else if (creature->CheckObjectState(gobject::STATE_NPC_PLAYER))
		{
			((gplayer_npc *)creature)->guard_id = _summon_guard->GetTID();
			((gplayer_npc *)creature)->guard_shape = _summon_guard->GetShape();
			((gplayer_npc *)creature)->guard_skin_id = _summon_guard->GetSkinID();
			((gplayer_npc *)creature)->guard_gfx_id = _summon_guard->GetGfxID();
			((gplayer_npc *)creature)->guard_generation = _summon_guard->GetGeneration();
			const std::string& guard_name = _summon_guard->GetEssence().name();
			unsigned int buff_size = std::min(guard_name.length(), sizeof(((gplayer_npc *)creature)->guard_name));
			((gplayer_npc *)creature)->guard_name_size = buff_size;
			memcpy(((gplayer_npc *)creature)->guard_name, guard_name.c_str(), buff_size);
		}
	}
	else
	{
		creature->ClrObjectState(gobject::STATE_GUARD);
		if (creature->ID.IsPlayer())
		{
			((gplayer *)creature)->guard_id = 0;
			((gplayer *)creature)->guard_shape = 0;
			((gplayer *)creature)->guard_skin_id = 0;
			((gplayer *)creature)->guard_gfx_id = 0;
			((gplayer *)creature)->guard_generation = 0;
			((gplayer *)creature)->guard_name_size = 0;
			memset(((gplayer *)creature)->guard_name, 0, sizeof(((gplayer *)creature)->guard_name));
		}
		else if (creature->CheckObjectState(gobject::STATE_NPC_PLAYER))
		{
			((gplayer_npc *)creature)->guard_id = 0;
			((gplayer_npc *)creature)->guard_shape = 0;
			((gplayer_npc *)creature)->guard_skin_id = 0;
			((gplayer_npc *)creature)->guard_gfx_id = 0;
			((gplayer_npc *)creature)->guard_generation = 0;
			((gplayer_npc *)creature)->guard_name_size = 0;
			memset(((gplayer_npc *)creature)->guard_name, 0, sizeof(((gplayer_npc *)creature)->guard_name));
		}
	}
}

int player_guard::PutInGuard(gcreature_imp *imp, tid_t id, int slot_index)
{
	guard_info *guard = GetGuard(id);
	if (!guard)
	{
		return S2C::ERR_GUARD_NOT_FOUND;
	}
	if (slot_index < 0 || slot_index >= GUARD_SLOT_COUNT)
	{
		return S2C::ERR_GUARD_WRONG_ELEMENT;
	}

	bool replace = false;
	guard_slot_info& slot = _slots[slot_index];

	if (slot_index == GUARD_SLOT_SECONDARY_1_IDX || slot_index == GUARD_SLOT_SECONDARY_2_IDX)
	{
		if (slot.GetLevel() <= 0)
		{
			return S2C::ERR_GUARD_SLOT_LOCKED; // 次战位尚未解锁
		}
	}
	else
	{
		if (guard->GetElement() != slot_index) // 主站槽位需要判断元素类型
		{
			return S2C::ERR_GUARD_WRONG_ELEMENT;
		}
	}

	int org_guard_id = slot.GetID();
	if (org_guard_id != 0)
	{
		TakeOutGuard(imp, slot_index, false);
		replace = true;
	}
	guard->SetSlotIndex(slot_index);
	slot.SetID(id);
	SendSlotUpdate(imp, slot_index);
	guard->Refresh(imp, this);

	if (imp->IsPlayerClass())
	{
		gplayer_imp *pImp = (gplayer_imp *)imp;
		if (!replace)
		{
			pImp->GetAchievement().OnGuardPutInSlot(pImp);
		}

		// for BI
		BI_LOG_GLOBAL(pImp->GetParent()->account.ToStr());
		SLOG(FORMAT, "put_in_guard")
		.BI_HEADER2_GS(pImp)
		.P("tid", id)
		.P("slot_index", slot_index)
		.P("org_tid", org_guard_id);
	}
	return 0;
}

int player_guard::TakeOutGuard(gcreature_imp *imp, int slot_index, bool by_player)
{
	if (slot_index < 0 || slot_index >= GUARD_SLOT_COUNT)
	{
		return S2C::ERR_GUARD_WRONG_ELEMENT;
	}
	guard_slot_info& slot = _slots[slot_index];
	int guard_id = slot.GetID();
	if (guard_id == 0)
	{
		return S2C::ERR_GUARD_NOT_FOUND;
	}
	guard_info *guard = GetGuard(guard_id);
	if (!guard)
	{
		return S2C::ERR_GUARD_NOT_FOUND;
	}
	guard->SetSlotIndex(-1);
	slot.SetID(0);
	guard->Refresh(imp, this);
	if (by_player)
	{
		SendSlotUpdate(imp, slot_index);
	}
	return 0;
}

int player_guard::GetInSlotCount() const
{
	int count = 0;
	for (auto& slot : _slots)
	{
		if (slot.GetID() > 0)
		{
			count++;
		}
	}
	return count;
}

int player_guard::SlotLevelUp(gcreature_imp *imp, int slot_index)
{
	if (slot_index < 0 || slot_index >= GUARD_SLOT_COUNT)
	{
		return S2C::ERR_GUARD_WRONG_ELEMENT;
	}
	guard_slot_info& slot = _slots[slot_index];
	int slot_level = slot.GetLevel();
	if (slot_level >= SERVER_CONFIG.guard_slot_max_level)
	{
		return S2C::ERR_GUARD_SLOT_MAX_LEVEL;
	}
	int cost_item_id = SERVER_CONFIG.guard_slot_levelup_item_id[slot_index];
	int cost_item_num = SERVER_CONFIG.guard_slot_levelup_item_cost;
	ITEM_CHECK_INFO info;
	if (!imp->CheckItemExistAtBackpack2(info, cost_item_id, cost_item_num))
	{
		return S2C::ERR_NOT_ENOUGH_MATERIAL;
	}
	slot_level++;
	slot.SetLevel(slot_level);
	int new_min_slot_level = SERVER_CONFIG.guard_slot_max_level;
	for (int i = 0; i < _slots.size(); ++i)
	{
		if (i == GUARD_SLOT_SECONDARY_1_IDX || i == GUARD_SLOT_SECONDARY_2_IDX) // 辅战位
		{
			continue;
		}

		if (_slots[i].GetLevel() < new_min_slot_level)
		{
			new_min_slot_level = _slots[i].GetLevel();
		}
	}
	if (new_min_slot_level != _min_slot_level)
	{
		_min_slot_level = new_min_slot_level;
		for (auto it = _slots.begin(), ite = _slots.end(); it != ite; ++it)
		{
			guard_slot_info& each_slot = *it;
			int each_guard_id = each_slot.GetID();
			if (each_guard_id > 0)
			{
				guard_info *each_guard = GetGuard(each_guard_id);
				if (each_guard)
				{
					each_guard->Refresh(imp, this);
				}
			}
		}
	}

	int guard_id = slot.GetID();
	if (guard_id > 0)
	{
		guard_info *guard = GetGuard(guard_id);
		if (guard)
		{
			guard->Refresh(imp, this);
		}
	}
	FuncInfo fi{kFuncCodeGuardSlotLevelUp, guard_id, slot_index};
	imp->DecItem2(info, fi);
	SendSlotUpdate(imp, slot_index);

	// for BI
	if (imp->IsPlayerClass())
	{
		gplayer_imp *pImp = (gplayer_imp *)imp;
		BI_LOG_GLOBAL(pImp->GetParent()->account.ToStr());
		SLOG(FORMAT, "guard_slot_levelup")
		.BI_HEADER2_GS(pImp)
		.P("slot_index", slot_index)
		.P("new_level", slot_level)
		.P("cost_tid", cost_item_id)
		.P("cost_count", cost_item_num);
	}
	return 0;
}

int player_guard::GainExpByItem(gcreature_imp *imp, int guard_id, int item_id, int item_num)
{
	guard_info *guard = GetGuard(guard_id);
	if (!guard)
	{
		return S2C::ERR_GUARD_NOT_FOUND;
	}
	if (item_num <= 0)
	{
		return S2C::ERR_NOT_ENOUGH_MATERIAL;
	}
	if (!imp->CheckItemExistAtBackpack(item_id, item_num))
	{
		return S2C::ERR_NOT_ENOUGH_MATERIAL;
	}
	const item *exp_item = item_manager::GetInstance().GetItem(item_id);
	if (!exp_item || exp_item->GetTemplateType() != ITT_GUARD_EXP)
	{
		return S2C::ERR_NOT_ENOUGH_MATERIAL;
	}
	const item_template_guard_exp *guard_exp_temp = ((item_guard_exp *)exp_item)->GetTemplate();
	if (!guard_exp_temp)
	{
		return S2C::ERR_NOT_ENOUGH_MATERIAL;
	}
	if (guard_exp_temp->_guard_proto < guard->GetProto())
	{
		return S2C::ERR_GUARD_PROTO_LOW;
	}

	exp_t exp_add = (exp_t)guard_exp_temp->_exp * (exp_t)item_num;

	FuncInfo fi{kFuncCodeItemGuardExp, item_id, guard_id};
	int retcode = GainExpForOneGuard(imp, fi, guard_id, exp_add);
	if (retcode != 0)
	{
		return retcode;
	}
	exp_t cost_num = (exp_add + (exp_t)guard_exp_temp->_exp - 1) / (exp_t)guard_exp_temp->_exp;
	imp->DecItemAtBackpack(fi, item_id, (int)cost_num);
	PB::gp_guard_add_exp_result result;
	result.set_guard_id(guard_id);
	result.set_guard_level(guard->GetLevel());
	result.set_guard_exp(guard->GetExp());
	result.set_growth_value(guard->GetGrowth());
	imp->Runner()->QuietSend<S2C::CMD::PBS2C>(result);
	return 0;
}

void player_guard::OnPlayerLogin(gcreature_imp *imp)
{
	if (!imp)
	{
		return;
	}

	OnLevelUp(imp);
	for (auto it = _guards.begin(), ite = _guards.end(); it != ite; ++it)
	{
		guard_info& guard = it->second;
		guard.RebuildProp(imp, this);
		guard.Attach(imp, this);
	}
}

void player_guard::OnEnterScene(gcreature_imp *imp)
{
	if (_summon_guard)
	{
		PB::gp_guard_summon_notice summon_proto;
		summon_proto.set_player_id(imp->GetParent()->ID.id);
		summon_proto.set_guard_id(_summon_guard->GetTID());
		summon_proto.set_guard_shape(_summon_guard->GetShape());
		summon_proto.set_guard_name(_summon_guard->GetEssence().name());
		summon_proto.set_guard_skin_id(_summon_guard->GetSkinID());
		summon_proto.set_guard_gfx_id(_summon_guard->GetGfxID());
		summon_proto.set_guard_generation(_summon_guard->GetGeneration());
		imp->Runner()->QuietSend<S2C::CMD::PBS2C>(summon_proto);
	}
}

void player_guard::SendClient(gcreature_imp *imp)
{
	PB::gp_guard_data proto;
	for (auto it = _guards.begin(), ite = _guards.end(); it != ite; ++it)
	{
		guard_info& guard = it->second;
		PB::guard_client_data *guard_data = proto.add_data();
		guard.MakeClientData(*guard_data);
	}
	proto.mutable_house()->CopyFrom(_house_data);
	for (auto it = _slots.begin(), ite = _slots.end(); it != ite; ++it)
	{
		guard_slot_info& slot = *it;
		PB::guard_slot_essence *slot_essence = proto.add_slots();
		slot_essence->CopyFrom(slot.GetEssence());
	}
	proto.mutable_star()->CopyFrom(_star_data);
	imp->Runner()->QuietSend<S2C::CMD::PBS2C>(proto);
}

void player_guard::OnLevelUp(gcreature_imp *imp)
{
	if (!imp || !imp->IsPlayerClass())
	{
		return;
	}
	int level = imp->GetLevel();
	auto it = guard_template_mgr::Instance()._auto_get_map.begin();
	auto ite = guard_template_mgr::Instance()._auto_get_map.upper_bound(level);
	for (; it != ite; ++it)
	{
		tid_t guard_id = it->second;
		if (!GetGuard(guard_id))
		{
			AddGuard(imp, guard_id);
		}
	}
}

void player_guard::SendSlotUpdate(gcreature_imp *imp, int slot)
{
	if (!imp || slot < 0 || slot >= _slots.size())
	{
		return;
	}
	guard_slot_info& slot_info = _slots[slot];
	PB::gp_guard_slot_update proto;
	proto.set_element(slot);
	proto.set_guard_id(slot_info.GetID());
	proto.set_level(slot_info.GetLevel());
	imp->Runner()->QuietSend<S2C::CMD::PBS2C>(proto);
}

int player_guard::GetMaxLevelDiff(gcreature_imp *imp)
{
	return MAX_GUARD_PLAYER_LEVEL_DIFF;
}

float player_guard::GetSlotTransferRate(int index)
{
	if (index == GUARD_SLOT_SECONDARY_1_IDX || index == GUARD_SLOT_SECONDARY_2_IDX) // 次战位 不提供战力转换
	{
		return 1;
	}
	if (index < 0 || index >= _slots.size())
	{
		return 1;
	}
	guard_slot_info& slot = _slots[index];
	int slot_level = slot.GetLevel();
	int slot_cfg_id = SERVER_CONFIG.guard_slot_rate_cfg_id[index];
	int slot_total_cfg_id = SERVER_CONFIG.guard_slot_rate_cfg_id[GUARD_ELEMENT_COUNT];
	const level_exp_factor_template *slot_cfg = level_exp_factor_template_manager::GetInstance().Get(slot_cfg_id);
	const level_exp_factor_template *slot_total_cfg = level_exp_factor_template_manager::GetInstance().Get(slot_total_cfg_id);
	if (!slot_cfg || !slot_total_cfg || slot_level > MAX_LEVEL || slot_level < 1 ||
	        _min_slot_level > MAX_LEVEL || _min_slot_level < 1)
	{
		return 1;
	}
	float base_transfer_rate = slot_cfg->exp_factor[slot_level - 1];
	float total_transfer_rate = slot_total_cfg->exp_factor[_min_slot_level  - 1];
	return base_transfer_rate + total_transfer_rate + _star_transfer_rate;
}

int player_guard::GainExpForOneGuard(gcreature_imp *imp, const FuncInfo& fi, tid_t guard_id, exp_t& exp_add)
{
	guard_info *guard = GetGuard(guard_id);
	if (!guard || !guard->GetTemplate())
	{
		return S2C::ERR_GUARD_NOT_FOUND;
	}
	int exp_table_id = guard->GetTemplate()->_exp_table_id;
	auto exp_table = guard_template_mgr::Instance().GetExpTable(exp_table_id);
	if (!exp_table)
	{
		return S2C::ERR_GUARD_NOT_FOUND;
	}

	exp_t exp_left = exp_add;
	int cur_level = guard->GetLevel();
	exp_t cur_exp = guard->GetExp();
	while (true)
	{
		exp_t lvl_exp = cur_level < exp_table->size() ? exp_table->at(cur_level) : DEFAULT_LEVELUP_EXP(cur_level);
		if (lvl_exp < 1)
		{
			lvl_exp = 1;
		}
		if (cur_level >= EXP_GUARD_MAX_LEVEL)
		{
			exp_t exp_cost = std::min((lvl_exp - 1 - cur_exp), exp_left);
			cur_exp += exp_cost;
			exp_left -= exp_cost;
			break;
		}
		else
		{
			exp_t exp_cost = lvl_exp - cur_exp;
			if (exp_cost <= exp_left)
			{
				cur_level += 1;
				cur_exp = 0;
				exp_left -= exp_cost;
				guard->OnLevelUp(imp, cur_level);
			}
			else
			{
				cur_exp += exp_left;
				exp_left = 0;
				break;
			}
		}
	}
	exp_add -= exp_left;
	if (exp_add <= 0)
	{
		return S2C::ERR_GUARD_EXP_FULL;
	}
	bool levelup = guard->GetLevel() < cur_level;
	guard->SetExp(cur_exp);
	guard->SetLevel(cur_level);
	if (levelup)
	{
		guard->Refresh(imp, this);
		SLOG(FORMAT, "guard_levelup")
		.P("roleid", imp->GetParent()->ID.id)
		.P("tid", guard_id)
		.P("level", cur_level);
	}
	// for BI
	if (imp->IsPlayerClass())
	{
		gplayer_imp *pImp = (gplayer_imp *)imp;
		BI_LOG_GLOBAL(pImp->GetParent()->account.ToStr());
		SLOG(FORMAT, "guard_exp")
		.BI_HEADER2_GS(pImp)
		.P("tid", guard_id)
		.P("exp_add", exp_add)
		.P("cur_exp", cur_exp)
		.P("cur_level", cur_level)
		.P("reason", fi.code)
		.P("sub_reason", fi.arg);
	}
	return 0;
}

int player_guard::BreakGuard(gcreature_imp *imp, int guard_id)
{
	guard_info *guard = GetGuard(guard_id);
	if (!guard)
	{
		return S2C::ERR_GUARD_NOT_FOUND;
	}
	int phase = guard->GetPhase();
	if (phase >= GUARD_MAX_PHASE)
	{
		return S2C::ERR_GUARD_MAX_PHASE;
	}
	const guard_phase_cfg *phase_cfg = guard->GetPhaseCfg();
	if (!phase_cfg)
	{
		return S2C::ERR_SERVICE_UNAVILABLE;
	}
	if (guard->GetGrowth() < phase_cfg->_break_growth_value)
	{
		return S2C::ERR_GUARD_GROWTH_LOW;
	}
	std::vector<ITEM_CHECK_INFO> item_info_list;
	for (int i = 0; i < GUARD_BREAK_ITEM_COUNT; i++)
	{
		auto& cost_item = phase_cfg->_break_item_cfg[i];
		if (cost_item._item_id <= 0 || cost_item._item_count <= 0)
		{
			continue;
		}
		ITEM_CHECK_INFO item_info;
		if (!imp->CheckItemExistAtBackpack2(item_info, cost_item._item_id, cost_item._item_count))
		{
			return S2C::ERR_NOT_ENOUGH_MATERIAL;
		}
		item_info_list.push_back(item_info);
	}
	for (auto& item_info : item_info_list)
	{
		imp->DecItem2(item_info, {kFuncCodeGuardBreak});
	}
	guard->SetPhase(phase + 1);
	int assist_count = 0, passive_count = 0;
	SERVER_CONFIG.GetUnlockSkillCount(phase + 1, assist_count, passive_count);
	for (int i = 0; i < passive_count; ++i)
	{
		if (i < passive_count && guard->GetPassiveSkillBook(i) < 0)
		{
			guard->SetPassiveSkillBook(i, 0);
		}
	}
	for (int i = 0; i < assist_count; ++i)
	{
		if (i < assist_count && guard->GetAssistSkillBook(i) < 0)
		{
			guard->SetAssistSkillBook(i, 0);
		}
	}
	guard->Refresh(imp, this, false);
	const guard_phase_cfg *new_phase_cfg = guard->GetPhaseCfg();
	if (phase_cfg && new_phase_cfg)
	{
		ChangeGuardShape(imp, guard_id, new_phase_cfg->_shape);
	}
	if (imp->IsPlayerClass())
	{
		gplayer_imp *pPlayer = (gplayer_imp *)imp;
		UpdatePhaseCount();
		pPlayer->GetAchievement().OnGuardBreakPhase(pPlayer);
		pPlayer->UploadQQInfo(GNET::BTYPE_GUARD_UPGRADE, guard_id, phase + 1);
		if ((phase + 1) >= GUARD_MAX_PHASE)
		{
			pPlayer->UploadQQInfo(GNET::BTYPE_GURAD_UPGRADE_FULL, guard_id, phase + 1);
		}
	}

	PB::gp_guard_break_result result;
	result.set_guard_id(guard_id);
	result.set_retcode(0);
	result.set_phase(phase + 1);
	guard->ExportProp(result);
	result.set_self_fight_capacity(guard->GetFightCapacity());
	result.set_attach_fight_capacity(guard->GetAttachFightCapacity());
	result.set_base_attach_fight_capacity(guard->GetBaseAttachFightCapacity());
	imp->Runner()->QuietSend<S2C::CMD::PBS2C>(result);
	GLog::formatlog("guard_break_phase", "rolelid=%ld:tid=%d:phase=%d", imp->GetParent()->ID.id, guard_id, phase + 1);

	return 0;
}

int player_guard::OpenGuardSkill(gcreature_imp *imp, int guard_id, bool assist, int slot)
{
	gplayer_imp *pPlayer = (gplayer_imp *)imp;
	if (!imp->IsPlayerClass())
	{
		return S2C::ERR_SERVICE_UNAVILABLE;
	}
	guard_info *guard = GetGuard(guard_id);
	if (!guard)
	{
		return S2C::ERR_GUARD_NOT_FOUND;
	}
	int skill_slot_size = assist ? guard->GetEssence().assist_skill_id_size() : guard->GetEssence().passive_skill_id_size();
	if (slot >= skill_slot_size)
	{
		return S2C::ERR_GUARD_INVALID_SKILL_SLOT;
	}
	int old_skill_id = assist ? guard->GetAssistSkillBook(slot) : guard->GetPassiveSkillBook(slot);
	if (old_skill_id >= 0)
	{
		return S2C::ERR_GUARD_SKILL_OPEN;
	}
	if (pPlayer->GetMixCash() < SERVER_CONFIG.guard_skill_slot_cash)
	{
		return S2C::ERR_OUT_OF_CASH;
	}
	if (assist)
	{
		guard->SetAssistSkillBook(slot, 0);
	}
	else
	{
		guard->SetPassiveSkillBook(slot, 0);
	}
	pPlayer->UseMixCash({kFuncCodeGuardOpenSkill, slot}, SERVER_CONFIG.guard_skill_slot_cash);
	return 0;
}

void player_guard::RequestNextPhaseProp(gcreature_imp *imp, int guard_id)
{
	guard_info *guard = GetGuard(guard_id);
	if (!guard)
	{
		return;
	}
	int phase = guard->GetPhase();
	if (phase >= GUARD_MAX_PHASE)
	{
		return;
	}
	PB::gp_guard_next_phase_prop_result proto;
	proto.set_guard_id(guard_id);
	int slot_index = guard->GetElement();
	for (int i = phase + 1; i <= GUARD_MAX_PHASE; ++i)
	{
		guard->SetPhase(i);
		property_template::data_GuardProp guard_prop, base_attached_prop, attached_prop;
		int self_fight_capacity = 0, base_attach_fight_capacity = 0, attach_fight_capacity = 0;
		guard->CalcProp(imp, this, slot_index, guard_prop, base_attached_prop, attached_prop, self_fight_capacity, base_attach_fight_capacity, attach_fight_capacity);

		auto phase_prop = proto.add_phase_prop();
		phase_prop->set_phase(i);
		guard_prop.ExportTo(phase_prop->mutable_cur_prop());
		phase_prop->set_self_fight_capacity(self_fight_capacity);
		phase_prop->set_base_attach_fight_capacity(base_attach_fight_capacity );
		phase_prop->set_attach_fight_capacity(attach_fight_capacity);
	}
	imp->Runner()->QuietSend<S2C::CMD::PBS2C>(proto);

	guard->SetPhase(phase);
}

void player_guard::StarSlotLevelup(gplayer_imp *imp, const PB::gp_guard_star_slot_levelup& cmd)
{
	if (!imp)
	{
		return;
	}
	int slot_index = cmd.slot_index();
	int slot_level = 0;
	int ret = __DoStarSlotLevelup(imp, cmd, slot_level);

	if (ret == 0)
	{
		__CalcStarSlotProp(imp, slot_index);
		int team_slot = guard_template_mgr::Instance().GetStarSlotElement(slot_index);
		if (team_slot >= 0 && team_slot < _slots.size())
		{
			guard_slot_info& slot = _slots[team_slot];
			int guard_id = slot.GetID();
			if (guard_id > 0)
			{
				guard_info *guard = GetGuard(guard_id);
				if (guard)
				{
					guard->Refresh(imp, this);
				}
			}
		}
		imp->GetAchievement().OnGuardStarSlotLevelUp(imp);
	}

	//通知客户端
	PB::gp_guard_star_slot_levelup_result result;
	result.set_slot_index(slot_index);
	result.set_retcode(ret);
	result.set_level(slot_level);
	imp->Runner()->QuietSend<S2C::CMD::PBS2C>(result);
}

int player_guard::__DoStarSlotLevelup(gplayer_imp *imp, const PB::gp_guard_star_slot_levelup& cmd, int& slot_level)
{
	CHECK_FUNC_SWITCH_AND_RETURN_INT(kFuncCodeGuardStar, imp, S2C::ERR_FUNC_CLOSED_TEMPORARY);

#define ITEM_TID(entry)      std::get<0>(entry)
#define EXIST_COUNT(entry)   std::get<1>(entry)
#define COMBINE_COUNT(entry) std::get<2>(entry)
#define RETURN(retcode, reason) \
do { \
	logger.P("retcode", retcode).P("reason", reason); \
	return retcode; \
} while (false)

	GNET::LogMessage logger(LOG_DEBUG, "GS::player_guard::__DoStarSlotLevelUp");
	logger.P("roleid", imp->GetParent()->ID.id)
	.P("slot_index", cmd.slot_index());

	if (cmd.has_material())
	{
		logger.P("material_target", cmd.material().target());
	}
	if (cmd.slot_index() < 0 || cmd.slot_index() >= _star_data.slot_size() || cmd.slot_index() >= GUARD_STAR_SLOT_COUNT)
	{
		RETURN(S2C::ERR_FATAL_ERR, "参数错误");
	}

	// 检查配置
	auto ptempl = GUARD_STAR_SLOT_CONFIG::get(SERVER_CONFIG.guard_star_slot_config_id[cmd.slot_index()]);
	auto pStarTempl = GUARD_STAR_CONFIG::get(SERVER_CONFIG.guard_star_config_id);
	if (!ptempl || !pStarTempl)
	{
		RETURN(S2C::ERR_FATAL_ERR, "配置无效");
	}
	slot_level = _star_data.slot(cmd.slot_index()).level();
	logger.P("old_level", slot_level);
	if (slot_level <= 0 || slot_level > ptempl->configs.size())
	{
		RETURN(S2C::ERR_FATAL_ERR, "等级错误");
	}
	int star_level = _star_data.level();
	if (star_level <= 0 || star_level > pStarTempl->configs.size())
	{
		RETURN(S2C::ERR_FATAL_ERR, "等级错误");
	}
	auto& star_config = pStarTempl->configs[star_level - 1];
	if (slot_level >= star_config.max_slot_level)
	{
		RETURN(S2C::ERR_LEVEL_NOT_MATCH, "星阵等级不足");
	}
	auto& config = ptempl->configs[slot_level - 1];
	if (config.cost_item_tid <= 0 || config.cost_item_count <= 0)
	{
		RETURN(S2C::ERR_FATAL_ERR, "材料配置错误");
	}
	if (config.cost_money > 0 && config.cost_money > imp->GetMoney(MT_BIND))
	{
		RETURN(S2C::ERR_NOT_ENOUGH_MONEY, "金钱不足");
	}

	// 检查客户端发来的数据与配置是否一致
	if (cmd.has_material() && cmd.material().target() != config.cost_item_tid)
	{
		RETURN(S2C::ERR_FATAL_ERR, "无效的客户端数据");
	}

	// 计算背包中材料的数量和需要补足的数量
	std::tuple<int, int, int> material_items  = std::make_tuple(config.cost_item_tid, 0, 0);
	ITEM_CHECK_INFO info_material(config.cost_item_tid, config.cost_item_count, false);

	int prepare_ret = [this, imp, &cmd, &config, &logger, &material_items, &info_material]() -> int
	{
		imp->CheckItemExistAtBackpack2(info_material, 0, 0, false);
		if (info_material.GetTotalCount() < config.cost_item_count)
		{
			EXIST_COUNT(material_items)   = info_material.GetTotalCount();
			COMBINE_COUNT(material_items) = config.cost_item_count - info_material.GetTotalCount();
		}
		else
		{
			EXIST_COUNT(material_items) = config.cost_item_count;
		}
		logger.P("material_exist_count", EXIST_COUNT(material_items))
		.P("material_combine_count", COMBINE_COUNT(material_items));
		return S2C::ERR_SUCCESS;
	}();
	if (prepare_ret != S2C::ERR_SUCCESS)
	{
		RETURN(prepare_ret, "预检查错误");
	}

	// advance合并与消耗
	auto combine_proc = [this, imp, slot_level, &cmd, &material_items, &logger]() -> int
	{
		std::map<tid_t, int> exist_material;
		std::map<tid_t, int> need_material;
		unsigned int cost_material        = 0U;
		int          money_cost_material  = 0;
		int          extra_id_material    = 0;
		int          exist_extra_material = 0;
		FuncInfo fi{kFuncCodeGuardStar, cmd.slot_index(), slot_level};

		// 合并检查
		if (COMBINE_COUNT(material_items) > 0)
		{
			if (!cmd.has_material())
			{
				return S2C::ERR_FATAL_ERR;
			}

			PB::gp_item_combine_advance combine_info = cmd.material();
			combine_info.mutable_slot()->set_location(GNET::IL_GUARD_STAR_SLOT_LEVELUP);
			std::stringstream ss;
			int ret = imp->CheckCombineItemAdvance(combine_info,
			                                       item_manager::ACT_GUARD_STAR_SLOT_LEVELUP,
			                                       COMBINE_COUNT(material_items),
			                                       exist_material, need_material,
			                                       cost_material, money_cost_material,
			                                       extra_id_material, exist_extra_material,
			                                       fi, ss);
			logger.P("material_combine_info", std::string("(") + ss.str() + std::string(")"));
			if (ret != 0)
			{
				logger.P("combine_material_errcode", ret);
				return S2C::ERR_FATAL_ERR;
			}
		}
		// 合并消耗
		if (COMBINE_COUNT(material_items) > 0)
		{
			// 前边检查通过 这里一定成功
			imp->DecCombineItemAdvance(cmd.material(), COMBINE_COUNT(material_items),
			                           exist_material, need_material,
			                           cost_material, money_cost_material,
			                           extra_id_material, exist_extra_material,
			                           fi);
		}

		return S2C::ERR_SUCCESS;
	};

	if (COMBINE_COUNT(material_items) > 0)
	{
		int combine_ret = combine_proc();
		if (combine_ret != S2C::ERR_SUCCESS)
		{
			RETURN(combine_ret, "合并失败");
		}
	}

	// 升级与消耗
	[this, imp, &cmd, &config, &material_items, &slot_level, &info_material]() -> void
	{
		auto slot_data = _star_data.mutable_slot(cmd.slot_index());

		// 扣除背包里以存在的道具
		FuncInfo func_info{kFuncCodeGuardStar, cmd.slot_index(), slot_level};
		if (EXIST_COUNT(material_items) > 0)
		{
			imp->DecItem2(info_material, func_info);
		}
		// 扣钱
		if (config.cost_money > 0)
		{
			imp->DecMoney(func_info, MT_BIND, config.cost_money);
		}
		++slot_level;
		slot_data->set_level(slot_level);
	}();

	logger.P("new_level", slot_level);

	RETURN(S2C::ERR_SUCCESS, "成功");

#undef COMBINE_COUNT
#undef EXIST_COUNT
#undef ITEM_TID
}

float player_guard::GetStarLevelAverage() const
{
	if (_star_data.slot().empty())
	{
		return 0;
	}
	int level = 0;
	for (const auto& slot : _star_data.slot())
	{
		level += slot.level();
	}
	return (float)level / _star_data.slot_size();
}

int player_guard::GetMinStarSlotLevel()
{
	if (_star_data.slot_size() == 0)
	{
		return 1;
	}
	int min_level = INT_MAX;
	for (int i = 0; i < _star_data.slot_size(); ++i)
	{
		int slot_level = _star_data.slot(i).level();
		if (slot_level < min_level)
		{
			min_level = slot_level;
		}
	}
	return min_level;
}

int player_guard::GetTotalStarSlotLevel()
{
	int total_level = 0;
	for (const auto& slot : _star_data.slot())
	{
		total_level += slot.level();
	}
	return total_level;
}

void player_guard::StarLevelup(gplayer_imp *imp, const PB::gp_guard_star_levelup& cmd)
{
	if (!imp)
	{
		return;
	}
	int star_level = 0;
	int ret = __DoStarLevelup(imp, cmd, star_level);

	if (ret == 0)
	{
		__CalcStarTransferRate(imp);
		for (auto& slot : _slots)
		{
			int guard_id = slot.GetID();
			if (guard_id > 0)
			{
				guard_info *guard = GetGuard(guard_id);
				if (guard)
				{
					guard->Refresh(imp, this);
				}
			}
		}
	}

	//通知客户端
	PB::gp_guard_star_levelup_result result;
	result.set_retcode(ret);
	result.set_level(star_level);
	imp->Runner()->QuietSend<S2C::CMD::PBS2C>(result);
}

int player_guard::__DoStarLevelup(gplayer_imp *imp, const PB::gp_guard_star_levelup& cmd, int& star_level)
{
	CHECK_FUNC_SWITCH_AND_RETURN_INT(kFuncCodeGuardStar, imp, S2C::ERR_FUNC_CLOSED_TEMPORARY);

#define ITEM_TID(entry)      std::get<0>(entry)
#define EXIST_COUNT(entry)   std::get<1>(entry)
#define COMBINE_COUNT(entry) std::get<2>(entry)
#define RETURN(retcode, reason) \
do { \
	logger.P("retcode", retcode).P("reason", reason); \
	return retcode; \
} while (false)

	GNET::LogMessage logger(LOG_DEBUG, "GS::player_guard::__DoStarLevelUp");
	logger.P("roleid", imp->GetParent()->ID.id);

	if (cmd.has_material())
	{
		logger.P("material_target", cmd.material().target());
	}

	// 检查配置
	auto ptempl = GUARD_STAR_CONFIG::get(SERVER_CONFIG.guard_star_config_id);
	if (!ptempl)
	{
		RETURN(S2C::ERR_FATAL_ERR, "配置无效");
	}
	star_level = _star_data.level();
	logger.P("old_level", star_level);
	if (star_level <= 0 || star_level > ptempl->configs.size() || star_level >= SERVER_CONFIG.guard_star_max_level)
	{
		RETURN(S2C::ERR_FATAL_ERR, "等级错误");
	}
	auto& config = ptempl->configs[star_level - 1];
	for (int i = 0; i < _star_data.slot_size(); ++i)
	{
		if (_star_data.slot(i).level() < config.max_slot_level)
		{
			RETURN(S2C::ERR_LEVEL_NOT_MATCH, "等级不足");
		}
	}
	// 判断宠物小屋等级
	int hourse_level = _house_data.house_level();
	if (hourse_level < config.need_guard_hourse_level)
	{
		RETURN(S2C::ERR_LEVEL_NOT_MATCH, "等级不足");
	}

	if (config.cost_item_tid <= 0 || config.cost_item_count <= 0)
	{
		RETURN(S2C::ERR_FATAL_ERR, "材料配置错误");
	}
	if (config.cost_money > 0 && config.cost_money > imp->GetMoney(MT_BIND))
	{
		RETURN(S2C::ERR_NOT_ENOUGH_MONEY, "金钱不足");
	}

	// 检查客户端发来的数据与配置是否一致
	if (cmd.has_material() && cmd.material().target() != config.cost_item_tid)
	{
		RETURN(S2C::ERR_FATAL_ERR, "无效的客户端数据");
	}

	// 计算背包中材料的数量和需要补足的数量
	std::tuple<int, int, int> material_items  = std::make_tuple(config.cost_item_tid, 0, 0);
	ITEM_CHECK_INFO info_material(config.cost_item_tid, config.cost_item_count, false);

	int prepare_ret = [this, imp, &cmd, &config, &logger, &material_items, &info_material]() -> int
	{
		imp->CheckItemExistAtBackpack2(info_material, 0, 0, false);
		if (info_material.GetTotalCount() < config.cost_item_count)
		{
			EXIST_COUNT(material_items)   = info_material.GetTotalCount();
			COMBINE_COUNT(material_items) = config.cost_item_count - info_material.GetTotalCount();
		}
		else
		{
			EXIST_COUNT(material_items) = config.cost_item_count;
		}
		logger.P("material_exist_count", EXIST_COUNT(material_items))
		.P("material_combine_count", COMBINE_COUNT(material_items));
		return S2C::ERR_SUCCESS;
	}();
	if (prepare_ret != S2C::ERR_SUCCESS)
	{
		RETURN(prepare_ret, "预检查错误");
	}

	// advance合并与消耗
	auto combine_proc = [this, imp, star_level, &cmd, &material_items, &logger]() -> int
	{
		std::map<tid_t, int> exist_material;
		std::map<tid_t, int> need_material;
		unsigned int cost_material        = 0U;
		int          money_cost_material  = 0;
		int          extra_id_material    = 0;
		int          exist_extra_material = 0;
		FuncInfo fi{kFuncCodeGuardStar, -1, star_level};

		// 合并检查
		if (COMBINE_COUNT(material_items) > 0)
		{
			if (!cmd.has_material())
			{
				return S2C::ERR_FATAL_ERR;
			}

			PB::gp_item_combine_advance combine_info = cmd.material();
			combine_info.mutable_slot()->set_location(GNET::IL_GUARD_STAR_LEVELUP);
			std::stringstream ss;
			int ret = imp->CheckCombineItemAdvance(combine_info,
			                                       item_manager::ACT_GUARD_STAR_LEVELUP,
			                                       COMBINE_COUNT(material_items),
			                                       exist_material, need_material,
			                                       cost_material, money_cost_material,
			                                       extra_id_material, exist_extra_material,
			                                       fi, ss);
			logger.P("material_combine_info", std::string("(") + ss.str() + std::string(")"));
			if (ret != 0)
			{
				logger.P("combine_material_errcode", ret);
				return S2C::ERR_FATAL_ERR;
			}
		}
		// 合并消耗
		if (COMBINE_COUNT(material_items) > 0)
		{
			// 前边检查通过 这里一定成功
			imp->DecCombineItemAdvance(cmd.material(), COMBINE_COUNT(material_items),
			                           exist_material, need_material,
			                           cost_material, money_cost_material,
			                           extra_id_material, exist_extra_material,
			                           fi);
		}

		return S2C::ERR_SUCCESS;
	};

	if (COMBINE_COUNT(material_items) > 0)
	{
		int combine_ret = combine_proc();
		if (combine_ret != S2C::ERR_SUCCESS)
		{
			RETURN(combine_ret, "合并失败");
		}
	}

	// 升级与消耗
	[this, imp, &cmd, &config, &material_items, &star_level, &info_material]() -> void
	{
		// 扣除背包里以存在的道具
		FuncInfo func_info{kFuncCodeGuardStar, -1, star_level};
		if (EXIST_COUNT(material_items) > 0)
		{
			imp->DecItem2(info_material, func_info);
		}
		// 扣钱
		if (config.cost_money > 0)
		{
			imp->DecMoney(func_info, MT_BIND, config.cost_money);
		}
		++star_level;
		_star_data.set_level(star_level);
	}();

	logger.P("new_level", star_level);

	RETURN(S2C::ERR_SUCCESS, "成功");

#undef COMBINE_COUNT
#undef EXIST_COUNT
#undef ITEM_TID
}

int player_guard::ChangeGuardSkill(gcreature_imp *imp, tid_t guard_id, bool assist, int skill_slot, item_index_t item_index, int& skill_id)
{
	guard_info *guard = GetGuard(guard_id);
	if (!guard)
	{
		return S2C::ERR_GUARD_NOT_FOUND;
	}
	int errcode = guard->ChangeSkill(imp, assist, skill_slot, item_index, skill_id);
	if (errcode == 0)
	{
		guard->Refresh(imp, this);
	}
	return errcode;
}

void player_guard::OnLeaveCombat(gcreature_imp *imp)
{
	memset(_next_skill_time, 0, sizeof(_next_skill_time));
}

void player_guard::TryCastActiveSkill(gcreature_imp *imp, const XID& target)
{
	auto try_cast_skill_f = [this, imp, &target](int group_idx) -> void
	{
		if (group_idx < 0 || group_idx >= GUARD_GROUP_COUNT)
		{
			return;
		}

		if (_casting_skill_slot[group_idx] >= 0)
		{
			return;
		}
		int now = gmatrix::GetInstance().GetSysTime();
		if (_next_skill_time[group_idx] == 0)
		{
			_next_skill_time[group_idx] = now + abase::Rand(SKILL_CD_ENTER_COMBAT_MIN, SKILL_CD_ENTER_COMBAT_MAX);
			return;
		}
		if (now < _next_skill_time[group_idx])
		{
			return;
		}
		_next_skill_time[group_idx] = gmatrix::GetInstance().GetSysTime() + abase::Rand(SKILL_CD_DURING_COMBAT_MIN, SKILL_CD_DURING_COMBAT_MAX);
		if (_slots.empty())
		{
			return;
		}

		int slot_index = abase::Rand(0, GUARD_GROUP_CAP - 1) + group_idx * GUARD_GROUP_CAP;
		int guard_id = _slots[slot_index].GetID();
		if (guard_id <= 0)
		{
			return;
		}
		guard_info *guard = GetGuard(guard_id);
		if (!guard || guard->GetActiveSkillID() <= 0)
		{
			return;
		}
		const XID& skill_target = guard->GetFightType() == FIGHT_ATTACK ? target : imp->GetParent()->ID;
		PB::gp_guard_cast_skill proto;
		proto.set_player_id(imp->GetParent()->ID.id);
		proto.set_guard_id(guard->GetTID());
		proto.set_guard_shape(guard->GetShape());
		proto.set_skill_id(guard->GetActiveSkillID());
		proto.set_skill_target(skill_target.id);
		proto.set_guard_phase(guard->GetPhase());
		proto.set_guard_skin_id(guard->GetSkinID());
		proto.set_guard_gfx_id(guard->GetGfxID());
		proto.set_guard_generation(guard->GetGeneration());
		imp->Runner()->RegionSend<S2C::CMD::PBS2C>(proto);

		// call skill interface
		_casting_skill_slot[group_idx] = slot_index;
		_skill_target[group_idx] = skill_target;
		_skill_delay[group_idx] = ACTIVE_SKILL_DELAY;
	};

	for (int i = 0; i < GUARD_GROUP_COUNT; ++i)
	{
		try_cast_skill_f(i);
	}
}

void player_guard::DoCastActiveSkill(gcreature_imp *imp, int group_idx)
{
	gplayer_imp *pImp = reinterpret_cast<gplayer_imp *>(imp);
	CHECK_FUNC_SWITCH_AND_RETURN(kFuncCodeGuardSlotPutin, pImp);

	if (group_idx < 0 || group_idx >= GUARD_GROUP_COUNT)
	{
		return;
	}

	if (_casting_skill_slot[group_idx] < 0)
	{
		return;
	}
	do
	{
		const guard_slot_info *slot = GetSlot(_casting_skill_slot[group_idx]);
		if (!slot || slot->GetID() <= 0)
		{
			break;
		}
		const guard_info *guard = GetGuard(slot->GetID());
		if (!guard || guard->GetActiveSkillID() <= 0)
		{
			break;
		}
		if (guard->GetFightType() == FIGHT_ATTACK && !imp->CheckCanAttack(_skill_target[group_idx]))
		{
			_next_skill_time[group_idx] = gmatrix::GetInstance().GetSysTime();
			break;
		}
		std::vector<skill_id_t> skill_arr;
		skill_arr.push_back(guard->GetActiveSkillID());

		auto *phase_cfg = guard->GetPhaseCfg();
		if (!phase_cfg)
		{
			break;
		}
		for (int i = 0; i < GUARD_ASSIST_SKILL_SLOT_COUNT; ++i)
		{
			int assist_fight_type = 0;
			int assist_skill = guard->GetAssistSkillID(i, assist_fight_type);
			if (assist_skill <= 0)
			{
				continue;
			}
			int rand = abase::Rand(1, 1000);
			if (rand < phase_cfg->_assist_skill_ratio)
			{
				if (assist_fight_type == FIGHT_ATTACK && !imp->CheckCanAttack(_skill_target[group_idx]))
				{
					continue;
				}
				skill_arr.push_back(assist_skill);
			}
		}
		_casting_active_skill[group_idx] = true;
		imp->PartnerCastSkill_AutoDelay(GUARD_CAST_SKILL, guard->GetTID(), skill_arr, _skill_target[group_idx],
		                                [this, group_idx](int skill_index, bool result)
		{
			if (skill_index == 0)
			{
				_casting_active_skill[group_idx] = false;
			}
		});
		_casting_active_skill[group_idx] = false;
	}
	while (0);
	_casting_skill_slot[group_idx] = -1;
	_skill_target[group_idx].Clear();
}

void player_guard::Heartbeat(gcreature_imp *imp)
{
	auto if_do_cast_f = [this](int group_idx) -> bool
	{
		if (group_idx < 0 || group_idx >= GUARD_GROUP_COUNT)
		{
			return false;
		}

		if (_casting_skill_slot[group_idx] < 0)
		{
			return false;
		}
		if (--_skill_delay[group_idx] > 0)
		{
			return false;
		}
		return true;
	};
	for (int i = 0; i < GUARD_GROUP_COUNT; ++i)
	{
		if (!if_do_cast_f(i))
		{
			continue;
		}
		DoCastActiveSkill(imp, i);
	}
}

bool player_guard::HasGuardSkill(int skill_id) const
{
	auto is_assist_skill_used_f = [this, skill_id](int group_idx) -> bool
	{
		if (group_idx < 0 || group_idx >= GUARD_GROUP_COUNT)
		{
			return false;
		}

		if (_casting_skill_slot[group_idx] < 0)
		{
			return false;
		}
		const guard_slot_info *slot = GetSlot(_casting_skill_slot[group_idx]);
		if (!slot || slot->GetID() <= 0)
		{
			return false;
		}
		const guard_info *guard = GetGuard(slot->GetID());
		if (!guard)
		{
			return false;
		}
		return guard->IsAssistSkillUsed(skill_id);
	};
	for (int i = 0; i < GUARD_GROUP_COUNT; ++i)
	{
		if (is_assist_skill_used_f(i))
		{
			return true;
		}
	}
	return false;
}

void player_guard::FillAttackMsg(gcreature_imp *imp, const XID& target, attack_msg& attack)
{
	auto fill_attack_msg_f = [this, imp, &target, &attack](int group_idx) -> void
	{
		if (group_idx < 0 || group_idx >= GUARD_GROUP_COUNT)
		{
			return;
		}

		if (_casting_skill_slot[group_idx] < 0)
		{
			return;
		}
		const guard_slot_info *slot = GetSlot(_casting_skill_slot[group_idx]);
		if (!slot)
		{
			return;
		}
		const guard_info *guard = GetGuard(slot->GetID());
		if (!guard)
		{
			return;
		}
		int phase = guard->GetPhase();
		int player_attack = 0;
		if (_casting_active_skill[group_idx])
		{
			if (phase >= GUARD_MAX_PHASE)
			{
				if (guard->GetPlayerPropType() == GUARD_ATTACK_PHYSIC)
				{
					player_attack = attack.attack_physic;
				}
				else if (guard->GetPlayerPropType() == GUARD_ATTACK_MAGIC)
				{
					player_attack = attack.attack_magic;
				}
				else
				{
					player_attack = std::max(attack.attack_physic, attack.attack_magic);
				}
			}
		}

		attack.attack_physic = std::max(attack.attack_physic, attack.attack_magic);
		attack.attack_magic = attack.attack_physic;

		const creature_prop& prop = slot->GetProp();

		attack.base_attack_physic = prop.GetGProperty()->PROP_NAME_GET(curPhyAtk) + player_attack * guard->GetPlayerPropRatio() / 1000;
		attack.base_attack_magic = prop.GetGProperty()->PROP_NAME_GET(curMagAtk) + player_attack * guard->GetPlayerPropRatio() / 1000;
	};

	for (int i = 0; i < GUARD_GROUP_COUNT; ++i)
	{
		fill_attack_msg_f(i);
	}
}

void player_guard::InsertSkill(gcreature_imp *imp, int skill_id)
{
	auto iter = _skill_map.find(skill_id);
	if (iter == _skill_map.end())
	{
		_skill_map.insert(std::make_pair(skill_id, 1));
		ConditionSuppressCombatStat(imp, SuppressCombatSystem::PET)
			.Then(nullptr, "player_guard::InsertSkill: suppress skill insert")
			.Else([&](gcreature_imp*) {
				object_interface oif(imp);
				imp->GetSkill().InsertSkill(skill_id, 1, oif);
			}, "player_guard::InsertSkill: normal skill insert");
	}
	else
	{
		iter->second++;
	}
}

void player_guard::RemoveSkill(gcreature_imp *imp, int skill_id)
{
	auto iter = _skill_map.find(skill_id);
	if (iter == _skill_map.end())
	{
		return;
	}
	if (--iter->second <= 0)
	{
		ConditionSuppressCombatStat(imp, SuppressCombatSystem::PET)
			.Then(nullptr, "player_guard::RemoveSkill: suppress skill remove")
			.Else([&](gcreature_imp*) {
				object_interface oif(imp);
				imp->GetSkill().RemoveSkill(skill_id, 1, oif);
			}, "player_guard::RemoveSkill: normal skill remove");
		_skill_map.erase(iter);
	}
}

void player_guard::IdipSetLevel(gplayer_imp *imp, int new_level)
{
	if (new_level <= 0)
	{
		return;
	}

	auto pStarTempl = GUARD_STAR_CONFIG::get(SERVER_CONFIG.guard_star_config_id);
	if (!pStarTempl)
	{
		return;
	}

	int star_level = _star_data.level();
	if (star_level <= 0 || star_level > pStarTempl->configs.size())
	{
		return;
	}

	int new_star_level = 0;
	for (int i = pStarTempl->configs.size(); i > 0; --i)
	{
		if (pStarTempl->configs[i - 1].max_slot_level <= 0)
		{
			continue;
		}

		if (new_level <= pStarTempl->configs[i - 1].max_slot_level)
		{
			new_star_level = i;
		}
		else
		{
			break;
		}
	}
	if (new_star_level == 0)
	{
		return;
	}

	for (int slot_index = 0; slot_index < GUARD_STAR_SLOT_COUNT; ++slot_index)
	{
		auto ptempl = GUARD_STAR_SLOT_CONFIG::get(SERVER_CONFIG.guard_star_slot_config_id[slot_index]);
		if (!ptempl)
		{
			return;
		}

		auto slot_data = _star_data.mutable_slot(slot_index);
		int slot_level = _star_data.slot(slot_index).level();
		if (slot_level <= 0 || slot_level > ptempl->configs.size())
		{
			return;
		}

		if (new_level == slot_data->level())
		{
			continue;
		}
		slot_data->set_level(new_level);
		__CalcStarSlotProp(imp, slot_index);

		int team_slot = guard_template_mgr::Instance().GetStarSlotElement(slot_index);
		if (team_slot >= 0 && team_slot < _slots.size())
		{
			guard_slot_info& slot = _slots[team_slot];
			int guard_id = slot.GetID();
			if (guard_id > 0)
			{
				guard_info *guard = GetGuard(guard_id);
				if (guard)
				{
					guard->Refresh(imp, this);
				}
			}
		}

		PB::gp_guard_star_slot_levelup_result result;
		result.set_slot_index(slot_index);
		result.set_retcode(0);
		result.set_level(new_level);
		imp->Runner()->QuietSend<S2C::CMD::PBS2C>(result);

		SLOG(FORMAT, "player_guard::IdipSetLevel").P("role_id", imp->GetRoleID()).P("slot_index", slot_index).P("old_level", slot_level).P("new_level", new_level);
	}

	if (star_level != new_star_level)
	{
		_star_data.set_level(new_star_level);
		__CalcStarTransferRate(imp);

		PB::gp_guard_star_levelup_result res;
		res.set_retcode(0);
		res.set_level(new_star_level);
		imp->Runner()->QuietSend<S2C::CMD::PBS2C>(res);

		SLOG(FORMAT, "player_guard::IdipSetLevel").P("role_id", imp->GetRoleID()).P("star_level", star_level).P("new_star_level", new_star_level);
	}

	imp->GetAchievement().OnGuardStarSlotLevelUp(imp);
}

void player_guard::Debug(gcreature_imp *imp, int cmd, tid_t guard_id, int arg1, int arg2)
{
	switch (cmd)
	{
	case 0:
	{
		guard_info *guard = GetGuard(guard_id);
		if (guard)
		{
			guard->DebugOutput();
		}
	}
	break;
	case 1:
		AddGuard(imp, guard_id);
		break;
	case 2:
		ChangeGuardShape(imp, guard_id, arg1);
		break;
	case 3:
		SummonGuard(imp, guard_id, arg1);
		break;
	case 4:
		BreakGuard(imp, guard_id);
		break;
	case 5:
		OpenGuardSkill(imp, guard_id, arg1 > 0, arg2);
		break;
	case 6:
		PutInGuard(imp, guard_id, arg1);
		break;
	case 7:
		TakeOutGuard(imp, arg1);
		break;
	case 8:
		SlotLevelUp(imp, arg1);
		break;
	case 9:
		GainExpByItem(imp, guard_id, arg1, arg2);
		break;
	case 10:
	{
		SLOG(ERR, "guard_slot_info").P("id0", _slots[0].GetID()).P("level0", _slots[0].GetLevel())
		.P("id1", _slots[1].GetID()).P("level1", _slots[1].GetLevel())
		.P("id2", _slots[2].GetID()).P("level2", _slots[2].GetLevel())
		.P("id3", _slots[3].GetID()).P("level3", _slots[3].GetLevel())
		.P("min_slot_level", _min_slot_level);
	}
	break;
	case 11:
	{
		guard_info *guard = GetGuard(guard_id);
		if (guard)
		{
			guard->SetLevel(arg1);
			guard->Refresh(imp, this);
		}
	}
	break;
	case 12:
	{
		guard_info *guard = GetGuard(guard_id);
		if (guard)
		{
			guard->SetPhase(arg1);
			guard->Refresh(imp, this);
			if (imp->IsPlayerClass())
			{
				gplayer_imp *pPlayer = (gplayer_imp *)imp;
				UpdatePhaseCount();
				pPlayer->GetAchievement().OnGuardBreakPhase(pPlayer);
			}
		}
	}
	break;
	case 13:
	{
		DebugCastAtiveSkill(imp, guard_id, imp->GetCurTarget());
	}
	break;
	case 14:
	{
		if (!imp->GetCurTarget().IsValid())
		{
			return;
		}
		MSG begin_msg;
		BuildMessage(begin_msg, GM_MSG_CAST_GUARD_SKILL, imp->GetCurTarget(), imp->Parent()->ID, A3DVECTOR3(0, 0, 0), guard_id);
		gmatrix::GetInstance().SendMessage(begin_msg);
	}
	break;
	case 15:
	{
		if (!imp->GetCurTarget().IsValid())
		{
			return;
		}
		MSG begin_msg;
		BuildMessage(begin_msg, GM_MSG_DUMP_PROP, imp->GetCurTarget(), imp->Parent()->ID, A3DVECTOR3(0, 0, 0));
		gmatrix::GetInstance().SendMessage(begin_msg);
	}
	break;
	case 16:
	{
		int skill_id = 0;
		ChangeGuardSkill(imp, guard_id, true, arg1, arg2, skill_id);
	}
	break;
	case 17:
	{
		int skill_id = 0;
		ChangeGuardSkill(imp, guard_id, false, arg1, arg2, skill_id);
	}
	break;
	case 18:
	{
		guard_info *guard = GetGuard(guard_id);
		if (guard)
		{
			int growth = guard->GetGrowth();
			growth += arg1;
			guard->SetGrowth(growth);
			guard->Refresh(imp, this);
			PB::gp_guard_add_exp_result result;
			result.set_guard_id(guard_id);
			result.set_guard_level(guard->GetLevel());
			result.set_guard_exp(guard->GetExp());
			result.set_growth_value(guard->GetGrowth());
			imp->Runner()->QuietSend<S2C::CMD::PBS2C>(result);
		}
	}
	break;
	case 19:
	{
		RequestNextPhaseProp(imp, guard_id);
	}
	break;
	case 20:
	{
		gplayer_imp *pPlayer = (gplayer_imp *)imp;
		PB::gp_guard_star_slot_levelup proto;
		proto.set_slot_index(guard_id);
		StarSlotLevelup(pPlayer, proto);
	}
	break;
	case 21:
	{
		gplayer_imp *pPlayer = (gplayer_imp *)imp;
		PB::gp_guard_star_levelup proto;
		StarLevelup(pPlayer, proto);
	}
	break;
	case 22:
	{
		guard_info *guard = GetGuard(guard_id);
		if (guard && arg1 > 0)
		{
			int new_skin = 0;
			int new_gfx = 0;
			guard->IncStarLevel(imp, arg1, new_skin, new_gfx);
			guard->Refresh(imp, this);
		}
	}
	break;
	case 23:
	{
		guard_info *guard = GetGuard(guard_id);
		if (guard && guard->IsInMarry())
		{
			auto spouse_data = guard->GetEssence().mutable_spouse();
			for (int i = 0; i < spouse_data->cycle_state_size(); ++i)
			{
				spouse_data->set_cycle_state(0, PB::GUARD_BREED_BOTH);
			}
			spouse_data->set_today_state(PB::GUARD_BREED_FINISH);
			guard->SendBreedUpdate(imp);
		}
	}
	break;
	case 30:
	{
		auto&& debug_star_levelup = [this, imp](int slot_index, int new_level)
		{
			if (slot_index < 0 || slot_index >= GUARD_STAR_SLOT_COUNT)
			{
				return;
			}
			auto slot_data = _star_data.mutable_slot(slot_index);
			auto ptempl = GUARD_STAR_SLOT_CONFIG::get(SERVER_CONFIG.guard_star_slot_config_id[slot_index]);
			auto pStarTempl = GUARD_STAR_CONFIG::get(SERVER_CONFIG.guard_star_config_id);
			if (!ptempl || !pStarTempl)
			{
				return;
			}
			int slot_level = _star_data.slot(slot_index).level();
			if (slot_level <= 0 || slot_level > ptempl->configs.size())
			{
				return;
			}
			int star_level = _star_data.level();
			if (star_level <= 0 || star_level > pStarTempl->configs.size())
			{
				return;
			}
			auto& star_config = pStarTempl->configs[star_level - 1];
			new_level = std::min(new_level, star_config.max_slot_level);
			if (slot_level >= new_level)
			{
				return;
			}
			slot_data->set_level(new_level);
			__CalcStarSlotProp(imp, slot_index);

			int team_slot = guard_template_mgr::Instance().GetStarSlotElement(slot_index);
			if (team_slot >= 0 && team_slot < _slots.size())
			{
				guard_slot_info& slot = _slots[team_slot];
				int guard_id = slot.GetID();
				if (guard_id > 0)
				{
					guard_info *guard = GetGuard(guard_id);
					if (guard)
					{
						guard->Refresh(imp, this);
					}
				}
			}

			//通知客户端
			PB::gp_guard_star_slot_levelup_result result;
			result.set_slot_index(slot_index);
			result.set_retcode(0);
			result.set_level(new_level);
			imp->Runner()->QuietSend<S2C::CMD::PBS2C>(result);

			if (imp->IsPlayerClass())
			{
				gplayer_imp *pPlayer = (gplayer_imp *)imp;
				pPlayer->GetAchievement().OnGuardStarSlotLevelUp(pPlayer);
			}
		};
		int slot_index = guard_id;
		int new_level = arg1;
		if (slot_index >= 0)
		{
			debug_star_levelup(slot_index, new_level);
		}
		else
		{
			for (int i = 0; i < GUARD_STAR_SLOT_COUNT; ++i)
			{
				debug_star_levelup(i, new_level);
			}
		}
	}
	break;
	default:
		break;
	}
}

void player_guard::DebugCastAtiveSkill(gcreature_imp *imp, tid_t guard_id, const XID& target)
{
	auto debug_cast_skill_f = [this, imp, guard_id, &target](int group_idx) -> void
	{
		if (group_idx < 0 || group_idx >= GUARD_GROUP_COUNT)
		{
			return;
		}

		if (_casting_skill_slot[group_idx] >= 0)
		{
			return;
		}
		if (!target.IsValid())
		{
			return;
		}
		guard_info *guard = GetGuard(guard_id);
		if (!guard || guard->GetActiveSkillID() <= 0 || guard->GetSlotIndex() < 0)
		{
			return;
		}
		PB::gp_guard_cast_skill proto;
		proto.set_player_id(imp->GetParent()->ID.id);
		proto.set_guard_id(guard->GetTID());
		proto.set_guard_shape(guard->GetShape());
		proto.set_skill_id(guard->GetActiveSkillID());
		proto.set_skill_target(target.id);
		proto.set_guard_phase(guard->GetPhase());
		proto.set_guard_skin_id(guard->GetSkinID());
		proto.set_guard_gfx_id(guard->GetGfxID());
		proto.set_guard_generation(guard->GetGeneration());
		imp->Runner()->RegionSend<S2C::CMD::PBS2C>(proto);

		// call skill interface
		_casting_skill_slot[group_idx] = guard->GetSlotIndex();
		MakeNewIDFromXID(target, _skill_target[group_idx]);
		_skill_delay[group_idx] = ACTIVE_SKILL_DELAY;
	};

	for (int i = 0; i < GUARD_GROUP_COUNT; ++i)
	{
		debug_cast_skill_f(i);
	}
}

void player_guard::CheckMaxFightCapacity(gcreature_imp *imp, guard_info *guard)
{
	if (!imp || !guard)
	{
		return;
	}
	if (guard->GetFightCapacity() < _max_fight_capacity)
	{
		return;
	}
	_max_fight_capacity = guard->GetFightCapacity();
	_max_guard = guard->GetTID();
}

int player_guard::GetPropByIdx(int prop_idx) const
{
	if (_casting_skill_slot[0] < 0) // 只有主队有属性
	{
		return 0;
	}
	const guard_slot_info *slot = GetSlot(_casting_skill_slot[0]);
	if (!slot)
	{
		return 0;
	}
	return slot->GetProp().GetGProperty()->GetProperty<int>(prop_idx);
}

void player_guard::UpdatePhaseCount()
{
	_phase_counter.Update(_guards.begin(), _guards.end(), [](decltype(_guards.begin()) it)
	{
		return it->second.GetPhase();
	});
}

void player_guard::MakeInfoLog(std::stringstream& allGuard)
{
	// 宠物tid,等级,阶段,成长值,形态,性格,阵法位置,阵法等级,宠物战力,加成战力,基础加成战力,技能总数#被动技能1,被动2...#辅助技能1,辅助技能2...;
	for (auto& kv : _guards)
	{
		tid_t guard_tid = kv.first;
		auto& guard = kv.second;
		allGuard << guard_tid << "," << guard.GetLevel() << "," << guard.GetPhase() << "," << guard.GetGrowth()
		         << "," << guard.GetShape() << "," << guard.GetEssence().nature();
		int slot_index = guard.GetSlotIndex();
		allGuard << "," << slot_index;
		if (slot_index >= 0 && GetSlot(slot_index))
		{
			allGuard << "," << GetSlot(slot_index)->GetLevel();
		}
		else
		{
			allGuard << ",0" ;
		}
		allGuard << "," << guard.GetFightCapacity() << "," << guard.GetAttachFightCapacity()
		         << "," << guard.GetBaseAttachFightCapacity()
		         << "," <<  guard.GetEssence().passive_skill_id_size() + guard.GetEssence().assist_skill_id_size() << "#";
		for (int skill_id : guard.GetEssence().passive_skill_id())
		{
			allGuard << skill_id << ",";
		}
		allGuard << "#";
		for (int skill_id : guard.GetEssence().assist_skill_id())
		{
			allGuard << skill_id << ",";
		}
		allGuard << "#" <<  guard.GetStarLevel();
		allGuard << ";";
	}
}

void player_guard::GetSlotPetFightCapacity(int& light, int& dark)
{
	light = 0;
	dark = 0;
	for (auto& kv : _guards)
	{
		if (kv.second.GetSlotIndex() == 0)
		{
			light = kv.second.GetAttachFightCapacity();
		}
		if (kv.second.GetSlotIndex() == 1)
		{
			dark = kv.second.GetAttachFightCapacity();
		}
	}
}

void player_guard::__CalcStarSlotProp(gcreature_imp *imp, int slot_index)
{
	if (slot_index < 0 || slot_index >= GUARD_STAR_SLOT_COUNT)
	{
		return;
	}
	auto& star_slot_prop = _star_slot_prop[slot_index];
	memset(&star_slot_prop, 0, sizeof(star_slot_prop));

	auto ptempl = GUARD_STAR_SLOT_CONFIG::get(SERVER_CONFIG.guard_star_slot_config_id[slot_index]);
	if (!ptempl)
	{
		return;
	}
	int slot_level = _star_data.slot(slot_index).level();
	if (slot_level <= 0 || slot_level > ptempl->configs.size())
	{
		return;
	}
	auto& config = ptempl->configs[slot_level - 1];
	if (config.basic_prop_addition_percent <= 0)
	{
		return;
	}

	property_template::data_EnhanceBaseProp enhance_prop;
	memset(&enhance_prop, 0, sizeof(enhance_prop));

	float ratio = config.basic_prop_addition_percent * .01f;
	PlayerBasePropEnhance(EBRT_GUARD_STAR, SERVER_CONFIG.guard_star_slot_addon_id[slot_index], &enhance_prop, ratio);

	star_slot_prop.baseHP = enhance_prop.pointHP;
	star_slot_prop.basePhyAtk = enhance_prop.pointPhyAtk;
	star_slot_prop.baseMagAtk = enhance_prop.pointMagAtk;
	star_slot_prop.basePhyDef = enhance_prop.pointPhyDef;
	star_slot_prop.baseMagDef = enhance_prop.pointMagDef;
	star_slot_prop.baseCritLevel = enhance_prop.pointCritLevel;
	star_slot_prop.basePierceLevel = enhance_prop.pointPierceLevel;
	star_slot_prop.baseCDReduLevel = enhance_prop.pointCDReduLevel;
	star_slot_prop.basePsychokinesisLevel = enhance_prop.pointPsychokinesisLevel;
}

void player_guard::__CalcStarTransferRate(gcreature_imp *imp)
{
	auto ptempl = GUARD_STAR_CONFIG::get(SERVER_CONFIG.guard_star_config_id);
	if (!ptempl)
	{
		return;
	}
	int star_level = _star_data.level();
	if (star_level <= 0 || star_level > ptempl->configs.size())
	{
		return;
	}
	auto& config = ptempl->configs[star_level - 1];
	_star_transfer_rate = (float)config.transfer_rate / 100.0f;
}

int player_guard::Breed(gplayer_imp *imp, int guard_id)
{
	if (!imp)
	{
		return S2C::ERR_SERVICE_UNAVILABLE;
	}
	guard_info *guard = GetGuard(guard_id);
	if (!guard)
	{
		return S2C::ERR_GUARD_NOT_FOUND;
	}
	if (!guard->IsInMarry())
	{
		return S2C::ERR_GUARD_NOT_MARRY;
	}
	if (guard->IsBreedFinish())
	{
		return S2C::ERR_GUARD_BREED_TIMEOUT;
	}
	auto spouse_data = guard->GetEssence().mutable_spouse();
	int breed_state = spouse_data->today_state();
	if (breed_state & PB::GUARD_BREED_SELF)
	{
		return S2C::ERR_GUARD_BREED_ALREADY;
	}
	int now = gmatrix::GetInstance().GetSysTime();
	int delta_days = DeltaDays(now, spouse_data->begin_time());
	if (delta_days >= spouse_data->cycle_state_size())
	{
		return S2C::ERR_GUARD_BREED_TIMEOUT;
	}
	breed_state |= PB::GUARD_BREED_SELF;
	spouse_data->set_today_state(breed_state);

	int cycle_state = spouse_data->cycle_state(delta_days);
	cycle_state |= PB::GUARD_BREED_SELF;
	spouse_data->set_cycle_state(delta_days, cycle_state);

	// notify other
	PB::ipt_guard_breed_notify notify;
	notify.set_spouse_owner(guard->GetEssence().spouse().ownerid());
	notify.set_guard_id(guard_id);
	notify.set_breed_time(now);
	GSP::GetInstance().SendDSMsg(imp->GetRoleID(), notify);

	// notify client
	guard->SendBreedUpdate(imp);

	SLOG(DEBUG, "guard_breed").P("roleid", imp->Parent()->ID.id).P("tid", guard_id).PS(delta_days);
	return 0;
}

void player_guard::OnOtherBreed(gplayer_imp *imp, const PB::ipt_guard_breed_notify& cmd)
{
	if (!imp)
	{
		return;
	}
	guard_info *guard = GetGuard(cmd.guard_id());
	if (!guard)
	{
		return;
	}
	if (!guard->IsInMarry() || guard->IsBreedFinish())
	{
		return;
	}
	auto spouse_data = guard->GetEssence().mutable_spouse();
	int breed_delta_days = DeltaDays(cmd.breed_time(), spouse_data->begin_time());
	if (breed_delta_days >= spouse_data->cycle_state_size())
	{
		return;
	}
	int now = gmatrix::GetInstance().GetSysTime();
	int now_delta_days = DeltaDays(now, spouse_data->begin_time());

	int cycle_state = spouse_data->cycle_state(breed_delta_days);
	cycle_state |= PB::GUARD_BREED_OTHER;
	spouse_data->set_cycle_state(breed_delta_days, cycle_state);
	if (breed_delta_days == now_delta_days)
	{
		int today_state = spouse_data->today_state();
		today_state |= PB::GUARD_BREED_OTHER;
		spouse_data->set_today_state(today_state);
	}
	guard->SendBreedUpdate(imp);
	SLOG(DEBUG, "guard_other_breed").P("roleid", imp->Parent()->ID.id).P("tid", cmd.guard_id()).PS(breed_delta_days).PS(now_delta_days);
}

int player_guard::SelectSkin(gplayer_imp *imp, int guard_id, int skin_id)
{
	if (!imp)
	{
		return S2C::ERR_SERVICE_UNAVILABLE;
	}
	guard_info *guard = GetGuard(guard_id);
	if (!guard)
	{
		return S2C::ERR_GUARD_NOT_FOUND;
	}
	if (guard->GetSkinID() == skin_id)
	{
		return 0;
	}
	if (skin_id != 0)
	{
		auto star_level_cfg = guard->GetStarLevelCfg();
		if (!star_level_cfg)
		{
			return S2C::ERR_GUARD_MARRY_SKIN_LOCK;
		}
		if (star_level_cfg->_available_skin_set.find(skin_id) == star_level_cfg->_available_skin_set.end())
		{
			return S2C::ERR_GUARD_MARRY_SKIN_LOCK;
		}
	}
	guard->SetSkinID(skin_id);

	PB::gp_guard_select_skin_notify proto;
	proto.set_roleid(imp->GetRoleID());
	proto.set_guard_id(guard_id);
	proto.set_skin_id(skin_id);

	if (guard == _summon_guard)
	{
		SetParentData(imp);
		imp->Runner()->RegionSend<S2C::CMD::PBS2C>(proto);
	}
	else
	{
		imp->Runner()->QuietSend<S2C::CMD::PBS2C>(proto);
	}
	SLOG(DEBUG, "guard_select_skin").P("roleid", imp->Parent()->ID.id).P("tid", guard_id).PS(skin_id);
	return 0;
}

int player_guard::SelectGfx(gplayer_imp *imp, int guard_id, int gfx_id)
{
	if (!imp)
	{
		return S2C::ERR_SERVICE_UNAVILABLE;
	}
	guard_info *guard = GetGuard(guard_id);
	if (!guard)
	{
		return S2C::ERR_GUARD_NOT_FOUND;
	}
	if (guard->GetGfxID() == gfx_id)
	{
		return 0;
	}
	if (gfx_id != 0)
	{
		auto star_level_cfg = guard->GetStarLevelCfg();
		if (!star_level_cfg)
		{
			return S2C::ERR_GUARD_MARRY_GFX_LOCK;
		}
		if (star_level_cfg->_available_gfx_set.find(gfx_id) == star_level_cfg->_available_gfx_set.end())
		{
			return S2C::ERR_GUARD_MARRY_GFX_LOCK;
		}
	}
	guard->SetGfxID(gfx_id);

	PB::gp_guard_select_gfx_notify proto;
	proto.set_roleid(imp->GetRoleID());
	proto.set_guard_id(guard_id);
	proto.set_gfx_id(gfx_id);

	if (guard == _summon_guard)
	{
		SetParentData(imp);
		imp->Runner()->RegionSend<S2C::CMD::PBS2C>(proto);
	}
	else
	{
		imp->Runner()->QuietSend<S2C::CMD::PBS2C>(proto);
	}
	SLOG(DEBUG, "guard_select_gfx").P("roleid", imp->Parent()->ID.id).P("tid", guard_id).PS(gfx_id);
	return 0;
}

void player_guard::DailyUpdate(gplayer_imp *imp)
{
	int now = gmatrix::GetInstance().GetSysTime();
	for (int guard_id : _marry_guards)
	{
		guard_info *guard = GetGuard(guard_id);
		if (!guard)
		{
			continue;
		}
		auto spouse_data = guard->GetEssence().mutable_spouse();
		if (spouse_data->today_state() == PB::GUARD_BREED_FINISH)
		{
			continue;
		}
		int delta_days = DeltaDays(now, spouse_data->begin_time());
		int new_state = delta_days >= spouse_data->cycle_state_size() ? PB::GUARD_BREED_FINISH : PB::GUARD_BREED_NONE;
		spouse_data->set_today_state(new_state);
		guard->SendBreedUpdate(imp);
	}
}

void player_guard::UnlockSecondarySlot(gplayer_imp *pimp, int slot_idx)
{
	if (slot_idx < 0 || slot_idx >= GUARD_SLOT_COUNT
	        || (slot_idx != GUARD_SLOT_SECONDARY_1_IDX && slot_idx != GUARD_SLOT_SECONDARY_2_IDX))
	{
		return;
	}
	if (!SeniorTowerRewardManager::GetInstance().IsGuardSecondarySlotUnlocked(pimp, slot_idx))
	{
		return;
	}

	auto& slot = _slots[slot_idx];
	if (slot.GetLevel() > 0)
	{
		return;
	}

	slot.SetLevel(1);
	PB::gp_result_notify notify;
	notify.set_notify(PB::gp_result_notify::NT_GUARD_SECOND_SLOT_UNLOCK);
	notify.set_retcode(0);
	notify.set_param(slot_idx);
	pimp->Runner()->CommonSend<S2C::CMD::PBS2C>(notify);
}

int player_guard::Divorce(gplayer_imp *imp, int guard_id)
{
	if (!imp)
	{
		return S2C::ERR_SERVICE_UNAVILABLE;
	}
	guard_info *guard = GetGuard(guard_id);
	if (!guard)
	{
		return S2C::ERR_GUARD_NOT_FOUND;
	}
	int ret = guard->Divorce(imp, this);
	if (ret == 0)
	{
		_marry_guards.erase(guard_id);
		PB::gp_guard_generation_update proto;
		proto.set_roleid(imp->GetRoleID());
		proto.set_guard_id(guard_id);
		proto.set_generation(guard->GetGeneration());

		if (guard == _summon_guard)
		{
			SetParentData(imp);
			imp->Runner()->RegionSend<S2C::CMD::PBS2C>(proto);
		}
		else
		{
			imp->Runner()->QuietSend<S2C::CMD::PBS2C>(proto);
		}
	}
	return ret;
}

void player_guard::GetCanMarryGuards(std::vector<int>& guards)
{
	for (auto& kv : _guards)
	{
		if (kv.second.CanMarry())
		{
			guards.push_back(kv.first);
		}
	}
}

void player_guard::OnGuardMarry(gplayer_imp *imp, guard_info *guard, const gplayer_imp *ownerImp, guard_info *pSpouse)
{
	if (!imp || !guard || !ownerImp || !pSpouse)
	{
		return;
	}
	guard->Marry(imp, ownerImp, pSpouse);
	_marry_guards.insert(guard->GetTID());
}

void player_guard::MakeRoleTradePetInfo(gplayer_imp *imp, PB::role_trade_pet_info& info)
{
	if (!imp)
	{
		return;
	}
	info.set_star_arr_lv(_star_data.level());
	for (int i = 0; i < _star_data.slot().size(); i++)
	{
		info.add_star_slot_lvs(_star_data.slot(i).level());
	}
	for (auto it = _guards.begin(); it != _guards.end(); ++it)
	{
		auto *p_add_one = info.add_pets();
		guard_info& guard = it->second;
		p_add_one->set_id(guard.GetEssence().tid());
		p_add_one->set_score(guard.GetEssence().tid());
		p_add_one->set_lv(guard.GetEssence().level());
		p_add_one->set_phase(guard.GetEssence().phase());
		p_add_one->set_growth(guard.GetEssence().growth_value());
		p_add_one->set_star_lv(guard.GetEssence().star_level());
		p_add_one->add_skills(guard.GetActiveSkillID());
		for (int i = 0; i < guard.GetEssence().assist_skill_id().size(); i++)
		{
			p_add_one->add_skills(guard.GetEssence().assist_skill_id(i));
		}
		for (int i = 0; i < guard.GetEssence().passive_skill_id().size(); i++)
		{
			p_add_one->add_chips(guard.GetEssence().passive_skill_id(i));
		}
	}
}

#undef SKILL_CD_ENTER_COMBAT_MIN
#undef SKILL_CD_ENTER_COMBAT_MAX
#undef SKILL_CD_DURING_COMBAT_MIN
#undef SKILL_CD_DURING_COMBAT_MAX
#undef ACTIVE_SKILL_DELAY
#undef DEFAULT_LEVELUP_EXP



#include "player.h"

#include "gprotoc/item_info.pb.h"
#include "gprotoc/data_DragonbornAttachProp.pb.h"
#include "gprotoc/db_dragonborn_bag_t.pb.h"
#include "gprotoc/skill_info.pb.h"
#include "gprotoc/gp_dragonborn_op_res.pb.h"
#include "gprotoc/dragonborn_break_skill_info_t.pb.h"
#include "gprotoc/gp_dragonborn_info.pb.h"
#include "gprotoc/gp_dragonborn_op.pb.h"
#include "gprotoc/common_item_info.pb.h"
#include "gprotoc/role_trade_dragonborn_info.pb.h"

#include "player_dragonborn.h"
#include "script_wrapper.h"
#include "item_manager.h"
#include "item/item_dragonborn.h"
#include "dragonborn_conf.h"
#include "personal_target_manager.h"
#include "personal_target_manager.h"
#include "player_misc.h"

player_dragonborn::player_dragonborn(/*gplayer_imp *imp*/)
	:/* _slot_level(0), _slot_fc(0), _sp_changed(false), _skill_dirty_flag(false), */_dragonborn_list(DRAGONBORN_MAX_SIZE, dragonborn_data(/*imp*/))
{
	_dragonborn_list[0].is_leader = true;
}

void player_dragonborn::HandleOperation(gplayer_imp *pImp, PB::gp_dragonborn_op& cmd)
{
	if (!GET_FUNC_SWITCH(kFuncCodeDragonborn))
	{
		pImp->error_message(GS_ERRMSGTYPE_NORMAL, SHOWERRMSG_CHAT, S2C::ERR_SERVICE_UNAVILABLE);
		return;
	}

	if (pImp->GetLevel() < DRAGON_CFG.level_limit)
	{
		return;
	}

	int rst = 0;
	switch (cmd.op())
	{
	case PB::gp_dragonborn_op::OT_EVOLUTION:
	{
		rst = EvolutionDragonborn(pImp, cmd.index(), cmd.depo());
	}
	break;
	case PB::gp_dragonborn_op::OT_BREAK:
	{
		std::set<int> list;
		for (int index : cmd.index_list())
		{
			list.insert(index);
		}
		rst = BreakDragonborn(pImp, cmd.index(), cmd.depo(), list, cmd.param1());
	}
	break;
	case PB::gp_dragonborn_op::OT_ADDEXP:
	{
		rst = AddExp(pImp, cmd.index(), cmd.depo(), cmd.param1(), cmd.param2());
	}
	break;
	case PB::gp_dragonborn_op::OT_TRAIN:
	{
		rst = TrainDragonborn(pImp, cmd.index(), cmd.depo(), cmd.param1());
	}
	break;
	case PB::gp_dragonborn_op::OT_DECOMPOSE:
	{
		std::set<int> list;
		for (int index : cmd.index_list())
		{
			list.insert(index);
		}
		rst = Decompose(pImp, list, cmd.depo());
	}
	break;
	case PB::gp_dragonborn_op::OT_LEARN_SKILL:
	{
		rst = LearnSkill(pImp, cmd.index(), cmd.depo(), cmd.param1());
	}
	break;
	case PB::gp_dragonborn_op::OT_RECRUIT:
	{
		int pool_id = cmd.param1();
		rst = RecruitDragonborn(pImp, pool_id, cmd.param2());
		if (rst != 0)
		{
			PB::gp_dragonborn_op_res res;
			res.set_op(PB::gp_dragonborn_op_res::OT_RECRUIT);
			res.set_retcode(rst);
			res.set_pool_id(pool_id);
			pImp->Runner()->QuietSend<S2C::CMD::PBS2C>(res);
			return;
		}
	}
	break;
	case PB::gp_dragonborn_op::OT_TRY_DECOMPOSE:
	{
		std::set<int> list;
		for (int index : cmd.index_list())
		{
			list.insert(index);
		}
		rst = GetDecomposeResult(pImp, cmd.param1(), list, cmd.depo());
	}
	break;
	case PB::gp_dragonborn_op::OT_SELECT_GFX:
	{
		rst = SelectGfx(pImp, cmd.index(), cmd.depo(), cmd.param1());
	}
	break;
	case PB::gp_dragonborn_op::OT_HIDE:
	{
		bool hide = cmd.param1();
		if (hide == pImp->GetSwitch(SERVER_SWITCH_TYPE, SERVER_SWITCH_INDEX_DRAGONBORN_HIDE))
		{
			break;
		}
		pImp->SetServerSwitch(SERVER_SWITCH_INDEX_DRAGONBORN_HIDE, hide);
		dragonborn_data& data = _dragonborn_list[0];
		if (data.attached)
		{
			SetParentData(pImp, data.tid, data.gfx, hide, true);
		}
	}
	}
	if (rst)
	{
		pImp->error_message(GS_ERRMSGTYPE_NORMAL, SHOWERRMSG_CHAT, rst);
	}
}

bool player_dragonborn::CheckDragonbornBedgeExist(const gplayer_imp *imp, unsigned int dragonborn_index, bool depo) const
{
	const item_list& inv = imp->GetInventory().GetInventory(depo ? GNET::IL_DRAGONBORN_DEPO : GNET::IL_DRAGONBORN);
	if (dragonborn_index >= inv.Size())
	{
		return false;
	}
	item_dragonborn *it = (item_dragonborn *)(inv[dragonborn_index]);
	if (!it)
	{
		return false;
	}
	if (ITT_DRAGONBORN_BEDGE != it->GetTemplateType())
	{
		return false;
	}
	return true;
}

const item_dragonborn *player_dragonborn::GetDragonbornBedge(const gplayer_imp *imp, unsigned int dragonborn_index, bool depo) const
{
	const item_list& inv = imp->GetInventory().GetInventory(depo ? GNET::IL_DRAGONBORN_DEPO : GNET::IL_DRAGONBORN);
	if (dragonborn_index >= inv.Size())
	{
		return NULL;
	}
	const item_dragonborn *it = dynamic_cast<const item_dragonborn *>(inv[dragonborn_index]);
	if (!it)
	{
		return NULL;
	}
	if (ITT_DRAGONBORN_BEDGE != it->GetTemplateType())
	{
		return NULL;
	}
	return it;
}

item_dragonborn *player_dragonborn::GetDragonbornBedge(gplayer_imp *imp, unsigned int dragonborn_index, bool depo)
{
	item_list& inv = imp->GetInventory().GetInventory(depo ? GNET::IL_DRAGONBORN_DEPO : GNET::IL_DRAGONBORN);
	if (dragonborn_index >= inv.Size())
	{
		return NULL;
	}
	item_dragonborn *it = dynamic_cast<item_dragonborn *>(inv[dragonborn_index]);
	if (!it)
	{
		return NULL;
	}
	if (ITT_DRAGONBORN_BEDGE != it->GetTemplateType())
	{
		return NULL;
	}
	return it;
}

void player_dragonborn::SetParentData(gplayer_imp *imp, tid_t tid, int gfx_level, bool hide, bool refresh)
{
	bool changed = (tid != imp->GetParent()->dragonborn_tid || gfx_level != imp->GetParent()->dragonborn_gfx || hide != imp->GetParent()->dragonborn_hide);
	if (tid)
	{
		imp->GetParent()->dragonborn_tid = tid;
		imp->GetParent()->dragonborn_gfx = gfx_level;
		imp->GetParent()->dragonborn_hide = hide;
		imp->GetParent()->SetObjectState2(gobject::STATE2_DRAGONBORN);
	}
	else
	{
		imp->GetParent()->dragonborn_tid = 0;
		imp->GetParent()->dragonborn_gfx = 0;
		imp->GetParent()->dragonborn_hide = false;
		imp->GetParent()->ClrObjectState2(gobject::STATE2_DRAGONBORN);
	}
	if (changed && imp->GetPropInit() && refresh)
	{
		imp->Runner()->update_object_state();

		PB::gp_dragonborn_info proto;
		proto.set_master(imp->GetParent()->ID.id);
		proto.set_tid(tid);
		proto.set_gfx_level(gfx_level);
		proto.set_hide(hide);
		imp->Runner()->RegionSend<S2C::CMD::PBS2C>(proto);
	}
}

void player_dragonborn::InsertAssistSkill(gcreature_imp *imp, int skill_id)
{
	auto iter = _assist_skill_map.find(skill_id);
	if (iter == _assist_skill_map.end())
	{
		_assist_skill_map.insert(std::make_pair(skill_id, 1));
		ConditionSuppressCombatStat(imp, SuppressCombatSystem::DRAGONBORN)
			.Then(nullptr, "player_dragonborn::InsertAssistSkill: suppress skill insert")
			.Else([skill_id, imp](gcreature_imp*) {
				object_interface oif(imp);
				imp->GetSkill().InsertSkill(skill_id, 1, oif);
			}, "player_dragonborn::InsertAssistSkill: normal skill insert");
	}
	else
	{
		iter->second++;
	}
}

void player_dragonborn::RemoveAssistSkill(gcreature_imp *imp, int skill_id)
{
	auto iter = _assist_skill_map.find(skill_id);
	if (iter == _assist_skill_map.end())
	{
		return;
	}
	if (--iter->second <= 0)
	{
		ConditionSuppressCombatStat(imp, SuppressCombatSystem::DRAGONBORN)
			.Then(nullptr, "player_dragonborn::RemoveAssistSkill: suppress skill remove")
			.Else([skill_id, imp](gcreature_imp*) {
				object_interface oif(imp);
				imp->GetSkill().RemoveSkill(skill_id, 1, oif);
			}, "player_dragonborn::RemoveAssistSkill: normal skill remove");
		_assist_skill_map.erase(iter);
	}
}

void player_dragonborn::Attach(gplayer_imp *imp, unsigned char index, bool refresh)
{
	if (index >= DRAGONBORN_MAX_SIZE)
	{
		return;
	}
	dragonborn_data& data = _dragonborn_list[index];
	if (data.attached)
	{
		return;
	}
	data.attached = true;

	property_template::data_DragonbornAttachProp& point_prop = *(property_template::data_DragonbornAttachProp *)&data.trans_prop;
	ConditionSuppressCombatStat(imp, SuppressCombatSystem::DRAGONBORN)
		.Then(nullptr, "player_dragonborn::Attach: suppress prop attach")
		.Else([&](gcreature_imp*) {
			imp->GetProperty().IncByStruct(point_prop);
		}, "player_dragonborn::Attach: normal prop attach");

	if (data.is_leader)
	{
		if (refresh)
		{
			bool hide = imp->GetSwitch(SERVER_SWITCH_TYPE, SERVER_SWITCH_INDEX_DRAGONBORN_HIDE);
			SetParentData(imp, data.tid, data.gfx, hide, refresh);
		}
		if (data.active_skill_id > 0)
		{
			ConditionSuppressCombatStat(imp, SuppressCombatSystem::DRAGONBORN)
				.Then(nullptr, "player_dragonborn::Attach: suppress active skill insert")
				.Else([data, imp](gcreature_imp*) {
					object_interface oif(imp);
					imp->GetSkill().InsertSkill(data.active_skill_id, data.active_skill_level, oif);
				}, "player_dragonborn::Attach: normal active skill insert");
		}
		for (int assist_skill_id : data.assist_skill_id)
		{
			InsertAssistSkill(imp, assist_skill_id);
		}
	}
}

void player_dragonborn::Detach(gplayer_imp *imp, unsigned char index, bool refresh)
{
	if (index >= DRAGONBORN_MAX_SIZE)
	{
		return;
	}
	dragonborn_data& data = _dragonborn_list[index];
	if (!data.attached)
	{
		return;
	}

	property_template::data_DragonbornAttachProp& point_prop = *(property_template::data_DragonbornAttachProp *)&data.trans_prop;
	ConditionSuppressCombatStat(imp, SuppressCombatSystem::DRAGONBORN)
		.Then(nullptr, "player_dragonborn::Detach: suppress prop detach")
		.Else([&](gcreature_imp*) {
			imp->GetProperty().DecByStruct(point_prop);
		}, "player_dragonborn::Detach: normal prop detach");

	if (data.is_leader)
	{
		if (refresh)
		{
			SetParentData(imp, 0, 0, false, refresh);
		}
		if (data.active_skill_id > 0)
		{
			ConditionSuppressCombatStat(imp, SuppressCombatSystem::DRAGONBORN)
				.Then(nullptr, "player_dragonborn::Detach: suppress active skill remove")
				.Else([data, imp](gcreature_imp*) {
					object_interface oif(imp);
					imp->GetSkill().RemoveSkill(data.active_skill_id, data.active_skill_level, oif);
				}, "player_dragonborn::Detach: normal active skill remove");
		}
		for (int assist_skill_id : data.assist_skill_id)
		{
			RemoveAssistSkill(imp, assist_skill_id);
		}
	}

	data.attached = false;
}

void player_dragonborn::OnDragonbornChanged(gplayer_imp *imp)
{
	/*if (_sp_changed)
	{
		_sp_changed = false;
		if (GetDragonbornBedge(imp, 0))
		{
			Detach(imp, 0);
			RebuildProp(imp, 0);
			Attach(imp, 0);
		}
	}
	RebuildSkill(imp);
	CalcFightingCapacity();*/
	//imp->GetAchievement().OnEquipDragonborn(imp);

	int total_fc = 0;
	for (int i = 0; i < 5; ++ i)
	{
		total_fc += GetInfo(DI_FIGHT_CAPACITY, i);
	}
	imp->GetAchievement().OnCommonFightingCapacityUpdate(imp, 3, total_fc);
}

void player_dragonborn::OnDragonbornGfxChange(gplayer_imp *imp, unsigned char index, bool depo, int new_gfx)
{
	if (depo)
	{
		return;
	}

	auto& data = _dragonborn_list[index];
	data.gfx = new_gfx;
	if (data.is_leader)
	{
		bool hide = imp->GetSwitch(SERVER_SWITCH_TYPE, SERVER_SWITCH_INDEX_DRAGONBORN_HIDE);
		SetParentData(imp, data.tid, data.gfx, hide, true);
	}
}

void player_dragonborn::RebuildProp(gplayer_imp *imp, unsigned char index, bool depo, bool force)
{
	item_dragonborn *it = GetDragonbornBedge(imp, index, depo);
	if (!it)
	{
		return;
	}
	//imp->SetSubPropBuildIndex(SPM_DRAGONBORN | (int)index);
	if (!depo)
	{
		it->ReBuildProp(imp, &_dragonborn_list[index], force);
	}
	else
	{
		it->ReBuildProp(imp, NULL, force);
	}
	//imp->SetSubPropBuildIndex(0);
}

void player_dragonborn::Save(archive& ar)
{
}
void player_dragonborn::Load(gplayer_imp *imp, archive& ar)
{
}

void player_dragonborn::OnLogin(gplayer_imp *imp)
{
	unsigned int equip_dragonborn_count = imp->GetInventory().GetDragonbornInv().Size();
	for (unsigned int i = 0; i < equip_dragonborn_count; ++i)
	{
		if (!GetDragonbornBedge(imp, i))
		{
			continue;
		}
		Detach(imp, i);
		RebuildProp(imp, i, false, false);
		Attach(imp, i, true);
	}
	/*if (_slot_level)
	{
		_slot_fc = Skill_Config_Man::GetInstance().GetDragonbornSkillCapacity(SLOT_SKILL, _slot_level);
		imp->GetProperty().ModifyByIndex(INDEX_DragonbornSFightingCapacity, _slot_fc, (float)_slot_fc);
	}*/
}
//nothing
void player_dragonborn::OnEnterScene(gplayer_imp *imp)
{
	/*PB::gp_dragonborn_slot pb;
	pb.set_level(_slot_level);
	imp->GetRunner()->CommonSend<S2C::CMD::PBS2C>(pb);
	OnDragonbornChanged(imp);*/
}
//nothing
void player_dragonborn::OnLeaveScene(gplayer_imp *imp)
{

}

void player_dragonborn::OnPutIn(gplayer_imp *imp, unsigned char index, bool depo, bool refresh)
{
	if (!GetDragonbornBedge(imp, index, depo))
	{
		return;
	}
	if (!depo)
	{
		Detach(imp, index);
	}
	RebuildProp(imp, index, depo, false);
	if (!depo)
	{
		Attach(imp, index, true);
	}
	if (refresh)
	{
		OnDragonbornChanged(imp);
	}
}
void player_dragonborn::OnTakeOut(gplayer_imp *imp, unsigned char index, bool depo, bool refresh)
{
	if (!GetDragonbornBedge(imp, index, depo))
	{
		return;
	}
	if (!depo)
	{
		Detach(imp, index, true);
	}
	if (refresh)
	{
		OnDragonbornChanged(imp);
	}
}

int player_dragonborn::TrainDragonborn(gplayer_imp *imp, unsigned char index, bool depo, int count)
{
	CHECK_FUNC_SWITCH_AND_RETURN_INT(kFuncCodeDragonbornTrain, imp, S2C::ERR_FUNC_CLOSED_TEMPORARY);

	if (!imp || (imp->IsRoamIn() && !GLOBAL_CONFIG.IsNormalCenterZone(GNET::g_zoneid)))
	{
		return S2C::ERR_ROAMOUT;
	}
	item_dragonborn *it = GetDragonbornBedge(imp, index, depo);
	if (!it)
	{
		return S2C::ERR_DRAGONBORN_INVALID_ITEM;
	}
	/*if (it->IsSecurityLocked())
	{
		return S2C::ERR_SECURITY_LOCKED;    //安全锁定中
	}*/
	if (!depo)
	{
		Detach(imp, index);
	}

	int old_apt[DRAGONBORN_PROP_COUNT] = {0};
	for (int i = 0; i < DRAGONBORN_PROP_COUNT; ++i)
	{
		old_apt[i] = it->GetAptitude(i);
	}

	int result = it->AptitudeTrain(imp, count);
	if (result == 0)
	{
		RebuildProp(imp, index, depo);
		if (it->CheckGfx())
		{
			OnDragonbornGfxChange(imp, index, depo, it->GetEssence().select_gfx());
		}
		imp->CmdItemInfo(depo ? GNET::IL_DRAGONBORN_DEPO : GNET::IL_DRAGONBORN, index);

		//for BI
		std::stringstream ssAptDiff, ssAptNew;
		for (int i = 0; i < DRAGONBORN_PROP_COUNT; ++i)
		{
			int new_apt = it->GetAptitude(i);
			ssAptNew << new_apt << ",";
			int diff = new_apt - old_apt[i];
			ssAptDiff << diff << ",";
		}
		BI_LOG_GLOBAL(imp->GetParent()->account.ToStr());
		SLOG(FORMAT, "dragonborn_train")
		.BI_HEADER2_GS(imp)
		.P("dragonborn_tid", it->GetTID())
		.P("index", index)
		.P("depo", depo)
		.P("train_count", count)
		.P("apt_diff", ssAptDiff.str())
		.P("cur_apt", ssAptNew.str());

		imp->GetAchievement().OnEquipDragonborn(imp);
	}
	else
	{
		//imp->Runner()->dragonborn_recast_result(-1);
	}
	if (!depo)
	{
		Attach(imp, index, true);
		OnDragonbornChanged(imp);
	}
	return result;
}

int player_dragonborn::GetDecomposeResult(gplayer_imp *imp, int type, const std::set<int>& index_list, bool depo)
{
	int location = depo ? GNET::IL_DRAGONBORN_DEPO : GNET::IL_BACKPACK;
	item_list& inv = imp->GetInventory().GetInventory(location);
	dragonborn_decompose_t result;
	int retcode = 0;
	for (auto iter = index_list.begin(); iter != index_list.end(); ++iter)
	{
		int index = *iter;
		if (index >= (int)inv.Size() || index < 0)
		{
			retcode = S2C::ERR_DRAGONBORN_COMMON_ERR;
			break;
		}
		item_dragonborn *it = dynamic_cast<item_dragonborn *>(inv[index]);
		if (!it || ITT_DRAGONBORN_BEDGE != it->GetTemplateType())
		{
			retcode = S2C::ERR_DRAGONBORN_COMMON_ERR;
			break;
		}
		if (it->IsSecurityLocked())
		{
			retcode = S2C::ERR_SECURITY_LOCKED;
			break;
		}
		int dec_ret = it->TryDecompose(imp, type, result);
		if (dec_ret != 0)
		{
			retcode = dec_ret;
			break;
		}
	}

	PB::gp_dragonborn_op_res res;
	res.set_op(PB::gp_dragonborn_op_res::OT_TRY_DECOMPOSE);
	res.set_retcode(retcode);
	res.set_value(type);
	if (retcode == 0)
	{
		for (auto& it : result.decompose_item)
		{
			auto *item = res.mutable_decompose_result()->add_items();
			item->set_tid(it.first);
			item->set_count(it.second);
		}
		res.mutable_decompose_result()->set_money(result.money);
	}
	imp->Runner()->QuietSend<S2C::CMD::PBS2C>(res);

	return 0;
}

int player_dragonborn::Decompose(gplayer_imp *imp, const std::set<int>& index_list, bool depo)
{
	CHECK_FUNC_SWITCH_AND_RETURN_INT(kFuncCodeDragonbornDecompose, imp, S2C::ERR_FUNC_CLOSED_TEMPORARY);

	int location = depo ? GNET::IL_DRAGONBORN_DEPO : GNET::IL_BACKPACK;
	item_list& inv = imp->GetInventory().GetInventory(location);
	dragonborn_decompose_t result;
	std::vector<int> remove_list;
	std::stringstream ssTids;
	std::stringstream ssDragonborns;
	int back_dragonborn_total_count = 0;
	std::map<int, int> back_dragonborn_count;
	std::map<int, std::vector<PB::dragonborn_break_skill_info_t>> back_dragonborn_skill;
	for (auto iter = index_list.begin(); iter != index_list.end(); ++iter)
	{
		int index = *iter;
		if (index >= (int)inv.Size() || index < 0)
		{
			return S2C::ERR_DRAGONBORN_COMMON_ERR;
		}
		item_dragonborn *it = dynamic_cast<item_dragonborn *>(inv[index]);
		if (!it || ITT_DRAGONBORN_BEDGE != it->GetTemplateType())
		{
			return S2C::ERR_DRAGONBORN_COMMON_ERR;
		}
		if (it->IsSecurityLocked())
		{
			return S2C::ERR_SECURITY_LOCKED;
		}
		int dec_ret = it->TryDecompose(imp, DRAGONBORN_DECOMPOSE_COMMON, result);
		if (dec_ret != 0)
		{
			return dec_ret;
		}

		int cur_break_level = it->GetBreakLevel();
		if (cur_break_level > 0)
		{
			int break_cost_num = it->GetTemplate()->_break_info[cur_break_level - 1].total_item_num;
			if (break_cost_num > 0)
			{
				back_dragonborn_count[it->GetTID()] += break_cost_num;
				for (auto& bs : it->GetEssence().break_skills())
				{
					back_dragonborn_skill[it->GetTID()].push_back(bs);
				}
				back_dragonborn_total_count += break_cost_num;
			}
		}

		remove_list.push_back(index);
		ssTids << it->GetTID() << "," << (int)it->GetLevel() << "," << it->GetEvolutionLevel() << "," << it->GetBreakLevel() << "," << it->GetAverageAptitude() << ";";
	}

	FuncInfo func_info{kFuncCodeDragonbornDecompose};
	if (result.decompose_item.size() > 0)
	{
		if (!imp->CheckCanIncItems(result.decompose_item))
		{
			return S2C::ERR_INVENTORY_IS_FULL;
		}
	}
	if (back_dragonborn_total_count > 0)
	{
		if (GetEmptySlotCount(imp) + result.decompose_item.size() < back_dragonborn_total_count)
		{
			return S2C::ERR_DRAGONBORN_BEDGE_FULL;
		}
	}
	if (result.money > 0)
	{
		if (!imp->CanIncMoney(MT_BIND, result.money))
		{
			return S2C::ERR_INVENTORY_BIND_MONEY_LIMIT;
		}
	}

	for (auto iter = remove_list.begin(); iter != remove_list.end(); ++iter)
	{
		imp->DecItemAtIndex(location, *iter, 1, func_info, true);
	}

	//开始返还材料
	if (result.decompose_item.size() > 0)
	{
		std::vector<gen_item_t> gen_list;
		for (auto iter = result.decompose_item.begin(); iter != result.decompose_item.end(); ++iter)
		{
			gen_item_t gen;
			gen.tid = iter->first;
			gen.count = iter->second;
			gen_list.push_back(gen);
		}
		imp->GenAndIncItems(IGT_DRAGONBORN, func_info, gen_list);
	}
	if (back_dragonborn_total_count > 0)
	{
		std::vector<gen_item_t> gen_list;
		for (auto& kv : back_dragonborn_count)
		{
			auto& skills = back_dragonborn_skill[kv.first];
			for (int i = 0; i < kv.second; ++ i)
			{
				gen_item_t gen;
				gen.tid = kv.first;
				if (i < skills.size())
				{
					gen.item_gen.pbinfo.mutable_dragonborn_skills()->CopyFrom(skills[i]);
				}
				gen_list.push_back(gen);
			}
			ssDragonborns << kv.first << "," << kv.second << ";";
		}
		imp->GenAndIncItems(IGT_DRAGONBORN_DECOMPOSE, func_info, gen_list);
	}
	if (result.money > 0)
	{
		imp->IncMoney(func_info, MT_BIND, result.money);
	}

	if (remove_list.size() > 0)
	{
		std::stringstream ssItems;
		for (auto& kv : result.decompose_item)
		{
			ssItems << kv.first << "," << kv.second << ";";
		}
		//for BI
		BI_LOG_GLOBAL(imp->GetParent()->account.ToStr());
		SLOG(FORMAT, "dragonborn_decompose")
		.BI_HEADER2_GS(imp)
		.P("count", remove_list.size())
		.P("tids", ssTids.str())
		.P("items", ssItems.str())
		.P("money", result.money)
		.P("back_dragonborns", ssDragonborns.str())
		;
		//imp->GetAchievement().OnDecomposeDragonborn(imp, remove_list.size());
	}
	return 0;
}

int player_dragonborn::BreakDragonborn(gplayer_imp *imp, unsigned int index, bool depo, const std::set<int>& cost_list, int dst_level)
{
	CHECK_FUNC_SWITCH_AND_RETURN_INT(kFuncCodeDragonbornBreak, imp, S2C::ERR_FUNC_CLOSED_TEMPORARY);
	if (!imp || (imp->IsRoamIn() && !GLOBAL_CONFIG.IsNormalCenterZone(GNET::g_zoneid)))
	{
		return S2C::ERR_ROAMOUT;
	}

	item_dragonborn *it = GetDragonbornBedge(imp, index, depo);
	if (!it)
	{
		return S2C::ERR_DRAGONBORN_INVALID_ITEM;
	}

	int old_level = it->GetBreakLevel();
	if (dst_level <= old_level || dst_level > it->GetTemplate()->_break_times)
	{
		return S2C::ERR_FATAL_ERR;
	}

	auto& break_config = it->GetTemplate()->_break_info;

	//计算需要的总数量
	int money_cost = 0;
	int break_num_cost = 0;
	std::vector<PB::dragonborn_break_skill_info_t> break_skills;
	for (int level = it->GetBreakLevel() + 1; level <= dst_level; ++ level)
	{
		int cost_num = break_config[level - 1].item_num;
		if (cost_num == 0)
		{
			return S2C::ERR_FATAL_ERR;
		}
		break_num_cost += cost_num;
		money_cost += DRAGON_CFG.GetBreakMoneyCost(level);
	}

	if (money_cost < 0 || money_cost > imp->GetMoney(MT_BIND))
	{
		return S2C::ERR_NOT_ENOUGH_MONEY;
	}

	//先计算需要返还的道具列表
	int cur_break_num = 0;
	dragonborn_decompose_t data;
	std::vector<int> remove_list;
	std::stringstream ssDragonborns;
	for (int cost_index : cost_list)
	{
		if (depo && index == cost_index)
		{
			//不能消耗自己
			return S2C::ERR_DRAGONBORN_BREAK_ITEM_ERR;
		}
		auto cost_dragonborn = GetDragonbornBedge(imp, cost_index, true);
		if (!cost_dragonborn || cost_dragonborn->GetTID() != it->GetTID())
		{
			return S2C::ERR_DRAGONBORN_BREAK_ITEM_ERR;
		}
		if (cost_dragonborn->IsSecurityLocked())
		{
			return S2C::ERR_SECURITY_LOCKED;
		}

		int cur_break_level = cost_dragonborn->GetBreakLevel();
		if (cur_break_level > 0)
		{
			cur_break_num += break_config[cur_break_level - 1].total_item_num + 1;
		}
		else
		{
			++ cur_break_num;
		}

		int dec_ret = cost_dragonborn->TryDecompose(imp, DRAGONBORN_DECOMPOSE_BREAK, data);
		if (dec_ret != 0)
		{
			return dec_ret;
		}

		remove_list.push_back(cost_index);
		//吃掉的龙裔被动技以及自己的被动技保存下来
		for (auto& bs : cost_dragonborn->GetEssence().break_skills())
		{
			break_skills.push_back(bs);
		}
		PB::dragonborn_break_skill_info_t self_skill;
		for (int ps : cost_dragonborn->GetEssence().passive_skill())
		{
			self_skill.add_skill_tids(ps);
		}
		break_skills.push_back(self_skill);
		ssDragonborns << (int)cost_dragonborn->GetLevel() << "," << cost_dragonborn->GetEvolutionLevel() << "," << cur_break_level << "," << cost_dragonborn->GetAverageAptitude() << ";";

		if (cur_break_num >= break_num_cost)
		{
			break;
		}
	}

	if (!imp->CheckCanIncItems(data.decompose_item))
	{
		return S2C::ERR_INVENTORY_IS_FULL;
	}

	int left_break_num = cur_break_num - break_num_cost;
	if (left_break_num > 0)
	{
		if (GetEmptySlotCount(imp) + remove_list.size() < left_break_num)
		{
			return S2C::ERR_DRAGONBORN_BEDGE_FULL;
		}
	}
	if (data.money > 0)
	{
		if (!imp->CanIncMoney(MT_BIND, data.money))
		{
			return S2C::ERR_INVENTORY_BIND_MONEY_LIMIT;
		}
	}

	// 开始消耗材料
	FuncInfo func_info{kFuncCodeDragonbornBreak};
	for (int cost_index : remove_list)
	{
		imp->DecItemAtIndex(GNET::IL_DRAGONBORN_DEPO, cost_index, 1, func_info, true);
	}
	imp->DecMoney(func_info, MT_BIND, money_cost);

	//退还材料
	std::vector<gen_item_t> gen_list;
	for (auto& iter : data.decompose_item)
	{
		gen_item_t gen;
		gen.tid = iter.first;
		gen.count = iter.second;
		gen_list.push_back(gen);
	}
	imp->GenAndIncItems(IGT_DRAGONBORN, func_info, gen_list);
	if (data.money > 0)
	{
		imp->IncMoney(func_info, MT_BIND, data.money);
	}

	//吐出龙裔
	if (left_break_num > 0)
	{
		std::vector<gen_item_t> dragonborn_gen_list;
		for (int i = 0; i < left_break_num; ++ i)
		{

			gen_item_t gen;
			gen.tid = it->GetTID();
			if (break_skills.size() > 0)
			{
				int rand = abase::Rand(0, break_skills.size() - 1);
				gen.item_gen.pbinfo.mutable_dragonborn_skills()->CopyFrom(break_skills[rand]);
				dragonborn_gen_list.push_back(gen);
			}
		}
		imp->GenAndIncItems(IGT_DRAGONBORN_DECOMPOSE, func_info, dragonborn_gen_list);
	}

	if (!depo)
	{
		Detach(imp, index);
	}

	it->SetBreakLevel(dst_level);
	//把突破吃掉的龙裔所有被动技记录下来 以后分解返还用
	for (auto& bs : break_skills)
	{
		it->GetEssence().add_break_skills()->CopyFrom(bs);
	}

	RebuildProp(imp, index, depo);
	if (it->CheckGfx())
	{
		OnDragonbornGfxChange(imp, index, depo, it->GetEssence().select_gfx());
	}

	imp->CmdItemInfo(depo ? GNET::IL_DRAGONBORN_DEPO : GNET::IL_DRAGONBORN, index);

	if (!depo)
	{
		Attach(imp, index, true);
		OnDragonbornChanged(imp);
	}

	//for BI
	BI_LOG_GLOBAL(imp->GetParent()->account.ToStr());
	SLOG(FORMAT, "dragonborn_break")
	.BI_HEADER2_GS(imp)
	.P("dragonborn_tid", it->GetTID())
	.P("index", index)
	.P("depo", depo)
	.P("inc_level", dst_level - old_level)
	.P("cur_level", dst_level)
	.P("cost_dragonborns", ssDragonborns.str());

	imp->GetAchievement().OnEquipDragonborn(imp);

	SLOG(TRACE, "item_dragonborn::Break").P("roleid", imp->GetRoleID()).P("cur_level", it->GetBreakLevel());
	return 0;
}

int player_dragonborn::EvolutionDragonborn(gplayer_imp *imp, unsigned int index, bool depo)
{
	CHECK_FUNC_SWITCH_AND_RETURN_INT(kFuncCodeDragonbornEvolution, imp, S2C::ERR_FUNC_CLOSED_TEMPORARY);
	if (!imp || (imp->IsRoamIn() && !GLOBAL_CONFIG.IsNormalCenterZone(GNET::g_zoneid)))
	{
		return S2C::ERR_ROAMOUT;
	}

	item_dragonborn *it = GetDragonbornBedge(imp, index, depo);
	if (!it)
	{
		return S2C::ERR_DRAGONBORN_INVALID_ITEM;
	}
	/*if (it->IsSecurityLocked())
	{
		return S2C::ERR_SECURITY_LOCKED;    //安全锁定中
	}*/
	if (!depo)
	{
		Detach(imp, index);
	}
	int result = it->Evolution(imp);

	if (result == 0)
	{
		RebuildProp(imp, index, depo);
		if (it->CheckGfx())
		{
			OnDragonbornGfxChange(imp, index, depo, it->GetEssence().select_gfx());
		}

		imp->CmdItemInfo(depo ? GNET::IL_DRAGONBORN_DEPO : GNET::IL_DRAGONBORN, index);

		//for BI
		BI_LOG_GLOBAL(imp->GetParent()->account.ToStr());
		SLOG(FORMAT, "dragonborn_evolution")
		.BI_HEADER2_GS(imp)
		.P("dragonborn_tid", it->GetTID())
		.P("index", index)
		.P("depo", depo)
		.P("cur_level", it->GetEvolutionLevel());

		imp->GetAchievement().OnEquipDragonborn(imp);
	}
	if (!depo)
	{
		Attach(imp, index, true);
		OnDragonbornChanged(imp);
	}

	return result;
}

int player_dragonborn::DebugSet(gplayer_imp *imp, unsigned int index, bool depo, int type, int value)
{
	if (!imp || (imp->IsRoamIn() && !GLOBAL_CONFIG.IsNormalCenterZone(GNET::g_zoneid)))
	{
		return S2C::ERR_ROAMOUT;
	}
	item_dragonborn *it = GetDragonbornBedge(imp, index, depo);
	if (!it)
	{
		return S2C::ERR_DRAGONBORN_INVALID_ITEM;
	}
	if (!depo)
	{
		Detach(imp, index);
	}
	int result = it->DebugSet(imp, type, value);

	if (result == 0)
	{
		RebuildProp(imp, index, depo);
		imp->CmdItemInfo(depo ? GNET::IL_DRAGONBORN_DEPO : GNET::IL_DRAGONBORN, index);
	}
	if (!depo)
	{
		Attach(imp, index, true);
		OnDragonbornChanged(imp);
	}
	return result;
}

void player_dragonborn::IdipDelErrDragonborn(gplayer_imp *imp, int location)
{
	item_list& inv = imp->GetInventory().GetInventory(location);
	std::map<int, int> need_remove;
	for (int i = 0; i < inv.Size(); ++i)
	{
		item_dragonborn *it = dynamic_cast<item_dragonborn *>(inv[i]);
		if (!it || ITT_DRAGONBORN_BEDGE != it->GetTemplateType())
		{
			continue;
		}
		if (it->IsSecurityLocked())
		{
			continue;
		}

		if (it->GetContent().empty())
		{
			need_remove.insert(std::make_pair(i, it->GetTID()));
		}
	}

	FuncInfo fi{kFuncCodeIdipSecure};
	for (auto it = need_remove.begin(); it != need_remove.end(); ++it)
	{
		imp->DecItemAtIndex(location, it->first, 1, FuncInfo{kFuncCodeIdipSecure, it->second}, true);
		SLOG(FORMAT, "player_dragonborn::IdipDelErrDragonborn").P("role_id", imp->GetRoleID()).P("location", location).P("index", it->first).P("tid", it->second);
	}
}

void player_dragonborn::IdipDelErrDragonborn(gplayer_imp *imp)
{
	if (!imp || (imp->IsRoamIn() && !GLOBAL_CONFIG.IsNormalCenterZone(GNET::g_zoneid)))
	{
		return;
	}
	IdipDelErrDragonborn(imp, GNET::IL_DRAGONBORN_DEPO);
	IdipDelErrDragonborn(imp, GNET::IL_DRAGONBORN);
}

int player_dragonborn::AddExp(gplayer_imp *imp, unsigned int index, bool depo, int item_id, int item_count)
{
	if (!imp || (imp->IsRoamIn() && !GLOBAL_CONFIG.IsNormalCenterZone(GNET::g_zoneid)))
	{
		return S2C::ERR_ROAMOUT;
	}

	item_dragonborn *it = GetDragonbornBedge(imp, index, depo);
	if (!it)
	{
		return S2C::ERR_DRAGONBORN_INVALID_ITEM;
	}

	if (item_count <= 0)
	{
		return S2C::ERR_NOT_ENOUGH_MATERIAL;
	}
	if (!imp->CheckItemExistAtBackpack(item_id, item_count))
	{
		return S2C::ERR_NOT_ENOUGH_MATERIAL;
	}

	const item *exp_item = item_manager::GetInstance().GetItem(item_id);
	if (!exp_item || exp_item->GetTemplateType() != ITT_DRAGONBORN_EXP)
	{
		return S2C::ERR_NOT_ENOUGH_MATERIAL;
	}
	const item_template_dragonborn_exp *dragonborn_exp_temp = ((item_dragonborn_exp *)exp_item)->GetTemplate();
	if (!dragonborn_exp_temp)
	{
		return S2C::ERR_NOT_ENOUGH_MATERIAL;
	}

	exp_t exp_add = (exp_t)dragonborn_exp_temp->_exp * (exp_t)item_count;

	//宠物条件满足才能继续
	int max_level = imp->GetLevel();
	unsigned char cur_level = it->GetLevel();
	if (cur_level >= max_level)
	{
		return S2C::ERR_DRAGONBORN_LEVEL_LIMIT;
	}

	exp_t cur_exp = it->GetExp() + exp_add;
	if (cur_exp < it->GetExp())
	{
		return S2C::ERR_DRAGONBORN_LEVEL_LIMIT;    //防止经验冒了
	}
	bool lp = false;
	unsigned int levelup_level = cur_level;
	do
	{
		if (cur_level >= max_level)
		{
			break;
		}
		exp_t lvl_exp = player_template::GetInstance().GetDragonbornLvlupExp(cur_level);
		if (lvl_exp < 1)
		{
			lvl_exp = 1;
		}
		if (cur_exp < lvl_exp)
		{
			break;    //加的经验不够升级的
		}
		lp = true;
		cur_exp -= lvl_exp;
		cur_level += 1;
		if (cur_level >= max_level)
		{
			exp_add -= cur_exp;
			cur_exp = 0;
			break;
		}
	}
	while (true);

	it->SetExp(cur_exp);
	it->SetLevel(cur_level);
	if (lp)
	{
		if (!depo)
		{
			Detach(imp, index);
		}
		RebuildProp(imp, index, depo);
		if (!depo)
		{
			Attach(imp, index, true);
			OnDragonbornChanged(imp);
		}
		GLog::log(LOG_INFO, "玩家: " FMT_I64" 的 %d 龙裔: %d 升级: %d", imp->GetParent()->ID.id, index, it->GetTID(), cur_level);

		levelup_level = cur_level - levelup_level;
	}
	it->SaveToContent();
	int cost_num = (exp_add + (exp_t)dragonborn_exp_temp->_exp - 1) / (exp_t)dragonborn_exp_temp->_exp;
	imp->DecItemAtBackpack({kFuncCodeDragonborn}, item_id, cost_num);
	imp->CmdItemInfo(depo ? GNET::IL_DRAGONBORN_DEPO : GNET::IL_DRAGONBORN, index);
	imp->GetAchievement().OnUseItem(imp, item_id, cost_num);

	return 0;
}

int player_dragonborn::LearnSkill(gplayer_imp *imp, unsigned int index, bool depo, item_index_t item_index)
{
	CHECK_FUNC_SWITCH_AND_RETURN_INT(kFuncCodeDragonbornSkill, imp, S2C::ERR_FUNC_CLOSED_TEMPORARY);
	if (!imp || (imp->IsRoamIn() && !GLOBAL_CONFIG.IsNormalCenterZone(GNET::g_zoneid)))
	{
		return S2C::ERR_ROAMOUT;
	}

	item_dragonborn *it = GetDragonbornBedge(imp, index, depo);
	if (!it)
	{
		return S2C::ERR_DRAGONBORN_INVALID_ITEM;
	}

	item_list& backpack = imp->GetInventory().GetInventory(GNET::IL_BACKPACK);
	if (item_index < 0 || item_index >= backpack.Size())
	{
		return S2C::ERR_NOT_ENOUGH_MATERIAL;
	}

	item *skill_book = backpack[item_index];
	if (!skill_book || skill_book->GetTemplateType() != ITT_DRAGONBORN_SKILL)
	{
		return S2C::ERR_NOT_ENOUGH_MATERIAL;
	}

	const item_template_dragonborn_skill *skill_temp = ((item_dragonborn_skill *)skill_book)->GetTemplate();
	if (!skill_temp || skill_temp->GetTID() <= 0)
	{
		return S2C::ERR_NOT_ENOUGH_MATERIAL;
	}

	if (!depo)
	{
		Detach(imp, index);
	}
	int replace_skill = 0;
	int new_skill = skill_temp->GetTID();
	int result = it->LearnSkill(imp, new_skill, replace_skill);

	if (result == 0)
	{
		imp->DecItemAtIndex(GNET::IL_BACKPACK, item_index, 1, {kFuncCodeDragonbornSkill}, true);
		RebuildProp(imp, index, depo);
		imp->CmdItemInfo(depo ? GNET::IL_DRAGONBORN_DEPO : GNET::IL_DRAGONBORN, index);

		PERSONAL_TARGET.TriggerTarget(imp, TT_DRAGONBORN_LEARN_SKILL);

		//for BI
		BI_LOG_GLOBAL(imp->GetParent()->account.ToStr());
		SLOG(FORMAT, "dragonborn_learnskill")
		.BI_HEADER2_GS(imp)
		.P("dragonborn_tid", it->GetTID())
		.P("index", index)
		.P("depo", depo)
		.PS(new_skill)
		.PS(replace_skill);
	}
	if (!depo)
	{
		Attach(imp, index, true);
		OnDragonbornChanged(imp);
	}

	return result;
}

int player_dragonborn::SelectGfx(gplayer_imp *imp, unsigned int index, bool depo, int gfx)
{
	if (!imp || (imp->IsRoamIn() && !GLOBAL_CONFIG.IsNormalCenterZone(GNET::g_zoneid)))
	{
		return S2C::ERR_ROAMOUT;
	}

	item_dragonborn *it = GetDragonbornBedge(imp, index, depo);
	if (!it)
	{
		return S2C::ERR_DRAGONBORN_INVALID_ITEM;
	}

	auto& ess = it->GetEssence();
	if (gfx < 0 || gfx > ess.active_gfx())
	{
		return -1;
	}

	if (gfx == ess.select_gfx())
	{
		return -2;
	}
	ess.set_select_gfx(gfx);
	it->SaveToContent(true);
	imp->CmdItemInfo(depo ? GNET::IL_DRAGONBORN_DEPO : GNET::IL_DRAGONBORN, index);

	OnDragonbornGfxChange(imp, index, depo, gfx);
	return 0;
}

int player_dragonborn::GetEmptySlotCount(gplayer_imp *pImp) const
{
	auto& depo = pImp->GetInventory().GetInventory(GNET::IL_DRAGONBORN_DEPO);
	auto& att = pImp->GetInventory().GetInventory(GNET::IL_DRAGONBORN);
	return depo.GetEffectSize() - depo.GetItemSlotCount() - att.GetItemSlotCount();
}

int player_dragonborn::RecruitDragonborn(gplayer_imp *pImp, int pool_id, bool is_ten)
{
	if (!GET_FUNC_SWITCH(kFuncCodeDragonbornRecruit))
	{
		return S2C::ERR_SERVICE_UNAVILABLE;
	}
	const auto *pool_config = DRAGON_CFG.GetRecruitConfig(pool_id);
	if (!pool_config)
	{
		return S2C::ERR_SERVICE_UNAVILABLE;
	}
	if (pool_config->activity_id > 0 && !activity_manager::GetInstance().IsActivityOpen(pool_config->activity_id))
	{
		return S2C::ERR_DRAGONBORN_RECRUIT_NOT_OPEN;
	}
	const int total_count = is_ten ? 10 : 1;
	if (pool_config->backpack_free_slot_num > 0)
	{
		if (pImp->GetInventory().GetInventory(GNET::IL_BACKPACK).GetEmptySlotCount() < (is_ten ? pool_config->backpack_free_slot_num : 1))
		{
			return S2C::ERR_PACKAGE_LESS_SLOT;
		}
	}
	//检查下有没有免费次数
	bool use_free = false;
	do
	{
		if (is_ten)
		{
			break; //十连抽不能用免费次数
		}
		//如果没有设置免费次数限制和CD 不能进行免费使用
		if (!pool_config->free_cool_down_id)
		{
			break;
		}
		if (!pImp->TestCoolDown(pool_config->free_cool_down_id))
		{
			break;
		}
		use_free = true;
	}
	while (0);
	if (!use_free && pool_config->common_use_limit_id > 0 && !pImp->GetCommonUseLimit().TestUseLimit(pool_config->common_use_limit_id, total_count))
	{
		return S2C::ERR_COMMON_USE_LIMIT;
	}

	bool discount = false;
	if (is_ten && pImp->GetCommonUseLimit().TestUseLimit(pool_config->discount_10_common_uselimit_id))
	{
		discount = true;
	}

	const int cost_count = is_ten ? (discount ? pool_config->discount_10_cost_item_count : 10) : 1;
	ITEM_CHECK_INFO info;
	if (!use_free)
	{
		if (!pImp->CheckItemExistAtBackpack2(info, pool_config->cost_item_id, is_ten ? cost_count : 1))
		{
			return S2C::ERR_NOT_ENOUGH_MATERIAL;
		}
	}

	if (GetEmptySlotCount(pImp) < total_count)
	{
		return S2C::ERR_DRAGONBORN_BEDGE_FULL;
	}

	std::multimap<int, int> rets;
	for (int i = 0; i < total_count; ++i)
	{
		int index = pImp->GetDrawSeqInfo().Draw(pImp, pool_config->seq_index);
		if (index < 0)
		{
			return S2C::ERR_DRAGONBORN_RECRUIT_ERR;
		}

		auto it_reward = pool_config->rewards.find(index);
		if (it_reward == pool_config->rewards.end())
		{
			return S2C::ERR_DRAGONBORN_RECRUIT_ERR;
		}

		auto& rewards = it_reward->second;
		int reward_index = abase::RandSelect(&rewards[0].weight, sizeof(rewards[0]), rewards.size());
		rets.insert(std::make_pair(rewards[reward_index].item_tid, rewards[reward_index].item_count));
	}

	//如果不是免费扣除消耗
	FuncInfo func_info{kFuncCodeDragonbornRecruit, pool_id};
	if (!use_free)
	{
		pImp->DecItem2(info, func_info);
		pImp->GetAchievement().OnUseItem(pImp, info.GetTid(), info.GetCount());
	}
	else
	{
		pImp->SetCoolDown(pool_config->free_cool_down_id, pool_config->free_cool_down_time * 1000);
	}

	if (!use_free && pool_config->common_use_limit_id > 0)
	{
		pImp->GetCommonUseLimit().AddUseLimit(pImp, pool_config->common_use_limit_id, total_count);
	}
	if (discount)
	{
		pImp->GetCommonUseLimit().AddUseLimit(pImp, pool_config->discount_10_common_uselimit_id, 1);
	}

	//发奖
	std::vector<gen_item_t> gen_list;
	PB::gp_dragonborn_op_res res;
	int gold_quality_count = 0;
	for (auto it = rets.begin(); it != rets.end(); ++it)
	{
		gen_item_t gen;
		gen.tid = it->first;
		gen.count = it->second;
		gen_list.push_back(gen);
		PB::common_item_info *cii = res.add_items();
		cii->set_tid(it->first);
		cii->set_count(it->second);
		if (item_manager::GetInstance().GetItemQuality(it->first) >= 4)
		{
			gold_quality_count++;
		}
	}
	pImp->GenAndIncItems(IGT_DRAGONBORN, func_info, gen_list);
	if (gold_quality_count > 0)
	{
		PERSONAL_TARGET.TriggerTarget(pImp, TT_DRAGONBORN_GET_GOLD_QUALITY, gold_quality_count);
	}

	int reward_id = is_ten ? pool_config->gain_reward_10_id : pool_config->gain_reward_id;
	if (reward_id > 0)
	{
		pImp->DeliverGeneralReward(func_info, GRANT_REWARD_TYPE_NO, reward_id, NULL, 0);
	}

	if (!use_free)
	{
		PERSONAL_TARGET.TriggerTarget(pImp, TT_RECRUIT_DRAGON_BORN, info.GetDecCount());
	}

	pImp->GetAchievement().OnDrawDragon(pImp, pool_config->cost_item_id, total_count);

	//通知客户端结果
	res.set_op(PB::gp_dragonborn_op_res::OT_RECRUIT);
	res.set_pool_id(pool_id);
	pImp->Runner()->QuietSend<S2C::CMD::PBS2C>(res);
	return 0;
}

void player_dragonborn::FillAttackMsg(const XID& target, attack_msg& attack)
{
	if (_dragonborn_list.empty() || !_dragonborn_list[0].attached || _dragonborn_list[0].active_skill_id <= 0)
	{
		return;
	}
	attack.attack_physic = std::max(attack.attack_physic, attack.attack_magic);
	attack.attack_magic = attack.attack_physic;

	auto& prop = _dragonborn_list[0].prop;

	attack.base_attack_physic = prop.curPhyAtk;
	attack.base_attack_magic = prop.curMagAtk;
}

bool player_dragonborn::CanActiveBuffSkill(int skill_id) const
{
	if (_dragonborn_list.empty() || !_dragonborn_list[0].attached || _dragonborn_list[0].active_skill_id <= 0)
	{
		return false;
	}
	if (skill_id == _dragonborn_list[0].active_skill_id)
	{
		return true;
	}
	return false;
}

int player_dragonborn::GetPropByIdx(int skill_id, int prop_idx) const
{
	if (_dragonborn_list.empty() || !_dragonborn_list[0].attached || _dragonborn_list[0].active_skill_id <= 0)
	{
		return 0;
	}

	int index = DragonbornProp::GetIndex(prop_idx);
	if (index < 0)
	{
		return 0;
	}
	int type;
	int offset = DragonbornProp::GetOffsetAndType(index, type);
	if (type == property_template::PT_TYPE_INT)
	{
		int *pProp = (int *)&_dragonborn_list[0].prop;
		return *(int *)(pProp + offset);
	}
	return 0;
}

int player_dragonborn::GetInfo(int type, int index, int param) const
{
	if (index < 0 || index >= DRAGONBORN_MAX_SIZE)
	{
		return 0;
	}
	if (_dragonborn_list.empty() || !_dragonborn_list[index].attached || _dragonborn_list[index].active_skill_id <= 0)
	{
		return 0;
	}

	if (type == DI_BREAK_LEVEL)
	{
		return _dragonborn_list[index].break_level;
	}
	else if (type == DI_FIGHT_CAPACITY)
	{
		return _dragonborn_list[index].prop.FightingCapacity;
	}
	else if (type == DI_SKILL_BREAK_FC)
	{
		if (_dragonborn_list[0].active_skill_id == param)
		{
			return DRAGON_CFG.GetDragonbornBreakSkillFc(param, _dragonborn_list[0].break_level);
		}
	}

	return 0;
}

void player_dragonborn::LogAttachInfo(gplayer_imp *imp)
{
	//for BI
	BI_LOG_GLOBAL(imp->GetParent()->account.ToStr());

	DEFINE_slogger(FORMAT, "dragonborn_attach");
	slogger.BI_HEADER2_GS(imp);

	unsigned int equip_dragonborn_count = imp->GetInventory().GetDragonbornInv().Size();
	for (unsigned int i = 0; i < equip_dragonborn_count; ++i)
	{
		int id = 0, fc = 0, level = 0, break_level = 0, evo_level = 0;
		std::stringstream ssApt;
		item_dragonborn *it = GetDragonbornBedge(imp, i, false);
		if (it)
		{
			id = it->GetTID();
			fc = it->GetEssence().prop().fightingcapacity();
			level = it->GetLevel();
			break_level = it->GetBreakLevel();
			evo_level = it->GetEvolutionLevel();
			for (int i = 0; i < DRAGONBORN_PROP_COUNT; ++i)
			{
				int apt = it->GetAptitude(i);
				ssApt << apt << ",";
			}
		}

#define DRAGON_TLOG(index, name, value) \
		{ std::stringstream tmp; \
		tmp << name << "_" << index; \
		slogger.P(tmp.str().c_str(), value); }

		DRAGON_TLOG(i + 1, "tid", id)
		DRAGON_TLOG(i + 1, "fc", fc)
		DRAGON_TLOG(i + 1, "level", level)
		DRAGON_TLOG(i + 1, "break_level", break_level)
		DRAGON_TLOG(i + 1, "evo_level", evo_level)
		DRAGON_TLOG(i + 1, "apt", ssApt.str())

#undef DRAGON_TLOG
	}
}

int player_dragonborn::GetAttachAverageAptitude()
{
	int total_apt = 0;
	int total_count = 0;
	for (auto it = _dragonborn_list.begin(); it != _dragonborn_list.end(); ++it)
	{
		if (!it->attached)
		{
			continue;
		}
		total_apt += it->average_apt;
		++ total_count;
	}
	if (total_count > 0)
	{
		return total_apt / total_count;
	}
	return 0;
}

int player_dragonborn::GetAttachEvoBreakCost(gplayer_imp *imp)
{
	int total_cost = 0;
	unsigned int equip_dragonborn_count = imp->GetInventory().GetDragonbornInv().Size();
	for (unsigned int i = 0; i < equip_dragonborn_count; ++i)
	{
		item_dragonborn *it = GetDragonbornBedge(imp, i, false);
		if (it)
		{
			total_cost += it->GetEvoAndBreakCostItemCount();
		}
	}
	return total_cost;
}

void player_dragonborn::OnDragonbornAttachChange(gplayer_imp *imp)
{
	// 有上下阵操作
	imp->GetAchievement().OnEquipDragonborn(imp);

	LogAttachInfo(imp);
}

void player_dragonborn::MakeComplicatedData(gplayer_imp *imp, PB::db_dragonborn_bag_t& complicated_data)
{
	imp->GetInventory().GetDragonbornInv().SaveToPB(complicated_data);
}

void player_dragonborn::MakeRoleTradeDragonbornInfo(gplayer_imp *imp, PB::role_trade_dragonborn_info& info) const
{
	if (!imp)
	{
		return;
	}
	unsigned int equip_dragonborn_count = imp->GetInventory().GetDragonbornInv().Size();
	for (unsigned int i = 0; i < equip_dragonborn_count; ++i)
	{
		const item_dragonborn *it = GetDragonbornBedge(imp, i, false);
		if (it)
		{
			auto *p_add_one = info.add_dragonborns();
			p_add_one->set_id(it->GetTID());
			p_add_one->set_score(it->GetEssence().prop().fightingcapacity());
			p_add_one->set_lv(it->GetLevel());
			p_add_one->set_break_lv(it->GetBreakLevel());
			p_add_one->set_evo_lv(it->GetEvolutionLevel());
			for (int i = 0; i < it->GetEssence().passive_skill().size(); ++i)
			{
				p_add_one->add_skills(it->GetEssence().passive_skill(i));
			}
			for (int i = 0; i < DRAGONBORN_PROP_COUNT; ++i)
			{
				p_add_one->add_aptitudes(it->GetAptitude(i));
			}
		}
	}
}

item_dragonborn *player_dragonborn::GetTidDragonborn( gplayer_imp *imp, int& index, bool& depo, tid_t tid)
{
	depo = false;
	index = 0;
	//int offset = 0;
	item_dragonborn *it = nullptr;

	for (int i = 0; i < 2; ++i)
	{
		item_list& inv = imp->GetInventory().GetInventory( i ? GNET::IL_DRAGONBORN_DEPO : GNET::IL_DRAGONBORN);
		inv.ForEachItems([&it, tid, i, &index, &depo](const item_list * inv, int inv_index, item * _item)
		{
			item_dragonborn *item_drag = dynamic_cast<item_dragonborn *>(_item);
			if (!item_drag)
			{
				return;
			}
			if (ITT_DRAGONBORN_BEDGE != item_drag->GetTemplateType())
			{
				return;
			}
			if (item_drag->GetTID() != tid)
			{
				return;
			}
			if (!it)
			{
				it = item_drag;
				depo = i;
				index = inv_index;
				return;
			}
			it = it->GetLevel() < item_drag->GetLevel() ? item_drag : it;
			depo = it->GetLevel() < item_drag->GetLevel() ? i : depo;
			index = it->GetLevel() < item_drag->GetLevel() ? inv_index : index;

		});
	}

	return it;
}
int player_dragonborn::IDIPResetLevel(gplayer_imp *imp, tid_t tid, int level)
{
	if (!imp)
	{
		return 1;
	}
	if (level < 0)
	{
		return 2;
	}
	//处理对应tid最高等级的龙裔
	int index = 0;
	bool depo = false;
	item_dragonborn *it = GetTidDragonborn(imp, index, depo, tid);

	if (!it)
	{
		LOG_TRACE("player_dragonborn::IDIPResetLevel::roleid=%ld:tid=%d not reach condition", imp->GetRoleID(), tid);
		return 3;
	}

	level = imp->GetLevel() < level ? imp->GetLevel() : level;

	it->SetExp(0);
	it->SetLevel(level);

	if (!depo)
	{
		Detach(imp, index);
	}
	RebuildProp(imp, index, depo);
	if (!depo)
	{
		Attach(imp, index, true);
		OnDragonbornChanged(imp);
	}
	LOG_TRACE("player_dragonborn::IDIPResetLevel::roleid=%ld:tid=%d:index=%d:depo=%d:level=%d", imp->GetRoleID(), tid, index, depo, it->GetLevel());
	it->SaveToContent();
	imp->CmdItemInfo(depo ? GNET::IL_DRAGONBORN_DEPO : GNET::IL_DRAGONBORN, index);

	return 0;
}
int  player_dragonborn::IDIPEvolutionDragonborn(gplayer_imp *imp, tid_t tid, int level)
{
	if (!imp)
	{
		return 1;
	}
	if (level < 0)
	{
		return 2;
	}
	//处理对应tid最高等级的龙裔
	int index = 0;
	bool depo = false;
	item_dragonborn *it = GetTidDragonborn(imp, index, depo, tid);

	if (!it)
	{
		LOG_TRACE("player_dragonborn::IDIPEvolutionDragonborn::roleid=%ld:tid=%d not reach condition", imp->GetRoleID(), tid);
		return 3;
	}

	if (!depo)
	{
		Detach(imp, index);
	}
	int result = it->IDIPEvolution(imp, level);

	if (result == 0)
	{
		RebuildProp(imp, index, depo);
		if (it->CheckGfx())
		{
			OnDragonbornGfxChange(imp, index, depo, it->GetEssence().select_gfx());
		}

		imp->CmdItemInfo(depo ? GNET::IL_DRAGONBORN_DEPO : GNET::IL_DRAGONBORN, index);

		LOG_TRACE("player_dragonborn::IDIPEvolutionDragonborn::roleid=%ld:tid=%d:index=%d:evolutionlevel=%d", imp->GetRoleID(), tid, index, it->GetEvolutionLevel());
		imp->GetAchievement().OnEquipDragonborn(imp);
	}
	if (!depo)
	{
		Attach(imp, index, true);
		OnDragonbornChanged(imp);
	}

	return result;
}
int player_dragonborn::IDIPBreakDragonborn(gplayer_imp *imp, int tid,  int level)
{
	if (!imp)
	{
		return 1;
	}
	if (level < 0)
	{
		return 2;
	}
	//处理对应tid最高等级的龙裔

	int index = 0;
	bool depo = false;

	item_dragonborn *it = GetTidDragonborn(imp, index, depo, tid);

	if (!it)
	{
		LOG_TRACE("player_dragonborn::IDIPBreakDragonborn::roleid=%ld:tid=%d not reach condition", imp->GetRoleID(), tid);
		return 3;
	}

	if (level > it->GetTemplate()->_break_times)
	{
		level = it->GetTemplate()->_break_times;
	}

	if (!depo)
	{
		Detach(imp, index);
	}

	it->SetBreakLevel(level);


	RebuildProp(imp, index, depo);
	if (it->CheckGfx())
	{
		OnDragonbornGfxChange(imp, index, depo, it->GetEssence().select_gfx());
	}

	imp->CmdItemInfo(depo ? GNET::IL_DRAGONBORN_DEPO : GNET::IL_DRAGONBORN, index);

	if (!depo)
	{
		Attach(imp, index, true);
		OnDragonbornChanged(imp);
	}

	imp->GetAchievement().OnEquipDragonborn(imp);
	LOG_TRACE("player_dragonborn::IDIPBreakDragonbor::roleid=%ld:tid=%d:index=%d:level=%d", imp->GetRoleID(), tid, index, it->GetBreakLevel());
	return 0;
}

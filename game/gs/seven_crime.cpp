#include "seven_crime.h"
#include "debug/print.h"
#include "gprotoc/gp_item_combine.pb.h"
#include "gprotoc/gp_seven_crime_re.pb.h"
#include "gprotoc/data_EnhanceBaseProp.pb.h"
#include "gprotoc/gp_item_combine_advance.pb.h"
#include "gprotoc/gp_seven_crime.pb.h"
#include "gprotoc/equip_seven_crime_t.pb.h"
#include "gprotoc/player_seven_crime_t.pb.h"
#include "gprotoc/role_trade_seven_crime_info.pb.h"

#include "player.h"
#include "item/item_equipment.h"
#include "player_misc.h"

namespace
{
unsigned int GetSevenCrimeFunctionalLabelMask()
{
	const int SevenCrimeFunctionalLabel = 15; // 来自skillconvex.h中的SKILL_FUNCTIONAL_LABEL_15，其表示七戒
	unsigned int mask = (1 << (SevenCrimeFunctionalLabel - 1));
	return mask;
}
}

bool seven_crime_config::Load()
{
	try
	{
		LuaTableNode root;
		if (!LuaConfReader::parseFromFile("./config/script/seven_crime.lua", "_G", root))
		{
			return false;
		}

		_min_player_level = (int)root["min_player_level"];
		_unidentified_speak_id = (int)root["unidentified_speak_id"];

		root["new_prop_need_item"].Fetch(_new_prop_need_item);
		root["batter_prop_need_item"].Fetch(_better_prop_need_item);

		root["sacrifice_slot_num"].Fetch(_sacrifice_slot_num);
		_sacrifice_slot_crime_num = (int)root["sacrifice_slot_crime_num"];

		root["sword_info"].Fetch(_sword_info);

		root["crime_type_info"].Fetch(_crime_type_info);
		_all_crime_type.clear();
		for (auto& a : _crime_type_info)
		{
			_all_crime_type.emplace_back(a.first);
		}

		root["equipment_crime_level"].Fetch(_equipment_crime_level);

		root["crime_quality_prop_info"].Fetch(_crime_quality_prop_info);
		_quality_rate_count = 0;
		_crime_quality_rate.clear();
		for (auto it = _crime_quality_prop_info.begin(); it != _crime_quality_prop_info.end(); ++it)
		{
			_quality_rate_count += it->second.rate;
			_crime_quality_rate[_quality_rate_count] = it->first;
		}

		_main_sword_prop_rate = (float)root["main_sword_prop_rate"];
		root["sword_fighting_capacity"].Fetch(_sword_fighting_capacity);

		std::vector<crime_prop_info_t> tmp_crime_prop_info;
		root["crime_prop_info"].Fetch(tmp_crime_prop_info);
		_crime_prop_rate_count = 0;
		_crime_special_prop_rate_count = 0;
		for (auto& a : tmp_crime_prop_info)
		{
			if (a.prop_type == 3)//特殊属性
			{
				_crime_special_prop_rate_count += a.prop_rate;
				_crime_special_prop_info.emplace_back(a);
				_crime_special_prop_value[a.prop_id] = a.value_tab;
				_crime_special_prop_score[a.prop_id] = a.score_tab;
			}
			else
			{
				_crime_prop_rate_count += a.prop_rate;
				_crime_prop_info.emplace_back(a);
			}
		}
		root["crime_prop_value_rate_tab"].Fetch(_crime_prop_value_rate_tab);
	}
	catch (std::exception& e)
	{
		__PRINTF("seven_crime.lua load exception:%s\n", e.what());
		return false;
	}
	catch (...)
	{
		__PRINTF("seven_crime.lua load failed!\n");
		return false;
	}
	return true;
}

int seven_crime_config::GetSowrdFightingCapacity(int sword_level) const
{
	int a = sword_level / 5 + 1;
	auto it = _sword_fighting_capacity.find(a);
	if (it == _sword_fighting_capacity.end())
	{
		return 0;
	}
	return it->second;
}

int seven_crime_config::GetCrimeTypeFightingCapacity(int crime_type, int crime_quality, int crime_level) const
{
	auto it1 = _crime_type_info.find(crime_type);
	if (it1 == _crime_type_info.end())
	{
		return 0;
	}
	const crime_type_info_t& crime_info = it1->second;

	auto it2 = crime_info.quality_level_score.find(crime_quality);
	if (it2 == crime_info.quality_level_score.end())
	{
		return 0;
	}
	const std::vector<int>& level_score = it2->second;

	if (crime_level <= 0 || crime_level > level_score.size())
	{
		return 0;
	}
	return level_score[crime_level - 1];
}

bool seven_crime_config::GetCrimeSkillAndPower(int crime_type, int crime_quality, int crime_level, int& skill_id, int& power) const
{
	auto it1 = _crime_type_info.find(crime_type);
	if (it1 == _crime_type_info.end())
	{
		return false;
	}
	const crime_type_info_t& crime_info = it1->second;
	skill_id = crime_info.skill_id;

	auto it2 = crime_info.quality_level_power.find(crime_quality);
	if (it2 == crime_info.quality_level_power.end())
	{
		return false;
	}
	const std::vector<int>& level_power = it2->second;

	if (crime_level <= 0 || crime_level > level_power.size())
	{
		return false;
	}
	power = level_power[crime_level - 1];

	return skill_id > 0 && power > 0;
}

int seven_crime_config::GetCrimeSpecialPropValue(int prop_id, int crime_level) const
{
	auto it = _crime_special_prop_value.find(prop_id);
	if (it == _crime_special_prop_value.end())
	{
		return 0;
	}
	int idx = crime_level - 1;
	if (idx < 0 || idx >= it->second.size())
	{
		return 0;
	}
	return it->second[idx];
}

int seven_crime_config::GetCrimeSpecialPropScore(int prop_id, int crime_level) const
{
	auto it = _crime_special_prop_score.find(prop_id);
	if (it == _crime_special_prop_score.end())
	{
		return 0;
	}
	int idx = crime_level - 1;
	if (idx < 0 || idx >= it->second.size())
	{
		return 0;
	}
	return it->second[idx];
}

int seven_crime_config::GetSacrificeSlotNum(int role_level) const
{
	auto it = _sacrifice_slot_num.lower_bound(role_level);
	if (it == _sacrifice_slot_num.end())
	{
		return 0;
	}
	return it->second;
}

int seven_crime_config::GetSacrificeSlotCrimeNum() const
{
	return _sacrifice_slot_crime_num;
}

bool seven_crime_config::GenCrimeTypeAndLevel(int equipment_level, int& crime_type, int& crime_level) const
{
	if (_all_crime_type.empty())
	{
		return false;
	}
	auto it = _equipment_crime_level.lower_bound(equipment_level);
	if (it == _equipment_crime_level.end())
	{
		return false;
	}

	int r = abase::Rand(0, _all_crime_type.size() - 1);
	crime_type = _all_crime_type[r];
	crime_level = it->second;
	return crime_type > 0 && crime_level > 0;
}

int seven_crime_config::_GetPropNum(int quality) const
{
	auto it = _crime_quality_prop_info.find(quality);
	if (it == _crime_quality_prop_info.end())
	{
		return 0;
	}
	return it->second.prop_num;
}

bool seven_crime_config::GenCrime(PB::equip_seven_crime_t& d) const
{
	int type = d.type();
	int level = d.level();
	if (type <= 0 || level <= 0)
	{
		return false;
	}

	int r = abase::Rand(1, _quality_rate_count);
	auto it = _crime_quality_rate.lower_bound(r);
	if (it == _crime_quality_rate.end())
	{
		return false;
	}
	int quality = it->second;
	int prop_num = _GetPropNum(quality);

	if (quality <= 0 || prop_num <= 0)
	{
		return false;
	}
	if (_crime_prop_rate_count <= 0 || _crime_prop_info.empty())
	{
		return false;
	}

	d.Clear();
	bool succ = GenCrimeProp(quality, prop_num, level, false, *d.mutable_prop());
	if (!succ)
	{
		d.Clear();
		d.set_type(type);
		d.set_level(level);
		return false;
	}

	d.set_type(type);
	d.set_quality(quality);
	d.set_level(level);
	d.set_fighting_capacity(GetCrimeTypeFightingCapacity(type, quality, level));
	return true;
}

int seven_crime_config::_GetSpecialPropRate(int quality, bool better) const
{
	auto it = _crime_quality_prop_info.find(quality);
	if (it == _crime_quality_prop_info.end())
	{
		return 0;
	}
	return better ? it->second.better_special_rate : it->second.special_rate;
}

bool player_seven_crime::_HasSpecialProp(const PB::equip_seven_crime_t::crime_prop_set_t& prop) const
{
	for (auto& a : prop.prop_arr())
	{
		if (a.type() == 3)
		{
			return true;
		}
	}
	return false;
}

bool seven_crime_config::GenCrimeProp(int quality, int prop_num, int crime_level, bool better, PB::equip_seven_crime_t::crime_prop_set_t& prop) const
{
	int rate_count = _crime_prop_rate_count;
	std::vector<crime_prop_info_t> conf_prop_arr = _crime_prop_info;
	for (int i = 0; i < prop_num; ++i)
	{
		int r = abase::Rand(1, rate_count);
		for (auto& conf_prop : conf_prop_arr)
		{
			if (r <= conf_prop.prop_rate)
			{
				PB::equip_seven_crime_t::crime_prop_t *pb_prop = prop.add_prop_arr();
				if (pb_prop == NULL)
				{
					return false;
				}
				pb_prop->set_type(conf_prop.prop_type);
				pb_prop->set_id(conf_prop.prop_id);

				if (crime_level > 0 && crime_level <= conf_prop.value_rate_tab.size())
				{
					int tab_id = conf_prop.value_rate_tab[crime_level - 1];
					pb_prop->set_value(_GetCrimePropValue(tab_id, better));
				}

				rate_count -= conf_prop.prop_rate;
				conf_prop.prop_rate = 0;
				break;
			}
			r -= conf_prop.prop_rate;
		}
	}
	if (prop.prop_arr().size() != prop_num)
	{
		return false;
	}

	//特殊属性
	int rate = _GetSpecialPropRate(quality, better);
	if (rate > 0)
	{
		if (abase::Rand(1, 1000) <= rate)
		{
			int r = abase::Rand(1, _crime_special_prop_rate_count);
			for (auto& conf_prop : _crime_special_prop_info)
			{
				if (r <= conf_prop.prop_rate)
				{
					PB::equip_seven_crime_t::crime_prop_t *pb_prop = prop.add_prop_arr();
					if (pb_prop == NULL)
					{
						return false;
					}
					pb_prop->set_type(conf_prop.prop_type);
					pb_prop->set_id(conf_prop.prop_id);
					break;
				}
				r -= conf_prop.prop_rate;
			}
		}
	}

	_CalcCrimePropFightingCapacity(crime_level, prop);
	return true;
}

void seven_crime_config::_CalcCrimePropFightingCapacity(int crime_level, PB::equip_seven_crime_t::crime_prop_set_t& prop) const
{
	float f = 0;
	float f_special = 0;
	for (int i = 0; i < prop.prop_arr_size(); ++i)
	{
		const PB::equip_seven_crime_t::crime_prop_t& p = prop.prop_arr(i);
		if (p.type() == 1)
		{
			switch (p.id())
			{
			case 1://生命
				f += p.value() * 0.005;
				break;
			case 4://物理防御
				f += p.value() * 0.4;
				break;
			case 5://魔法防御
				f += p.value() * 0.4;
				break;

			case 6://暴击等级
				f += p.value() * 0.2;
				break;
			case 7://多重打击
				f += p.value() * 0.2;
				break;
			case 26://冷却缩减
				f += p.value() * 0.2;
				break;
			case 8://破甲等级
				f += p.value() * 0.2;
				break;
			case 10://暴击抗性
				f += p.value() * 0.2;
				break;

			case 20://体质
				f += p.value() * 4;
				break;
			case 23://灵巧
				f += p.value() * 4;
				break;
			case 24://洞察
				f += p.value() * 4;
				break;

			case 163://罪责强度
				f += p.value() * 3;
				break;
			case 164://罪责抵抗
				f += p.value() * 3;
				break;
			}
		}
		else if (p.type() == 2)
		{
			switch (p.id())
			{
			case 1://物理攻击/魔法攻击
				f += p.value() * 0.6;
				break;
			case 2://主属性，力量/智力
				f += p.value() * 4;
				break;
			}
		}
		else
		{
			f_special += GetCrimeSpecialPropScore(p.id(), crime_level);
		}
	}
	prop.set_fighting_capacity(f + f_special);
	prop.set_special_fighting_capacity(f_special);
}

int seven_crime_config::_GetCrimePropValue(int tab_id, bool better) const
{
	auto it = _crime_prop_value_rate_tab.find(tab_id);
	if (it == _crime_prop_value_rate_tab.end())
	{
		return 0;
	}
	const crime_prop_value_rate_tab_t& tab_info = it->second;
	if (tab_info.value_map.empty())
	{
		return 0;
	}

	std::map<int, std::tuple<int, int>>::const_iterator it2;
	if (better)
	{
		it2 = tab_info.value_map.end();
		it2 --;
	}
	else
	{
		int r = abase::Rand(1, tab_info.rate_count);
		it2 = tab_info.value_map.lower_bound(r);
		if (it2 == tab_info.value_map.end())
		{
			return 0;
		}
	}
	const std::tuple<int, int>& range = it2->second;
	int min = std::get<0>(range);
	int max = std::get<1>(range);
	if (min >= max)
	{
		return 0;
	}
	return abase::Rand(min, max);
}

int seven_crime_config::GetPropQuality(int crime_level, const PB::equip_seven_crime_t::crime_prop_t& cp) const
{
	int prop_type = cp.type();
	int prop_id = cp.id();
	int prop_value = cp.value();
	int prop_tab = 0;
	for (const auto& prop_conf : _crime_prop_info)
	{
		if (prop_conf.prop_type == prop_type && prop_conf.prop_id == prop_id)
		{
			if (crime_level > 0 && crime_level <= prop_conf.value_rate_tab.size())
			{
				prop_tab = prop_conf.value_rate_tab[crime_level - 1];
			}
			break;
		}
	}
	if (prop_tab <= 0)
	{
		return 0;
	}

	auto it = _crime_prop_value_rate_tab.find(prop_tab);
	if (it == _crime_prop_value_rate_tab.end())
	{
		return 0;
	}
	const crime_prop_value_rate_tab_t& tab_info = it->second;
	if (tab_info.value_map.empty())
	{
		return 0;
	}
	int prop_quality = 0;
	for (auto it2 = tab_info.value_map.begin(); it2 != tab_info.value_map.end(); ++it2)
	{
		prop_quality ++;
		const std::tuple<int, int>& range = it2->second;
		int min = std::get<0>(range);
		int max = std::get<1>(range);
		if (prop_value >= min && prop_value <= max)
		{
			return prop_quality;
		}
	}
	return 0;
}

float seven_crime_config::GetMainSwordPropRate() const
{
	return _main_sword_prop_rate;
}

bool seven_crime_config::IsSwordValid(int sword_id) const
{
	return _sword_info.find(sword_id) != _sword_info.end();
}

void seven_crime_config::GetSwordSkill(int sword_id, int& skill_id, int& passive_skill_id) const
{
	auto it = _sword_info.find(sword_id);
	if (it == _sword_info.end())
	{
		return;
	}
	skill_id = it->second.skill_id;
	passive_skill_id = it->second.passive_skill_id;
	return;
}

int seven_crime_config::GetSwordEnhanceConfig(int sword_id) const
{
	auto it = _sword_info.find(sword_id);
	if (it == _sword_info.end())
	{
		return 0;
	}
	return it->second.enhance_config_id;
}

bool seven_crime_config::GetSwordInfo(int sword_id, sword_info_t& info) const
{
	auto it = _sword_info.find(sword_id);
	if (it == _sword_info.end())
	{
		return false;
	}
	info = it->second;
	return true;
}

int seven_crime_config::GetMinPlayerLevel() const
{
	return _min_player_level;
}

int seven_crime_config::GetUnidentifiedSpeakId() const
{
	return _unidentified_speak_id;
}

static void GetPropNeedItem(const std::map<int, std::vector<int>>& need_item_map, int crime_level, int& item_tid, int& item_num)
{
	auto it = need_item_map.find(crime_level);
	if (it == need_item_map.end())
	{
		return;
	}
	const std::vector<int>& a = it->second;
	if (a.size() != 2)
	{
		return;
	}
	item_tid = a[0];
	item_num = a[1];
}

void seven_crime_config::GetNewPropNeedItem(int crime_level, int& item_tid, int& item_num) const
{
	GetPropNeedItem(_new_prop_need_item, crime_level, item_tid, item_num);
}

void seven_crime_config::GetBetterPropNeedItem(int crime_level, int& item_tid, int& item_num) const
{
	GetPropNeedItem(_better_prop_need_item, crime_level, item_tid, item_num);
}

//////////////////////////////////////////////////////////////////////////////////

//在skill加载后调用，增加的技能才不会被清掉
void player_seven_crime::OnLoad(const PB::player_seven_crime_t& data)
{
	_data.CopyFrom(data);

	//剑的等级和主剑技能
	_main_sword_skill_id = 0;
	_main_sword_skill_level = 0;
	_main_sword_passive_skill_id = 0;
	_sword_level_map.clear();
	for (int i = 0; i < _data.seven_sword_size(); ++i)
	{
		int sword_id = _data.seven_sword(i).id();
		int sword_level = _data.seven_sword(i).level();
		if (sword_id > 0 && sword_level > 0)
		{
			_sword_level_map[sword_id] = sword_level;
			if (sword_id == _data.main_sword_id())
			{
				seven_crime_config::GetInstance().GetSwordSkill(sword_id, _main_sword_skill_id, _main_sword_passive_skill_id);
				_main_sword_skill_level = sword_level;

				ConditionSuppressCombatStat(_imp, SuppressCombatSystem::SEVEN_CRIME)
					.Then(nullptr, "player_seven_crime::OnLoad: suppress skill insert")
					.Else([&](gcreature_imp* imp) {
						object_interface oif(imp);
						if (_main_sword_skill_id > 0 && _main_sword_skill_level > 0)
						{
							imp->GetSkill().InsertSkill(_main_sword_skill_id, _main_sword_skill_level, oif);
						}
						if (_main_sword_passive_skill_id > 0 && _main_sword_skill_level > 0)
						{
							imp->GetSkill().PassiveSkillTakeEffect(oif, _main_sword_passive_skill_id, _main_sword_skill_level);
						}
					}, "player_seven_crime::OnLoad: normal skill insert");
			}
		}
	}

	//如果主剑等级是0，需要给玩家加1级的技能
	_TryAddBaseMainSwordSkill();

	//剑的属性
	_UpdateSwordProp();

	//献祭槽里的被动技能和属性
	_OnSacrificeChange();

	_UpdateVirtualFightingCapacity();

	if (_data.all_sword_fighting_capacity() == 0)
	{
		//这个字段是后加的，需要额外算一次，之后再升级和切换主剑的时候会自动计算。
		_data.set_all_sword_fighting_capacity(GetSwordFightingCapacity());
	}

	gplayer_imp *player = dynamic_cast<gplayer_imp *>(_imp);
	if (player)
	{
		int sword_level_count = 0;
		for (auto& a : _sword_level_map)
		{
			sword_level_count += a.second;
		}

		SLOG(FORMAT, "seven_crime_load_sword")
		.P("roleid", player->GetRoleID())
		.P("sword_level_count", sword_level_count)
		.P("sword_level_1", _RawGetSwordLevel(1))
		.P("sword_level_2", _RawGetSwordLevel(2))
		.P("sword_level_3", _RawGetSwordLevel(3))
		.P("sword_level_4", _RawGetSwordLevel(4))
		.P("sword_level_5", _RawGetSwordLevel(5))
		.P("sword_level_6", _RawGetSwordLevel(6))
		.P("sword_level_7", _RawGetSwordLevel(7));
	}
}

void player_seven_crime::MakeComplicatedData(PB::player_seven_crime_t& data)
{
	data.CopyFrom(_data);
}

void player_seven_crime::_TryAddBaseMainSwordSkill()
{
	gplayer_imp *player = dynamic_cast<gplayer_imp *>(_imp);
	if (!player || player->GetLevel() < seven_crime_config::GetInstance().GetMinPlayerLevel())
	{
		return;
	}
	if (_main_sword_skill_level > 0)
	{
		return;
	}

	int main_sword_id = _data.main_sword_id();
	if (seven_crime_config::GetInstance().IsSwordValid(main_sword_id))
	{
		int new_skill_id = 0;
		int new_passive_skill_id = 0;
		seven_crime_config::GetInstance().GetSwordSkill(main_sword_id, new_skill_id, new_passive_skill_id);
		int new_skill_level = 1;

		ConditionSuppressCombatStat(_imp, SuppressCombatSystem::SEVEN_CRIME)
			.Then(nullptr, "player_seven_crime::_TryAddBaseMainSwordSkill: suppress skill remove/insert")
			.Else([&](gcreature_imp* imp) {
				object_interface oif(imp);
				if (_main_sword_skill_id > 0 && _main_sword_skill_level > 0)
				{
					imp->GetSkill().RemoveSkill(_main_sword_skill_id, _main_sword_skill_level, oif);
				}
				if (new_skill_id > 0 && new_skill_level > 0)
				{
					imp->GetSkill().InsertSkill(new_skill_id, new_skill_level, oif);
				}

				if (_main_sword_passive_skill_id > 0 && _main_sword_skill_level > 0)
				{
					imp->GetSkill().PassiveSkillUndoEffect(oif, _main_sword_passive_skill_id, _main_sword_skill_level);
				}
				if (new_passive_skill_id > 0 && new_skill_level > 0)
				{
					imp->GetSkill().PassiveSkillTakeEffect(oif, new_passive_skill_id, new_skill_level);
				}
			}, "player_seven_crime::_TryAddBaseMainSwordSkill: normal skill remove/insert");

		_main_sword_skill_id = new_skill_id;
		_main_sword_skill_level = new_skill_level;
		_main_sword_passive_skill_id = new_passive_skill_id;
	}
}

void player_seven_crime::_OnMainSwordChange()
{
	int new_skill_id = 0;
	int new_passive_skill_id = 0;
	seven_crime_config::GetInstance().GetSwordSkill(_data.main_sword_id(), new_skill_id, new_passive_skill_id);
	int new_skill_level = _RawGetSwordLevel(_data.main_sword_id());
	if (new_skill_id == _main_sword_skill_id && new_skill_level == _main_sword_skill_level)
	{
		return;
	}

	ConditionSuppressCombatStat(_imp, SuppressCombatSystem::SEVEN_CRIME)
		.Then(nullptr, "player_seven_crime::_OnMainSwordChange: suppress skill remove/insert")
		.Else([&](gcreature_imp* imp) {
			object_interface oif(imp);
			if (_main_sword_skill_id > 0 && _main_sword_skill_level > 0)
			{
				imp->GetSkill().RemoveSkill(_main_sword_skill_id, _main_sword_skill_level, oif);
			}
			if (new_skill_id > 0 && new_skill_level > 0)
			{
				imp->GetSkill().InsertSkill(new_skill_id, new_skill_level, oif);
			}
			if (_main_sword_passive_skill_id > 0 && _main_sword_skill_level > 0)
			{
				imp->GetSkill().PassiveSkillUndoEffect(oif, _main_sword_passive_skill_id, _main_sword_skill_level);
			}
			if (new_passive_skill_id > 0 && new_skill_level > 0)
			{
				imp->GetSkill().PassiveSkillTakeEffect(oif, new_passive_skill_id, new_skill_level);
			}
		}, "player_seven_crime::_OnMainSwordChange: normal skill remove/insert");

	_main_sword_skill_id = new_skill_id;
	_main_sword_skill_level = new_skill_level;
	_main_sword_passive_skill_id = new_passive_skill_id;

	//如果主剑等级是0，需要给玩家加1级的技能
	_TryAddBaseMainSwordSkill();

	_UpdateSwordProp();
	_UpdateVirtualFightingCapacity();
	_data.set_all_sword_fighting_capacity(GetSwordFightingCapacity());
}

bool player_seven_crime::_SwordUpgrade(int sword_id, int old_level, int& fighting_capacity)
{
	PB::player_seven_crime_t::sword_t *p_sword_data = NULL;
	for (int i = 0; i < _data.seven_sword_size(); ++i)
	{
		if (_data.seven_sword(i).id() == sword_id)
		{
			if (_data.seven_sword(i).level() != old_level)
			{
				return false;
			}
			p_sword_data = _data.mutable_seven_sword(i);
			break;
		}
	}
	if (p_sword_data == NULL && old_level == 0)
	{
		p_sword_data = _data.add_seven_sword();
	}
	if (p_sword_data == NULL)
	{
		return false;
	}
	p_sword_data->set_id(sword_id);
	int new_level = old_level + 1;
	p_sword_data->set_level(new_level);
	fighting_capacity = _CalcSwordFightingCapacity(sword_id, new_level, false);
	p_sword_data->set_fighting_capacity(fighting_capacity);

	_sword_level_map[sword_id] = new_level;

	object_interface oif(_imp);
	if (_data.main_sword_id() == sword_id)
	{
		int skill_id = 0;
		int passive_skill_id = 0;
		seven_crime_config::GetInstance().GetSwordSkill(sword_id, skill_id, passive_skill_id);
		ConditionSuppressCombatStat(_imp, SuppressCombatSystem::SEVEN_CRIME)
			.Then(nullptr, "player_seven_crime::_SwordUpgrade: suppress skill remove/insert")
			.Else([&](gcreature_imp* imp) {
				if (_main_sword_skill_id > 0 && _main_sword_skill_level > 0)
				{
					imp->GetSkill().RemoveSkill(_main_sword_skill_id, _main_sword_skill_level, oif);
				}
				if (skill_id > 0 && new_level > 0)
				{
					imp->GetSkill().InsertSkill(skill_id, new_level, oif);
				}
				if (_main_sword_passive_skill_id > 0 && _main_sword_skill_level > 0)
				{
					imp->GetSkill().PassiveSkillUndoEffect(oif, _main_sword_passive_skill_id, _main_sword_skill_level);
				}
				if (passive_skill_id > 0 && new_level > 0)
				{
					imp->GetSkill().PassiveSkillTakeEffect(oif, passive_skill_id, new_level);
				}
			}, "player_seven_crime::_SwordUpgrade: normal skill remove/insert");
		_main_sword_skill_id = skill_id;
		_main_sword_skill_level = new_level;
		_main_sword_passive_skill_id = passive_skill_id;
	}

	_UpdateSwordProp();
	_UpdateVirtualFightingCapacity();
	_data.set_all_sword_fighting_capacity(GetSwordFightingCapacity());
	ConditionSuppressCombatStat(_imp, SuppressCombatSystem::SEVEN_CRIME)
		.Then(nullptr, "player_seven_crime::_SwordUpgrade: suppress skill reset")
		.Else([&](gcreature_imp* imp) {
			imp->GetSkill().EventReset(oif, false, GetSevenCrimeFunctionalLabelMask());//会影响被动技能，需要刷新
		}, "player_seven_crime::_SwordUpgrade: normal skill reset");
	return true;
}

void player_seven_crime::_UpdateSwordProp()
{
	creature_enhance_if cef(_imp);
	ConditionSuppressCombatStat(_imp, SuppressCombatSystem::SEVEN_CRIME)
		.Then(nullptr, "player_seven_crime::_UpdateSwordProp: suppress prop dec")
		.Else([&](gcreature_imp* imp) {
			cef.GetProperty().DecByStruct(_sword_prop);
		}, "player_seven_crime::_UpdateSwordProp: normal prop dec");
	memset(&_sword_prop, 0, sizeof(_sword_prop));
	for (auto a : _sword_level_map)
	{
		int sword_id = a.first;
		int sword_level = a.second;
		if (sword_id <= 0 || sword_level <= 0)
		{
			continue;
		}
		seven_crime_config::sword_info_t sword_info;
		if (!seven_crime_config::GetInstance().GetSwordInfo(sword_id, sword_info))
		{
			continue;
		}
		auto p_enhance_config = ENHANCE_CONFIG::get(sword_info.enhance_config_id);
		if (!p_enhance_config || sword_level >= p_enhance_config->configs.size())
		{
			continue;
		}
		float ratio = p_enhance_config->configs[sword_level].basic_prop_addition_percent * 0.01f;
		if (sword_info.skill_id == _main_sword_skill_id)
		{
			ratio *= seven_crime_config::GetInstance().GetMainSwordPropRate();
		}
		int attack_type = _imp->GetProperty().GetGProperty()->PROP_NAME_GET(atkType);
		if (attack_type == 1)//物理
		{
			PlayerBasePropEnhance(EBRT_SEVEN_CRIME_SWORD, sword_info.phy_enhance_prop_id, &_sword_prop, ratio);
		}
		else if (attack_type == 2)//魔法
		{
			PlayerBasePropEnhance(EBRT_SEVEN_CRIME_SWORD, sword_info.mag_enhance_prop_id, &_sword_prop, ratio);
		}
	}
	ConditionSuppressCombatStat(_imp, SuppressCombatSystem::SEVEN_CRIME)
		.Then(nullptr, "player_seven_crime::_UpdateSwordProp: suppress prop inc")
		.Else([&](gcreature_imp* imp) {
			cef.GetProperty().IncByStruct(_sword_prop);
		}, "player_seven_crime::_UpdateSwordProp: normal prop inc");
}

int player_seven_crime::_CalcSwordFightingCapacity(int sword_id, int sword_level, bool check_main_sword) const
{
	seven_crime_config::sword_info_t sword_info;
	if (!seven_crime_config::GetInstance().GetSwordInfo(sword_id, sword_info))
	{
		return 0;
	}
	auto p_enhance_config = ENHANCE_CONFIG::get(sword_info.enhance_config_id);
	if (!p_enhance_config || sword_level >= p_enhance_config->configs.size())
	{
		return 0;
	}
	float ratio = p_enhance_config->configs[sword_level].basic_prop_addition_percent * 0.01f;
	if (check_main_sword && sword_info.skill_id == _main_sword_skill_id)
	{
		ratio *= seven_crime_config::GetInstance().GetMainSwordPropRate();
	}

	float fighting_capacity = 0;
	property_template::data_EnhanceBaseProp enhance_prop;
	memset(&enhance_prop, 0, sizeof(enhance_prop));
	int attack_type = _imp->GetProperty().GetGProperty()->PROP_NAME_GET(atkType);
	if (attack_type == 1)//物理
	{
		PlayerBasePropEnhance(EBRT_SEVEN_CRIME_SWORD, sword_info.phy_enhance_prop_id, &enhance_prop, ratio);
		fighting_capacity += enhance_prop.pointPhyAtk * 0.6;
	}
	else if (attack_type == 2)//魔法
	{
		PlayerBasePropEnhance(EBRT_SEVEN_CRIME_SWORD, sword_info.mag_enhance_prop_id, &enhance_prop, ratio);
		fighting_capacity += enhance_prop.pointMagAtk * 0.6;
	}
	fighting_capacity += (enhance_prop.pointSevenCrimeAdd + enhance_prop.pointSevenCrimeRedu) * 0.6;
	fighting_capacity += enhance_prop.pointHP * 0.005;
	fighting_capacity += (enhance_prop.pointPhyDef + enhance_prop.pointMagDef) * 0.4;
	fighting_capacity += (enhance_prop.pointCritLevel + enhance_prop.pointPsychokinesisLevel + enhance_prop.pointPierceLevel + enhance_prop.pointCritResLevel + enhance_prop.pointCDReduLevel) * 0.2;
	return fighting_capacity + seven_crime_config::GetInstance().GetSowrdFightingCapacity(sword_level);
}

int player_seven_crime::GetSwordFightingCapacity() const
{
	int f = 0;
	for (int i = 0; i < _data.seven_sword_size(); ++i)
	{
		if (_data.seven_sword(i).id() == _data.main_sword_id())
		{
			f += _CalcSwordFightingCapacity(_data.seven_sword(i).id(), _data.seven_sword(i).level(), true);
		}
		else
		{
			f += _data.seven_sword(i).fighting_capacity();
		}
	}
	return f;
}

int player_seven_crime::GetSacrificeFightingCapacity() const
{
	int f = 0;
	for (int i = 0; i < _data.sacrifice_slot_size(); ++i)
	{
		for (int j = 0; j < _data.sacrifice_slot(i).equip_crime_size(); ++j)
		{
			const PB::equip_seven_crime_t& c = _data.sacrifice_slot(i).equip_crime(j);
			f += c.fighting_capacity() + c.prop().fighting_capacity();
		}
	}
	return f;
}

//更新七宗罪系统的系列战斗力
void player_seven_crime::_UpdateVirtualFightingCapacity() const
{
	int f = 0;
	//剑的等级对应的虚拟战斗力
	for (int i = 0; i < _data.seven_sword_size(); ++i)
	{
		if (_data.seven_sword(i).id() == _data.main_sword_id())
		{
			f += seven_crime_config::GetInstance().GetSowrdFightingCapacity(_data.seven_sword(i).level()) * seven_crime_config::GetInstance().GetMainSwordPropRate();
		}
		else
		{
			f += seven_crime_config::GetInstance().GetSowrdFightingCapacity(_data.seven_sword(i).level());
		}
	}

	//献祭的虚拟战斗力
	for (int i = 0; i < _data.sacrifice_slot_size(); ++i)
	{
		for (int j = 0; j < _data.sacrifice_slot(i).equip_crime_size(); ++j)
		{
			const PB::equip_seven_crime_t& c = _data.sacrifice_slot(i).equip_crime(j);
			f += c.fighting_capacity() + c.prop().special_fighting_capacity();
		}
	}

	_imp->GetProperty().SetByIndex(property_template::INDEX_sevenCrimeFightingCapacity, f, f, f, f);
}

void player_seven_crime::_OnSacrificeChange()
{
	object_interface oif(_imp);
	//罪责类型
	ConditionSuppressCombatStat(_imp, SuppressCombatSystem::SEVEN_CRIME)
		.Then(nullptr, "player_seven_crime::_OnSacrificeChange: suppress passive skill undo effect")
		.Else([&](gcreature_imp* imp) {
			for (int a : _sacrifice_skill_arr)
			{
				imp->GetSkill().PassiveSkillUndoEffect(oif, a, 1);
			}
		}, "player_seven_crime::_OnSacrificeChange: normal passive skill undo effect");
	_sacrifice_skill_arr.clear();
	_crime_type_num_map.clear();
	_crime_type_power_map.clear();

	for (int i = 0; i < _data.sacrifice_slot_size(); ++i)
	{
		for (int j = 0; j < _data.sacrifice_slot(i).equip_crime_size(); ++j)
		{
			const PB::equip_seven_crime_t& equip_crime = _data.sacrifice_slot(i).equip_crime(j);
			_crime_type_num_map[equip_crime.type()] += 1;

			int skill_id = 0;
			int power = 0;
			bool succ = seven_crime_config::GetInstance().GetCrimeSkillAndPower(equip_crime.type(), equip_crime.quality(), equip_crime.level(), skill_id, power);
			if (!succ)
			{
				continue;
			}
			_crime_type_power_map[equip_crime.type()] += power;
			_sacrifice_skill_arr.emplace_back(skill_id);
		}
	}

	ConditionSuppressCombatStat(_imp, SuppressCombatSystem::SEVEN_CRIME)
		.Then(nullptr, "player_seven_crime::_OnSacrificeChange: suppress passive skill take effect")
		.Else([&](gcreature_imp* imp) {
			for (int a : _sacrifice_skill_arr)
			{
				imp->GetSkill().PassiveSkillTakeEffect(oif, a, 1);
			}
		}, "player_seven_crime::_OnSacrificeChange: normal passive skill take effect");

	//罪责属性
	ConditionSuppressCombatStat(_imp, SuppressCombatSystem::SEVEN_CRIME)
		.Then(nullptr, "player_seven_crime::_OnSacrificeChange: suppress prop modify")
		.Else([&](gcreature_imp* imp) {
			for (auto& a : _sacrifice_prop)
			{
				imp->GetProperty().ModifyByIndex(a.first, -a.second, -a.second, -a.second, -a.second);
			}
		}, "player_seven_crime::_OnSacrificeChange: normal prop modify");
	_sacrifice_prop.clear();

	_sacrifice_add_skill_crit = 0;
	_sacrifice_add_skill_crit_redu = 0;
	_sacrifice_add_skill_cd_rate = 0;
	_sacrifice_add_skill_dam = 0;
	_sacrifice_add_buff_skill_crit = 0;
	_sacrifice_add_buff_skill_crit_redu = 0;
	_sacrifice_add_buff_skill_dam = 0;
	for (int i = 0; i < _data.sacrifice_slot_size(); ++i)
	{
		for (int j = 0; j < _data.sacrifice_slot(i).equip_crime_size(); ++j)
		{
			const PB::equip_seven_crime_t& equip_crime = _data.sacrifice_slot(i).equip_crime(j);
			int level = equip_crime.level();
			for (int k = 0; k < equip_crime.prop().prop_arr_size(); ++k)
			{
				_AddCrimeProp(level, equip_crime.prop().prop_arr(k), _sacrifice_prop);
			}
		}
	}
	ConditionSuppressCombatStat(_imp, SuppressCombatSystem::SEVEN_CRIME)
		.Then(nullptr, "player_seven_crime::_OnSacrificeChange: suppress prop modify")
		.Else([&](gcreature_imp* imp) {
			for (auto& a : _sacrifice_prop)
			{
				imp->GetProperty().ModifyByIndex(a.first, a.second, a.second, a.second, a.second);
			}
		}, "player_seven_crime::_OnSacrificeChange: normal prop modify");
}

void player_seven_crime::_AddCrimeProp(int level, const PB::equip_seven_crime_t::crime_prop_t& crime_prop, std::map<int, int>& sacrifice_prop)
{
	switch (crime_prop.type())
	{
	case 1://角色属性，prop_id=addon.txt里的id
	{
		int addon_id = crime_prop.id();
		auto *handler = addon_manager::GetInstance().QueryHandler(addon_id);
		if (handler == NULL)
		{
			return;
		}
		int prop_index = handler->GetPropertyIndex();
		if (prop_index <= 0)
		{
			return;
		}
		sacrifice_prop[prop_index] += crime_prop.value();
		return;
	}
	case 2://根据职业动态生效的属性
	{
		if (crime_prop.id() == 1)//物理攻击/魔法攻击
		{
			int attack_type = _imp->GetProperty().GetGProperty()->PROP_NAME_GET(atkType);
			if (attack_type == 1)//物理
			{
				sacrifice_prop[property_template::INDEX_pointPhyAtk] += crime_prop.value();
			}
			else if (attack_type == 2)//魔法
			{
				sacrifice_prop[property_template::INDEX_pointMagAtk] += crime_prop.value();
			}
		}
		else if (crime_prop.id() == 2)//主属性，力量/智力
		{
			int attack_type = _imp->GetProperty().GetGProperty()->PROP_NAME_GET(atkType);
			if (attack_type == 1)//物理
			{
				sacrifice_prop[property_template::INDEX_pointBaseProperty2] += crime_prop.value();//力量
			}
			else if (attack_type == 2)//魔法
			{
				sacrifice_prop[property_template::INDEX_pointBaseProperty3] += crime_prop.value();//智力
			}
		}
		return;
	}
	case 3://特殊属性
	{
		switch (crime_prop.id())
		{
		case 1:
			_sacrifice_add_skill_crit += seven_crime_config::GetInstance().GetCrimeSpecialPropValue(1, level);
			break;
		case 2:
			_sacrifice_add_buff_skill_crit += seven_crime_config::GetInstance().GetCrimeSpecialPropValue(2, level);
			break;
		case 3:
			_sacrifice_add_skill_crit_redu += seven_crime_config::GetInstance().GetCrimeSpecialPropValue(3, level);
			break;
		case 4:
			_sacrifice_add_buff_skill_crit_redu += seven_crime_config::GetInstance().GetCrimeSpecialPropValue(4, level);
			break;
		case 5:
			_sacrifice_add_skill_cd_rate += seven_crime_config::GetInstance().GetCrimeSpecialPropValue(5, level);
			break;
		case 6:
			_sacrifice_add_skill_dam += seven_crime_config::GetInstance().GetCrimeSpecialPropValue(6, level);
			break;
		case 7:
			_sacrifice_add_buff_skill_dam += seven_crime_config::GetInstance().GetCrimeSpecialPropValue(7, level);
			break;
		}
	}
	}
}

int player_seven_crime::GetSacrificeAddSkillCrit() const
{
	if (!GNET::FuncSwitchManager::Instance().getFuncSwitch(kFuncCodeSevenCrimeSacrifice))
	{
		return 0;
	}
	return _sacrifice_add_skill_crit;
}

int player_seven_crime::GetSacrificeAddSkillCritRedu() const
{
	if (!GNET::FuncSwitchManager::Instance().getFuncSwitch(kFuncCodeSevenCrimeSacrifice))
	{
		return 0;
	}
	return _sacrifice_add_skill_crit_redu;
}

void player_seven_crime::TryCostSkillCD(int& cooltime) const
{
	if (!GNET::FuncSwitchManager::Instance().getFuncSwitch(kFuncCodeSevenCrimeSacrifice))
	{
		return;
	}
	if (_sacrifice_add_skill_cd_rate <= 0)
	{
		return;
	}
	if (abase::Rand(1, 1000) <= _sacrifice_add_skill_cd_rate)
	{
		cooltime *= 0.5f;
	}
}

void player_seven_crime::IdipSetLevel(gplayer_imp *player, int sword_id, int new_level)
{
	if (player->GetWorldImp()->IsFair() || player->IsPubg())
	{
		return;
	}
	if (player->GetLevel() < seven_crime_config::GetInstance().GetMinPlayerLevel())
	{
		return;
	}
	CHECK_FUNC_SWITCH_AND_RETURN(kFuncCodeSevenCrime, player);
	CHECK_FUNC_SWITCH_AND_RETURN(kFuncCodeSevenCrimeOpt, player);
	if (player->IsRoamIn() && !GLOBAL_CONFIG.IsNormalCenterZone(GNET::g_zoneid))
	{
		return;
	}

	int enhance_config_id = seven_crime_config::GetInstance().GetSwordEnhanceConfig(sword_id);
	if (enhance_config_id <= 0)
	{
		return;
	}
	auto p_enhance_config = ENHANCE_CONFIG::get(enhance_config_id);
	if (!p_enhance_config)
	{
		return;
	}
	int old_level = _RawGetSwordLevel(sword_id);
	if (new_level == old_level)
	{
		return;
	}
	if (new_level < 0 || new_level >= p_enhance_config->configs.size())
	{
		return;
	}
	int old_player_fighting_capacity = player->GetFightingCapactiy();

	PB::player_seven_crime_t::sword_t *p_sword_data = NULL;
	for (int i = 0; i < _data.seven_sword_size(); ++i)
	{
		if (_data.seven_sword(i).id() == sword_id)
		{
			if (_data.seven_sword(i).level() != old_level)
			{
				return;
			}
			p_sword_data = _data.mutable_seven_sword(i);
			break;
		}
	}
	if (p_sword_data == NULL && old_level == 0)
	{
		p_sword_data = _data.add_seven_sword();
	}
	if (p_sword_data == NULL)
	{
		return;
	}

	p_sword_data->set_id(sword_id);
	p_sword_data->set_level(new_level);
	int sword_fighting_capacity = _CalcSwordFightingCapacity(sword_id, new_level, false);
	p_sword_data->set_fighting_capacity(sword_fighting_capacity);
	_sword_level_map[sword_id] = new_level;

	object_interface oif(_imp);
	if (_data.main_sword_id() == sword_id)
	{
		int skill_id = 0;
		int passive_skill_id = 0;
		seven_crime_config::GetInstance().GetSwordSkill(sword_id, skill_id, passive_skill_id);
		ConditionSuppressCombatStat(_imp, SuppressCombatSystem::SEVEN_CRIME)
			.Then(nullptr, "player_seven_crime::IdipSetLevel: suppress skill remove/insert")
			.Else([&](gcreature_imp* imp) {
				if (_main_sword_skill_id > 0 && _main_sword_skill_level > 0)
				{
					imp->GetSkill().RemoveSkill(_main_sword_skill_id, _main_sword_skill_level, oif);
				}
				if (skill_id > 0 && new_level > 0)
				{
					imp->GetSkill().InsertSkill(skill_id, new_level, oif);
				}

				if (_main_sword_passive_skill_id > 0 && _main_sword_skill_level > 0)
				{
					imp->GetSkill().PassiveSkillUndoEffect(oif, _main_sword_passive_skill_id, _main_sword_skill_level);
				}
				if (passive_skill_id > 0 && new_level > 0)
				{
					imp->GetSkill().PassiveSkillTakeEffect(oif, passive_skill_id, new_level);
				}
			}, "player_seven_crime::IdipSetLevel: normal skill remove/insert");
		_main_sword_skill_id = skill_id;
		_main_sword_skill_level = new_level;
		_main_sword_passive_skill_id = passive_skill_id;
	}

	_UpdateSwordProp();
	_UpdateVirtualFightingCapacity();
	_data.set_all_sword_fighting_capacity(GetSwordFightingCapacity());
	ConditionSuppressCombatStat(_imp, SuppressCombatSystem::SEVEN_CRIME)
		.Then(nullptr, "player_seven_crime::IdipSetLevel: suppress skill reset")
		.Else([&](gcreature_imp* imp) {
			imp->GetSkill().EventReset(oif, false, GetSevenCrimeFunctionalLabelMask());//会影响被动技能，需要刷新
		}, "player_seven_crime::IdipSetLevel: normal skill reset");

	PB::gp_seven_crime_re m;
	m.set_ret(S2C::ERR_SUCCESS);
	m.set_opt(PB::SCO_UPGRADE_SWORD);
	m.set_sword_id(sword_id);
	m.set_sword_level(new_level);
	m.set_sword_fight_capacity(sword_fighting_capacity);
	m.set_all_sword_fight_capacity(_data.all_sword_fighting_capacity());
	player->Runner()->QuietSend<S2C::CMD::PBS2C>(m);

	player->GetAchievement().OnSevenCrimeSwordLevelChange(player);

	SLOG(FORMAT, "IdipSetLevel")
	.P("roleid", player->GetRoleID())
	.P("is_main", sword_id == _data.main_sword_id())
	.P("sword_id", sword_id)
	.P("old_level", old_level)
	.P("new_level", new_level)
	.P("old_fighting_capacity", old_player_fighting_capacity)
	.P("new_fighting_capacity", player->GetFightingCapactiy());
}

int player_seven_crime::GetSacrificeAddSkillDam() const
{
	if (!GNET::FuncSwitchManager::Instance().getFuncSwitch(kFuncCodeSevenCrimeSacrifice))
	{
		return 0;
	}
	return _sacrifice_add_skill_dam;
}

int player_seven_crime::GetSacrificeAddBuffSkillCrit() const
{
	if (!GNET::FuncSwitchManager::Instance().getFuncSwitch(kFuncCodeSevenCrimeSacrifice))
	{
		return 0;
	}
	return _sacrifice_add_buff_skill_crit;
}

int player_seven_crime::GetSacrificeAddBuffSkillCritRedu() const
{
	if (!GNET::FuncSwitchManager::Instance().getFuncSwitch(kFuncCodeSevenCrimeSacrifice))
	{
		return 0;
	}
	return _sacrifice_add_buff_skill_crit_redu;
}

int player_seven_crime::GetSacrificeAddBuffSkillDam() const
{
	if (!GNET::FuncSwitchManager::Instance().getFuncSwitch(kFuncCodeSevenCrimeSacrifice))
	{
		return 0;
	}
	return _sacrifice_add_buff_skill_dam;
}

int player_seven_crime::GetCrimePower(int crime_type) const
{
	if (!GNET::FuncSwitchManager::Instance().getFuncSwitch(kFuncCodeSevenCrime))
	{
		return 0;
	}
	if (!GNET::FuncSwitchManager::Instance().getFuncSwitch(kFuncCodeSevenCrimeSacrifice))
	{
		return 0;
	}
	auto it = _crime_type_power_map.find(crime_type);
	if (it == _crime_type_power_map.end())
	{
		return 0;
	}
	return it->second;
}

int player_seven_crime::GetCrimeNum(int crime_type) const
{
	if (!GNET::FuncSwitchManager::Instance().getFuncSwitch(kFuncCodeSevenCrime))
	{
		return 0;
	}
	if (!GNET::FuncSwitchManager::Instance().getFuncSwitch(kFuncCodeSevenCrimeSacrifice))
	{
		return 0;
	}
	auto it = _crime_type_num_map.find(crime_type);
	if (it == _crime_type_num_map.end())
	{
		return 0;
	}
	return it->second;
}

void player_seven_crime::ClearSkillAndProp()
{
	object_interface oif(_imp);
	ConditionSuppressCombatStat(_imp, SuppressCombatSystem::SEVEN_CRIME)
		.Then(nullptr, "player_seven_crime::ClearSkillAndProp: suppress skill remove")
		.Else([&](gcreature_imp* imp) {
			if (_main_sword_skill_id > 0 && _main_sword_skill_level > 0)
			{
				imp->GetSkill().RemoveSkill(_main_sword_skill_id, _main_sword_skill_level, oif);
			}
			if (_main_sword_passive_skill_id > 0 && _main_sword_skill_level > 0)
			{
				imp->GetSkill().PassiveSkillUndoEffect(oif, _main_sword_passive_skill_id, _main_sword_skill_level);
			}
		}, "player_seven_crime::ClearSkillAndProp: normal skill remove");
	_main_sword_skill_id = 0;
	_main_sword_skill_level = 0;
	_main_sword_passive_skill_id = 0;

	_sword_level_map.clear();

	creature_enhance_if cef(_imp);
	ConditionSuppressCombatStat(_imp, SuppressCombatSystem::SEVEN_CRIME)
		.Then(nullptr, "player_seven_crime::ClearSkillAndProp: suppress prop dec")
		.Else([&](gcreature_imp* imp) {
			cef.GetProperty().DecByStruct(_sword_prop);
		}, "player_seven_crime::ClearSkillAndProp: normal prop dec");
	memset(&_sword_prop, 0, sizeof(_sword_prop));

	ConditionSuppressCombatStat(_imp, SuppressCombatSystem::SEVEN_CRIME)
		.Then(nullptr, "player_seven_crime::ClearSkillAndProp: suppress passive skill undo effect")
		.Else([&](gcreature_imp* imp) {
			for (int a : _sacrifice_skill_arr)
			{
				imp->GetSkill().PassiveSkillUndoEffect(oif, a, 1);
			}
		}, "player_seven_crime::ClearSkillAndProp: normal passive skill undo effect");
	_sacrifice_skill_arr.clear();
	_crime_type_num_map.clear();

	ConditionSuppressCombatStat(_imp, SuppressCombatSystem::SEVEN_CRIME)
		.Then(nullptr, "player_seven_crime::ClearSkillAndProp: suppress prop modify")
		.Else([&](gcreature_imp* imp) {
			for (auto& a : _sacrifice_prop)
			{
				imp->GetProperty().ModifyByIndex(a.first, -a.second, -a.second, -a.second, -a.second);
			}
		}, "player_seven_crime::ClearSkillAndProp: normal prop modify");
	_sacrifice_prop.clear();
}

void player_seven_crime::Save(PB::player_seven_crime_t *data) const
{
	data->CopyFrom(_data);
}

void player_seven_crime::OnLogin(bool islogin)
{
	//如果是切地图(不是登陆游戏)，就不用再推数据了
	if (!islogin)
	{
		return;
	}

	gplayer_imp *player = dynamic_cast<gplayer_imp *>(_imp);
	if (!player)
	{
		return;
	}

	PB::gp_seven_crime_re m;
	m.set_ret(S2C::ERR_SUCCESS);
	m.set_opt(PB::SCO_INFO);
	m.mutable_info()->CopyFrom(_data);
	m.set_all_sword_fight_capacity(_data.all_sword_fighting_capacity());
	player->Runner()->QuietSend<S2C::CMD::PBS2C>(m);
}

void player_seven_crime::CmdOpt(const PB::gp_seven_crime& cmd)
{
	gplayer_imp *player = dynamic_cast<gplayer_imp *>(_imp);
	if (!player)
	{
		return;
	}
	if (player->GetWorldImp()->IsFair() || player->IsPubg())
	{
		return;
	}
	if (player->GetLevel() < seven_crime_config::GetInstance().GetMinPlayerLevel())
	{
		return;
	}
	CHECK_FUNC_SWITCH_AND_RETURN(kFuncCodeSevenCrime, player);
	CHECK_FUNC_SWITCH_AND_RETURN(kFuncCodeSevenCrimeOpt, player);

	//本服和跨服都可以做的操作
	switch (cmd.opt())
	{
	case PB::SCO_SELECT_SWORD:
	{
		_CmdSelectSword(player, cmd.sword_id());
		break;
	}
	default:
		break;
	}

	if (player->IsRoamIn() && !GLOBAL_CONFIG.IsNormalCenterZone(GNET::g_zoneid))
	{
		return;
	}

	//只在本服可以做的操作
	switch (cmd.opt())
	{
	case PB::SCO_SACRIFICE:
	{
		CHECK_FUNC_SWITCH_AND_RETURN(kFuncCodeSevenCrimeSacrifice, player);
		_CmdSacrifice(player, cmd.equipment_index(), cmd.equipment_tid(), cmd.sacrifice_slot_idx(), cmd.sacrifice_crime_idx());
		break;
	}
	case PB::SCO_NEW_EQUIPMENT_PROP:
	{
		CHECK_FUNC_SWITCH_AND_RETURN(kFuncCodeSevenCrimeSacrifice, player);
		_CmdNewEquipmentProp(player, cmd.equipment_index(), cmd.equipment_tid(), false);
		break;
	}
	case PB::SCO_BETTER_EQUIPMENT_PROP:
	{
		CHECK_FUNC_SWITCH_AND_RETURN(kFuncCodeSevenCrimeSacrifice, player);
		_CmdNewEquipmentProp(player, cmd.equipment_index(), cmd.equipment_tid(), true);
		break;
	}
	case PB::SCO_USE_EQUIPMENT_PROP:
	{
		CHECK_FUNC_SWITCH_AND_RETURN(kFuncCodeSevenCrimeSacrifice, player);
		_CmdUseEquipmentProp(player, cmd.equipment_index(), cmd.equipment_tid());
		break;
	}
	case PB::SCO_DROP_EQUIPMENT_PROP:
	{
		CHECK_FUNC_SWITCH_AND_RETURN(kFuncCodeSevenCrimeSacrifice, player);
		_CmdDropEquipmentProp(player, cmd.equipment_index(), cmd.equipment_tid());
		break;
	}
	case PB::SCO_NEW_SACRIFICE_PROP:
	{
		CHECK_FUNC_SWITCH_AND_RETURN(kFuncCodeSevenCrimeSacrifice, player);
		_CmdNewSacrificeProp(player, cmd.sacrifice_slot_idx(), cmd.sacrifice_crime_idx(), false);
		break;
	}
	case PB::SCO_BETTER_SACRIFICE_PROP:
	{
		CHECK_FUNC_SWITCH_AND_RETURN(kFuncCodeSevenCrimeSacrifice, player);
		_CmdNewSacrificeProp(player, cmd.sacrifice_slot_idx(), cmd.sacrifice_crime_idx(), true);
		break;
	}
	case PB::SCO_USE_SACRIFICE_PROP:
	{
		CHECK_FUNC_SWITCH_AND_RETURN(kFuncCodeSevenCrimeSacrifice, player);
		_CmdUseSacrificeProp(player, cmd.sacrifice_slot_idx(), cmd.sacrifice_crime_idx());
		break;
	}
	case PB::SCO_DROP_SACRIFICE_PROP:
	{
		CHECK_FUNC_SWITCH_AND_RETURN(kFuncCodeSevenCrimeSacrifice, player);
		_CmdDropSacrificeProp(player, cmd.sacrifice_slot_idx(), cmd.sacrifice_crime_idx());
		break;
	}
	case PB::SCO_UPGRADE_SWORD:
	{
		_CmdUpgradeSword(player, cmd.sword_id());
		break;
	}
	case PB::SCO_ADVANCE_UPGRADE_SWORD:
	{
		_CmdAdvanceUpgradeSword(player, cmd.sword_id(), cmd.material());
		break;
	}
	default:
		break;
	}
}

void player_seven_crime::_CmdSelectSword(gplayer_imp *player, int sword_id)
{
	if (player->IsCombatState())
	{
		return;
	}
	if (!seven_crime_config::GetInstance().IsSwordValid(sword_id))
	{
		return;//剑不存在
	}

	if (sword_id != _data.main_sword_id())
	{
		int old_sword_id = _data.main_sword_id();
		int old_sword_level = _RawGetSwordLevel(old_sword_id);
		int old_sword_skill_id = _main_sword_skill_id;
		int old_sword_passive_skill_id = _main_sword_passive_skill_id;
		int old_sword_skill_level = _main_sword_skill_level;
		int old_fighting_capacity = player->GetFightingCapactiy();

		_data.set_main_sword_id(sword_id);
		_OnMainSwordChange();

		SLOG(FORMAT, "seven_crime_select_sword")
		.P("roleid", player->GetRoleID())
		.P("old_sword_id", old_sword_id)
		.P("old_sword_level", old_sword_level)
		.P("old_sword_skill_id", old_sword_skill_id)
		.P("old_sword_passive_skill_id", old_sword_passive_skill_id)
		.P("old_sword_skill_level", old_sword_skill_level)
		.P("old_fighting_capacity", old_fighting_capacity)
		.P("new_sword_id", _data.main_sword_id())
		.P("new_sword_level", _RawGetSwordLevel(_data.main_sword_id()))
		.P("new_sword_skill_id", _main_sword_skill_id)
		.P("new_sword_passive_skill_id", _main_sword_passive_skill_id)
		.P("new_sword_skill_level", _main_sword_skill_level)
		.P("new_fighting_capacity", player->GetFightingCapactiy());
	}

	PB::gp_seven_crime_re m;
	m.set_ret(S2C::ERR_SUCCESS);
	m.set_opt(PB::SCO_SELECT_SWORD);
	m.set_sword_id(sword_id);
	m.set_all_sword_fight_capacity(_data.all_sword_fighting_capacity());
	player->Runner()->QuietSend<S2C::CMD::PBS2C>(m);
}

void player_seven_crime::_CmdUpgradeSword(gplayer_imp *player, int sword_id)
{
	int enhance_config_id = seven_crime_config::GetInstance().GetSwordEnhanceConfig(sword_id);
	if (enhance_config_id <= 0)
	{
		return;
	}
	auto p_enhance_config = ENHANCE_CONFIG::get(enhance_config_id);
	if (!p_enhance_config)
	{
		return;
	}
	int old_level = _RawGetSwordLevel(sword_id);
	if (old_level < 0 || old_level >= p_enhance_config->configs.size())
	{
		return;
	}
	auto& upgrade_config = p_enhance_config->configs[old_level];
	if (player->GetLevel() < upgrade_config.need_level)
	{
		return;
	}
	money_t need_money = upgrade_config.cost_money;
	if (need_money > 0 && need_money > player->GetMoney(MT_BIND))
	{
		return;
	}
	ITEM_CHECK_INFO item_check_info;
	if (!player->CheckItemExistAtBackpack2(item_check_info, upgrade_config.cost_item_tid, upgrade_config.cost_item_count, false))
	{
		return;
	}

	//升级
	int old_player_fighting_capacity = player->GetFightingCapactiy();
	int sword_fighting_capacity = 0;
	if (!_SwordUpgrade(sword_id, old_level, sword_fighting_capacity))
	{
		return;
	}
	int new_level = _RawGetSwordLevel(sword_id);
	if (new_level != old_level + 1)
	{
		return;
	}

	FuncInfo func_info{kFuncCodeSevenCrimeOpt, PB::SCO_UPGRADE_SWORD, sword_id};
	player->DecItem2(item_check_info, func_info);
	if (need_money > 0)
	{
		player->DecMoney(func_info, MT_BIND, need_money);
	}

	if (upgrade_config.speak_id > 0)
	{
		gplayer_imp::SPEAK_PARAM_MAP map;
		raw_wrapper& h1 = map[SYS_SPEAK::SEVEN_CRIME_SWORD_ID];
		SYS_SPEAK::MakeTypeInt(h1, SYS_SPEAK::SEVEN_CRIME_SWORD_ID, sword_id);
		raw_wrapper& h2 = map[SYS_SPEAK::SEVEN_CRIME_SWORD_LEVEL];
		SYS_SPEAK::MakeTypeInt(h2, SYS_SPEAK::SEVEN_CRIME_SWORD_LEVEL, new_level);
		player->SystemSpeak2(upgrade_config.speak_id, &map);
	}

	PB::gp_seven_crime_re m;
	m.set_ret(S2C::ERR_SUCCESS);
	m.set_opt(PB::SCO_UPGRADE_SWORD);
	m.set_sword_id(sword_id);
	m.set_sword_level(new_level);
	m.set_sword_fight_capacity(sword_fighting_capacity);
	m.set_all_sword_fight_capacity(_data.all_sword_fighting_capacity());
	player->Runner()->QuietSend<S2C::CMD::PBS2C>(m);

	player->GetAchievement().OnSevenCrimeSwordLevelChange(player);

	SLOG(FORMAT, "seven_crime_upgrade_sword")
	.P("roleid", player->GetRoleID())
	.P("is_main", sword_id == _data.main_sword_id())
	.P("sword_id", sword_id)
	.P("old_level", old_level)
	.P("new_level", new_level)
	.P("old_fighting_capacity", old_player_fighting_capacity)
	.P("new_fighting_capacity", player->GetFightingCapactiy())
	.P("cost_item_id", upgrade_config.cost_item_tid)
	.P("cost_item_num", upgrade_config.cost_item_count)
	.P("cost_money", need_money);
}

void player_seven_crime::_CmdAdvanceUpgradeSword(gplayer_imp *player, int sword_id, const PB::gp_item_combine_advance& material)
{
	int enhance_config_id = seven_crime_config::GetInstance().GetSwordEnhanceConfig(sword_id);
	if (enhance_config_id <= 0)
	{
		return;
	}
	auto p_enhance_config = ENHANCE_CONFIG::get(enhance_config_id);
	if (!p_enhance_config)
	{
		return;
	}
	int old_level = _RawGetSwordLevel(sword_id);
	if (old_level < 0 || old_level >= p_enhance_config->configs.size())
	{
		return;
	}
	auto& upgrade_config = p_enhance_config->configs[old_level];
	if (player->GetLevel() < upgrade_config.need_level)
	{
		return;
	}
	money_t need_money = upgrade_config.cost_money;
	if (need_money > 0 && need_money > player->GetMoney(MT_BIND))
	{
		return;
	}
	if (upgrade_config.cost_item_tid <= 0 || upgrade_config.cost_item_count <= 0)
	{
		return;
	}
	if (material.target() != upgrade_config.cost_item_tid)
	{
		return;
	}

	// 计算背包中材料的数量和需要补足的数量
	ITEM_CHECK_INFO info_material(upgrade_config.cost_item_tid, upgrade_config.cost_item_count, false);
	player->CheckItemExistAtBackpack2(info_material, 0, 0, false);
	int material_exist_num = std::min(info_material.GetTotalCount(), (size_t)upgrade_config.cost_item_count);//已经拥有的材料数量
	int material_combine_num = upgrade_config.cost_item_count - material_exist_num;//需要补足的材料数量
	if (material_combine_num <= 0)
	{
		return;//材料足够，应该走正常的升级流程
	}

	// advance合并与消耗
	std::map<tid_t, int> exist_material;
	std::map<tid_t, int> need_material;
	unsigned int cost_material        = 0;
	int          money_cost_material  = 0;
	int          extra_id_material    = 0;
	int          exist_extra_material = 0;
	FuncInfo func_info{kFuncCodeSevenCrimeOpt, PB::SCO_ADVANCE_UPGRADE_SWORD, sword_id};
	PB::gp_item_combine_advance combine_info = material;
	combine_info.mutable_slot()->set_location(GNET::IL_EQUIPMENT_ENHANCE);
	std::stringstream ss;
	int ret = player->CheckCombineItemAdvance(
	              combine_info,
	              item_manager::ACT_SEVEN_CRIME_UPGRADE_SWORD,
	              material_combine_num,
	              exist_material, need_material,
	              cost_material, money_cost_material,
	              extra_id_material, exist_extra_material,
	              func_info, ss
	          );
	if (ret != 0)
	{
		return;
	}

	//升级
	int old_player_fighting_capacity = player->GetFightingCapactiy();
	int fighting_capacity = 0;
	if (!_SwordUpgrade(sword_id, old_level, fighting_capacity))
	{
		return;
	}
	int new_level = _RawGetSwordLevel(sword_id);
	if (new_level != old_level + 1)
	{
		return;
	}

	// 合并消耗, 前边检查通过 这里一定成功
	player->DecCombineItemAdvance(
	    material, material_combine_num,
	    exist_material, need_material,
	    cost_material, money_cost_material,
	    extra_id_material, exist_extra_material,
	    func_info
	);

	// 扣除背包里已存在的道具
	if (material_exist_num > 0)
	{
		player->DecItem2(info_material, func_info);
	}

	// 扣钱
	if (need_money > 0)
	{
		player->DecMoney(func_info, MT_BIND, need_money);
	}

	if (upgrade_config.speak_id > 0)
	{
		gplayer_imp::SPEAK_PARAM_MAP map;
		raw_wrapper& h1 = map[SYS_SPEAK::SEVEN_CRIME_SWORD_ID];
		SYS_SPEAK::MakeTypeInt(h1, SYS_SPEAK::SEVEN_CRIME_SWORD_ID, sword_id);
		raw_wrapper& h2 = map[SYS_SPEAK::SEVEN_CRIME_SWORD_LEVEL];
		SYS_SPEAK::MakeTypeInt(h2, SYS_SPEAK::SEVEN_CRIME_SWORD_LEVEL, new_level);
		player->SystemSpeak2(upgrade_config.speak_id, &map);
	}

	PB::gp_seven_crime_re m;
	m.set_ret(S2C::ERR_SUCCESS);
	m.set_opt(PB::SCO_ADVANCE_UPGRADE_SWORD);
	m.set_sword_id(sword_id);
	m.set_sword_level(new_level);
	m.set_sword_fight_capacity(fighting_capacity);
	m.set_all_sword_fight_capacity(_data.all_sword_fighting_capacity());
	player->Runner()->QuietSend<S2C::CMD::PBS2C>(m);

	player->GetAchievement().OnSevenCrimeSwordLevelChange(player);

	SLOG(FORMAT, "seven_crime_upgrade_sword_advance")
	.P("roleid", player->GetRoleID())
	.P("is_main", sword_id == _data.main_sword_id())
	.P("sword_id", sword_id)
	.P("old_level", old_level)
	.P("new_level", new_level)
	.P("old_fighting_capacity", old_player_fighting_capacity)
	.P("new_fighting_capacity", player->GetFightingCapactiy())
	.P("cost_item_id", upgrade_config.cost_item_tid)
	.P("cost_item_num", material_exist_num)
	.P("cost_money", need_money)
	.P("material", ss.str());
}

item_equipment *player_seven_crime::_GetCrimeEquipment(gplayer_imp *player, int index, int tid)
{
	item_list& backpack = player->GetInventory().GetBackpack();
	if (index < 0 || index >= backpack.Size())
	{
		return NULL;
	}
	item *t = backpack[index];
	if (t == NULL || t->GetTID() != tid)
	{
		return NULL;
	}
	if (t->IsSecurityLocked() || t->IsUnidentified() || t->GetTemplateType() != ITT_EQUIPMENT)
	{
		return NULL;
	}
	item_equipment *e = dynamic_cast<item_equipment *>(t);
	if (e == NULL || !e->IsSevenCrime())
	{
		return NULL;
	}
	const PB::equip_seven_crime_t& equip_crime = e->GetEssence().seven_crime();
	if (equip_crime.type() <= 0 || equip_crime.level() <= 0 || equip_crime.quality() <= 0)
	{
		return NULL;
	}
	return e;
}

PB::player_seven_crime_t::sacrifice_slot_t *player_seven_crime::_GetSacrificeSlot(gplayer_imp *player, int slot_idx)
{
	if (slot_idx < 0 || slot_idx + 1 > seven_crime_config::GetInstance().GetSacrificeSlotNum(player->GetLevel()))
	{
		return NULL;
	}
	while (_data.sacrifice_slot_size() < slot_idx + 1)
	{
		if (_data.add_sacrifice_slot() == NULL)
		{
			return NULL;
		}
	}
	return _data.mutable_sacrifice_slot(slot_idx);
}

PB::equip_seven_crime_t *player_seven_crime::_GetSacrificeCrime(gplayer_imp *player, int slot_idx, int crime_idx)
{
	PB::player_seven_crime_t::sacrifice_slot_t *slot = _GetSacrificeSlot(player, slot_idx);
	if (slot == NULL)
	{
		return NULL;
	}
	if (crime_idx < 0 || crime_idx + 1 > seven_crime_config::GetInstance().GetSacrificeSlotCrimeNum())
	{
		return NULL;
	}
	while (slot->equip_crime_size() < crime_idx + 1)
	{
		if (slot->add_equip_crime() == NULL)
		{
			return NULL;
		}
	}
	return slot->mutable_equip_crime(crime_idx);
}

void player_seven_crime::_CmdSacrifice(gplayer_imp *player, int index, int tid, int slot_idx, int crime_idx)
{
	if (player->IsCombatState())
	{
		return;
	}

	item_equipment *e = _GetCrimeEquipment(player, index, tid);
	if (e == NULL)
	{
		return;
	}
	PB::equip_seven_crime_t& equip_crime = *e->GetEssence().mutable_seven_crime();

	PB::player_seven_crime_t::sacrifice_slot_t *s = _GetSacrificeSlot(player, slot_idx);
	if (s == NULL)
	{
		return;
	}
	PB::player_seven_crime_t::sacrifice_slot_t& sacrifice_slot = *s;

	//其它位置没有献祭过相同的罪责
	for (int i = 0; i < sacrifice_slot.equip_crime_size(); ++i)
	{
		if (i != crime_idx && sacrifice_slot.equip_crime(i).type() == equip_crime.type())
		{
			return;
		}
	}

	PB::equip_seven_crime_t *c = _GetSacrificeCrime(player, slot_idx, crime_idx);
	if (c == NULL)
	{
		return;
	}
	PB::equip_seven_crime_t& sacrifice_crime = *c;
	if (sacrifice_crime.type() == equip_crime.type())
	{
		if (equip_crime.level() < sacrifice_crime.level() && equip_crime.quality() <= sacrifice_crime.quality())
		{
			return;
		}
		if (equip_crime.quality() < sacrifice_crime.quality() && equip_crime.level() <= sacrifice_crime.level())
		{
			return;
		}
	}

	//通过检查，开始替换
	sacrifice_crime.CopyFrom(equip_crime);
	equip_crime.Clear();
	player->DecItemAtIndex(GNET::IL_BACKPACK, index, 1, {kFuncCodeSevenCrimeOpt, PB::SCO_SACRIFICE});
	_OnSacrificeChange();
	_UpdateVirtualFightingCapacity();

	PB::gp_seven_crime_re m;
	m.set_ret(S2C::ERR_SUCCESS);
	m.set_opt(PB::SCO_SACRIFICE);
	m.set_equipment_index(index);
	m.mutable_info()->CopyFrom(_data);
	player->Runner()->QuietSend<S2C::CMD::PBS2C>(m);

	player->GetAchievement().OnSevenCrimeSacrificeQualityChange(player);

	SLOG(FORMAT, "seven_crime_sacrifice")
	.P("account", player->GetAccount().ToOctets())
	.P("roleid", player->GetRoleID())
	.P("equip_tid", tid)
	.P("sacrifice_crime", sacrifice_crime.ShortDebugString().c_str());
}

void player_seven_crime::_CmdNewEquipmentProp(gplayer_imp *player, int index, int tid, bool better)
{
	item_equipment *equipment = _GetCrimeEquipment(player, index, tid);
	if (equipment == NULL)
	{
		return;
	}
	PB::equip_seven_crime_t& equip_crime = *equipment->GetEssence().mutable_seven_crime();
	if (equip_crime.prop().prop_arr_size() <= 0)
	{
		return;
	}

	int need_item_tid = 0;
	int need_item_num = 0;
	if (better)
	{
		seven_crime_config::GetInstance().GetBetterPropNeedItem(equip_crime.level(), need_item_tid, need_item_num);
	}
	else
	{
		seven_crime_config::GetInstance().GetNewPropNeedItem(equip_crime.level(), need_item_tid, need_item_num);
	}
	if (need_item_tid <= 0 || need_item_num <= 0)
	{
		return;
	}
	ITEM_CHECK_INFO info;
	if (!player->CheckItemExist2(info, need_item_tid, need_item_num, false))
	{
		return;
	}

	PB::equip_seven_crime_t::crime_prop_set_t prop_tmp;
	prop_tmp.Swap(equip_crime.mutable_prop_new());
	int prop_num = equip_crime.prop().prop_arr_size();
	if (_HasSpecialProp(equip_crime.prop()))
	{
		prop_num --;
	}
	bool succ = seven_crime_config::GetInstance().GenCrimeProp(equip_crime.quality(), prop_num, equip_crime.level(), better, *equip_crime.mutable_prop_new());
	if (!succ)
	{
		equip_crime.mutable_prop_new()->Swap(&prop_tmp);
		return;
	}
	std::string str = equip_crime.ShortDebugString();
	equipment->SaveToContent(true);
	player->CmdItemInfo(GNET::IL_BACKPACK, index);

	FuncInfo func_info{kFuncCodeSevenCrimeOpt, better ? PB::SCO_BETTER_EQUIPMENT_PROP : PB::SCO_NEW_EQUIPMENT_PROP};
	player->DecItem2(info, func_info);

	PB::gp_seven_crime_re m;
	m.set_ret(S2C::ERR_SUCCESS);
	m.set_opt(better ? PB::SCO_BETTER_EQUIPMENT_PROP : PB::SCO_NEW_EQUIPMENT_PROP);
	m.set_equipment_index(index);
	player->Runner()->QuietSend<S2C::CMD::PBS2C>(m);

	SLOG(FORMAT, "seven_crime_new_equipment_prop")
	.P("account", player->GetAccount().ToOctets())
	.P("roleid", player->GetRoleID())
	.P("better", better)
	.P("equip_tid", tid)
	.P("equip_crime", str.c_str());
}

void player_seven_crime::_CmdUseEquipmentProp(gplayer_imp *player, int index, int tid)
{
	item_equipment *equipment = _GetCrimeEquipment(player, index, tid);
	if (equipment == NULL)
	{
		return;
	}
	PB::equip_seven_crime_t& equip_crime = *equipment->GetEssence().mutable_seven_crime();
	if (equip_crime.prop_new().prop_arr_size() <= 0)
	{
		return;
	}

	equip_crime.mutable_prop()->Swap(equip_crime.mutable_prop_new());
	equip_crime.clear_prop_new();
	std::string str = equip_crime.ShortDebugString();
	equipment->SaveToContent(true);
	player->CmdItemInfo(GNET::IL_BACKPACK, index);

	PB::gp_seven_crime_re m;
	m.set_ret(S2C::ERR_SUCCESS);
	m.set_opt(PB::SCO_USE_EQUIPMENT_PROP);
	m.set_equipment_index(index);
	player->Runner()->QuietSend<S2C::CMD::PBS2C>(m);

	SLOG(FORMAT, "seven_crime_use_equipment_prop")
	.P("account", player->GetAccount().ToOctets())
	.P("roleid", player->GetRoleID())
	.P("equip_tid", tid)
	.P("equip_crime", str.c_str());
}

void player_seven_crime::_CmdDropEquipmentProp(gplayer_imp *player, int index, int tid)
{
	item_equipment *equipment = _GetCrimeEquipment(player, index, tid);
	if (equipment == NULL)
	{
		return;
	}
	PB::equip_seven_crime_t& equip_crime = *equipment->GetEssence().mutable_seven_crime();
	if (equip_crime.prop_new().prop_arr_size() <= 0)
	{
		return;
	}

	equip_crime.clear_prop_new();
	std::string str = equip_crime.ShortDebugString();
	equipment->SaveToContent(true);
	player->CmdItemInfo(GNET::IL_BACKPACK, index);

	PB::gp_seven_crime_re m;
	m.set_ret(S2C::ERR_SUCCESS);
	m.set_opt(PB::SCO_DROP_EQUIPMENT_PROP);
	m.set_equipment_index(index);
	player->Runner()->QuietSend<S2C::CMD::PBS2C>(m);

	SLOG(FORMAT, "seven_crime_drop_equipment_prop")
	.P("account", player->GetAccount().ToOctets())
	.P("roleid", player->GetRoleID())
	.P("equip_tid", tid)
	.P("equip_crime", str.c_str());
}

void player_seven_crime::_CmdNewSacrificeProp(gplayer_imp *player, int slot_idx, int crime_idx, bool better)
{
	PB::equip_seven_crime_t *s = _GetSacrificeCrime(player, slot_idx, crime_idx);
	if (s == NULL)
	{
		return;
	}
	PB::equip_seven_crime_t& sacrifice_crime = *s;
	if (sacrifice_crime.prop().prop_arr_size() <= 0)
	{
		return;
	}

	int need_item_tid = 0;
	int need_item_num = 0;
	if (better)
	{
		seven_crime_config::GetInstance().GetBetterPropNeedItem(sacrifice_crime.level(), need_item_tid, need_item_num);
	}
	else
	{
		seven_crime_config::GetInstance().GetNewPropNeedItem(sacrifice_crime.level(), need_item_tid, need_item_num);
	}
	if (need_item_tid <= 0 || need_item_num <= 0)
	{
		return;
	}
	ITEM_CHECK_INFO info;
	if (!player->CheckItemExist2(info, need_item_tid, need_item_num, false))
	{
		return;
	}

	PB::equip_seven_crime_t::crime_prop_set_t prop_tmp;
	prop_tmp.Swap(sacrifice_crime.mutable_prop_new());
	int prop_num = sacrifice_crime.prop().prop_arr_size();
	if (_HasSpecialProp(sacrifice_crime.prop()))
	{
		prop_num --;
	}
	bool succ = seven_crime_config::GetInstance().GenCrimeProp(sacrifice_crime.quality(), prop_num, sacrifice_crime.level(), better, *sacrifice_crime.mutable_prop_new());
	if (!succ)
	{
		sacrifice_crime.mutable_prop_new()->Swap(&prop_tmp);
		return;
	}
	std::string str = sacrifice_crime.ShortDebugString();

	FuncInfo func_info{kFuncCodeSevenCrimeOpt, better ? PB::SCO_BETTER_SACRIFICE_PROP : PB::SCO_NEW_SACRIFICE_PROP};
	player->DecItem2(info, func_info);

	PB::gp_seven_crime_re m;
	m.set_ret(S2C::ERR_SUCCESS);
	m.set_opt(better ? PB::SCO_BETTER_SACRIFICE_PROP : PB::SCO_NEW_SACRIFICE_PROP);
	m.mutable_info()->CopyFrom(_data);
	m.set_sacrifice_slot_idx(slot_idx);
	m.set_sacrifice_crime_idx(crime_idx);
	player->Runner()->QuietSend<S2C::CMD::PBS2C>(m);

	SLOG(FORMAT, "seven_crime_new_sacrifice_prop")
	.P("account", player->GetAccount().ToOctets())
	.P("roleid", player->GetRoleID())
	.P("better", better)
	.P("slot_idx", slot_idx)
	.P("crime_idx", crime_idx)
	.P("sacrifice_crime", str.c_str());
}

void player_seven_crime::_CmdUseSacrificeProp(gplayer_imp *player, int slot_idx, int crime_idx)
{
	PB::equip_seven_crime_t *s = _GetSacrificeCrime(player, slot_idx, crime_idx);
	if (s == NULL)
	{
		return;
	}
	PB::equip_seven_crime_t& sacrifice_crime = *s;
	if (sacrifice_crime.prop_new().prop_arr_size() <= 0)
	{
		return;
	}

	sacrifice_crime.mutable_prop()->Swap(sacrifice_crime.mutable_prop_new());
	sacrifice_crime.clear_prop_new();
	std::string str = sacrifice_crime.ShortDebugString();
	_OnSacrificeChange();
	_UpdateVirtualFightingCapacity();

	PB::gp_seven_crime_re m;
	m.set_ret(S2C::ERR_SUCCESS);
	m.set_opt(PB::SCO_USE_SACRIFICE_PROP);
	m.mutable_info()->CopyFrom(_data);
	m.set_sacrifice_slot_idx(slot_idx);
	m.set_sacrifice_crime_idx(crime_idx);
	player->Runner()->QuietSend<S2C::CMD::PBS2C>(m);

	player->GetAchievement().OnSevenCrimeSacrificeQualityChange(player);

	SLOG(FORMAT, "seven_crime_use_sacrifice_prop")
	.P("account", player->GetAccount().ToOctets())
	.P("roleid", player->GetRoleID())
	.P("slot_idx", slot_idx)
	.P("crime_idx", crime_idx)
	.P("sacrifice_crime", str.c_str());
}

void player_seven_crime::_CmdDropSacrificeProp(gplayer_imp *player, int slot_idx, int crime_idx)
{
	PB::equip_seven_crime_t *s = _GetSacrificeCrime(player, slot_idx, crime_idx);
	if (s == NULL)
	{
		return;
	}
	PB::equip_seven_crime_t& sacrifice_crime = *s;
	if (sacrifice_crime.prop_new().prop_arr_size() <= 0)
	{
		return;
	}

	sacrifice_crime.clear_prop_new();
	std::string str = sacrifice_crime.ShortDebugString();

	PB::gp_seven_crime_re m;
	m.set_ret(S2C::ERR_SUCCESS);
	m.set_opt(PB::SCO_DROP_SACRIFICE_PROP);
	m.mutable_info()->CopyFrom(_data);
	m.set_sacrifice_slot_idx(slot_idx);
	m.set_sacrifice_crime_idx(crime_idx);
	player->Runner()->QuietSend<S2C::CMD::PBS2C>(m);

	SLOG(FORMAT, "seven_crime_drop_sacrifice_prop")
	.P("account", player->GetAccount().ToOctets())
	.P("roleid", player->GetRoleID())
	.P("slot_idx", slot_idx)
	.P("crime_idx", crime_idx)
	.P("sacrifice_crime", str.c_str());
}

//没有技能就按照1级返回，只能由外部调用
int player_seven_crime::GetSwordLevel(int sword_id) const
{
	if (!GNET::FuncSwitchManager::Instance().getFuncSwitch(kFuncCodeSevenCrime))
	{
		return 0;
	}
	if (!GNET::FuncSwitchManager::Instance().getFuncSwitch(kFuncCodeSevenCrimeSkill))
	{
		return 0;
	}
	auto it = _sword_level_map.find(sword_id);
	if (it == _sword_level_map.end())
	{
		return 1;
	}
	return std::max(1, it->second);
}

int player_seven_crime::_RawGetSwordLevel(int sword_id) const
{
	auto it = _sword_level_map.find(sword_id);
	if (it == _sword_level_map.end())
	{
		return 0;
	}
	return it->second;
}

int player_seven_crime::GetMainSwordLevel() const
{
	return GetSwordLevel(_data.main_sword_id());
}

float player_seven_crime::GetSwordLevelAverage() const
{
	float count = 0;
	for (int i = 1; i <= 7; ++i)
	{
		count += GetSwordLevel(i);
	}
	return count / 7;
}

//拥有的词条品质和/已装备的罪责最多拥有的词条数
//绿、蓝、紫、橙（对应1、2、3、4）
float player_seven_crime::GetSacrificeQualityAverage() const
{
	if (!GNET::FuncSwitchManager::Instance().getFuncSwitch(kFuncCodeSevenCrime))
	{
		return 0;
	}
	if (!GNET::FuncSwitchManager::Instance().getFuncSwitch(kFuncCodeSevenCrimeSacrifice))
	{
		return 0;
	}
	int prop_count = 0;//词条总数
	int quality_count = 0;//品质总和
	for (int i = 0; i < _data.sacrifice_slot_size(); ++i)
	{
		for (int j = 0; j < _data.sacrifice_slot(i).equip_crime_size(); ++j)
		{
			const PB::equip_seven_crime_t& c = _data.sacrifice_slot(i).equip_crime(j);
			for (int k = 0; k < c.prop().prop_arr_size(); ++k)
			{
				const PB::equip_seven_crime_t::crime_prop_t& cp = c.prop().prop_arr(k);
				if (cp.type() != 3)//排除特殊属性
				{
					prop_count ++;
					quality_count += seven_crime_config::GetInstance().GetPropQuality(c.level(), cp);
				}
			}
		}
	}
	if (prop_count <= 0)
	{
		return 0;
	}
	return quality_count / (float)prop_count;
}

void player_seven_crime::MakeRoleTradeSevenCrimeInfo(PB::role_trade_seven_crime_info& s_info)
{
	for (auto& kv : _sword_level_map)
	{
		auto *p_add_sword = s_info.add_swords();
		p_add_sword->set_id(kv.first);
		p_add_sword->set_lv(kv.second);
	}

	for (int i = 0; i < _data.sacrifice_slot_size(); ++i)
	{
		for (int j = 0; j < _data.sacrifice_slot(i).equip_crime_size(); ++j)
		{
			const PB::equip_seven_crime_t& equip_crime = _data.sacrifice_slot(i).equip_crime(j);
			s_info.add_sacrifices(equip_crime.type());
		}
	}
}

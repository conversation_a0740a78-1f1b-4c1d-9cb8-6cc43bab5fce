#include "alchemy_runestone_manager.h"
#include "player.h"
#include "item.h"
#include "item/item_alchemy_runestone.h"
#include "skill_config_man.h"
#include "slog.h"
#include "achievement_manager.h"
#include "personal_target_manager.h"
#include "player_misc.h"
//#include <algorithm>

#include "gprotoc/item_info.pb.h"
#include "gprotoc/gp_alchemy_c2s.pb.h"
#include "gprotoc/ALCHEMY_RUNESTONE_ADDON_TYPE.pb.h"
#include "gprotoc/role_complicated_data_alchemy.pb.h"
#include "gprotoc/ALCHEMY_ACTION.pb.h"
#include "gprotoc/alchemy_runestone_essence_t.pb.h"
#include "gprotoc/role_complicated_data.pb.h"
#include "gprotoc/alchemy_other_data.pb.h"
#include "gprotoc/gp_alchemy_re.pb.h"
#include "gprotoc/gp_alchemy_on_effect_change.pb.h"
#include "gprotoc/alchemy_slot.pb.h"
#include "gprotoc/player_universal_data_t.pb.h"
#include "gprotoc/role_trade_runestone_info.pb.h"

bool alchemy_runestone_config_reader::LoadConfig()
{
	//校验模板
	alchemy_runestone_limit_break_item = 0;
	alchemy_runestone_base_exp = 0;
	alchemy_runestone_max_level = 0;
	slot_max_level = 0;
	slot_stage_up_level = 1;
	try
	{

		LuaStateScoped k("./config/script/alchemy_runestone_2_config.lua");
		if (k.L == NULL)
		{
			return false;
		}
		LuaTableNode root;
		bool rst = LuaConfReader::parseFromLuaStack(k.L, root);
		if (!rst)
		{
			return false;
		}
		FETCH_IF_EXIST(alchemy_runestone_max_special_addon);
		FETCH_CONTAINER_IF_EXIST(alchemy_runestone_addon_at_level_1);
		for (auto& i : alchemy_runestone_addon_at_level_1) //总组
		{
			if (i.second.size() > alchemy_runestone_max_special_addon)
			{
				LOG_TRACE("alchemy : 炼金矩阵配置文件异常：1级可初始化词条数大于石头词条数量上限 在 发生器id：%d. 在 alchemy_runestone_2_config.lua:alchemy_runestone_addon_at_level_1", i.first);
				return false;
			}
			std::vector<std::vector<float>> temp;
			for (auto& j : i.second) //组
			{
				std::vector<float> temp2;
				for (auto& k : j) //项
				{
					auto iter = k.find("weight");
					if (iter == k.end())
					{
						return false;
					}
					temp2.push_back(iter->second);
				}
				CheckAndNormalizeRandOdds(temp2.data(), sizeof(float), temp2.size());
				temp.push_back(temp2);
			}
			alchemy_runestone_addon_at_level_1_weight[i.first] = temp;
		}
		FETCH_CONTAINER_IF_EXIST(alchemy_slot_limit);
		FETCH_CONTAINER_IF_EXIST(alchemy_runestone_base_addon_group);
		FETCH_CONTAINER_IF_EXIST(alchemy_runestone_per_level);
		FETCH_CONTAINER_IF_EXIST(slot_addon_level);
		FETCH_CONTAINER_IF_EXIST(alchemy_runestone_need_exp);
		{
			int sum = 0;
			alchemy_runestone_sum_exp.push_back(sum);//占1级位
			for (auto& i : alchemy_runestone_need_exp)
			{
				alchemy_runestone_sum_exp.push_back(sum);
				sum += i;
			}
		}
		FETCH_CONTAINER_IF_EXIST(slot_need_exp);
		FETCH_CONTAINER_IF_EXIST(alchemy_runestone_exp_lost);
		FETCH_IF_EXIST(alchemy_runestone_limit_break_item);
		FETCH_CONTAINER_IF_EXIST(alchemy_runestone_limit_break_item_count);
		for (auto& i : alchemy_runestone_limit_break_item_count)
		{
			if (i.second <= 0)
			{
				continue;
			}
			alchemy_runestone_limit_break_level.push_back(i.first);
		}
		sort(alchemy_runestone_limit_break_level.begin(), alchemy_runestone_limit_break_level.end());
		{
			int sum = 0;
			for (auto& i : alchemy_runestone_limit_break_level)
			{
				sum += alchemy_runestone_limit_break_item_count[i - 1];
				alchemy_runestone_limit_break_need_item_count.push_back(sum);
			}
		}

		FETCH_CONTAINER_IF_EXIST(alchemy_runestone_exp_item);
		FETCH_CONTAINER_IF_EXIST(slot_exp_item);
		FETCH_CONTAINER_IF_EXIST(slot_refresh_item);
		FETCH_CONTAINER_IF_EXIST(slot_refresh_weight);
		for (auto& i : slot_refresh_weight)
		{
			for (auto& j : i.second)
			{
				auto iter = slot_refresh_weight_cal.find(i.first);
				if (iter != slot_refresh_weight_cal.end())
				{
					slot_refresh_weight_cal[i.first].push_back(j[2]);
				}
				else
				{
					slot_refresh_weight_cal.insert(std::pair<int, std::vector<float>>(i.first, std::vector<float>()));
					slot_refresh_weight_cal[i.first].push_back(j[2]);
				}
			}
			CheckAndNormalizeRandOdds(slot_refresh_weight_cal[i.first].data(), sizeof(float), slot_refresh_weight_cal[i.first].size());
		}
		FETCH_CONTAINER_IF_EXIST(suit_skill_list);
		for (auto& i : suit_skill_list)
		{
			suit_skill_6.insert(i.second[2]);
		}
		FETCH_IF_EXIST(alchemy_runestone_base_exp);
		FETCH_IF_EXIST(alchemy_runestone_max_level);
		FETCH_IF_EXIST(slot_max_level);
		FETCH_CONTAINER_IF_EXIST(light_effect_unlock_level);
		FETCH_CONTAINER_IF_EXIST(alchemy_base_level);
		for (auto& i : light_effect_unlock_level)
		{
			light_effect_level_unlock[i.second].insert(i.first);
		}
		{
			std::set<int> temp_effect_id;
			for (int i = 0; i <= alchemy_runestone_max_level; ++i)
			{
				auto iter = light_effect_level_unlock.find(i);
				if (iter != light_effect_level_unlock.end())
				{
					temp_effect_id = iter->second;
				}
				else
				{
					light_effect_level_unlock[i] = temp_effect_id;
				}
			}
		}
		FETCH_CONTAINER_IF_EXIST(alchemy_addon_max_num);
		FETCH_CONTAINER_IF_EXIST(slot_base_addon_group_build);
		FETCH_IF_EXIST(slot_stage_up_level);
		FETCH_CONTAINER_IF_EXIST(slot_level_up_cost_item_and_count_build);
		FETCH_CONTAINER_IF_EXIST(slot_stage_up_cost_item_and_count);
		FETCH_CONTAINER_IF_EXIST(fight_point_calc_ratio);
	}
	catch (std::exception& exp)
	{
		return false;
	}
	return true;
}

template <int PROP_INDEX>
class AlchemyRunestoneAddonWrapper: public alchemy_runestone_addon
{
public:
	virtual void active(gplayer_imp *_imp, int param1, float param2, int64_t param3, double param4) override
	{
		ConditionSuppressCombatStat(_imp, SuppressCombatSystem::RUNESTONE)
			.Then(nullptr, "AlchemyRunestoneAddonWrapper::active: suppress addon active")
			.Else([param1, param2, param3, param4](gcreature_imp* imp) {
				imp->GetProperty().ModifyProperty(PROP_INDEX, param1, param2, param3, param4);
			}, "AlchemyRunestoneAddonWrapper::active: normal addon active");
	}

	virtual void deactive(gplayer_imp *_imp, int param1, float param2, int64_t param3, double param4) override
	{
		ConditionSuppressCombatStat(_imp, SuppressCombatSystem::RUNESTONE)
			.Then(nullptr, "AlchemyRunestoneAddonWrapper::deactive: suppress addon deactive")
			.Else([param1, param2, param3, param4](gcreature_imp* imp) {
				imp->GetProperty().ModifyProperty(PROP_INDEX, -param1, -param2, -param3, -param4);
			}, "AlchemyRunestoneAddonWrapper::deactive: normal addon deactive");
	}
};

alchemy_runestone_config_reader& alchemy_runestone_config_reader::GetInstance()
{
	static alchemy_runestone_config_reader _Instance;
	return _Instance;
}

alchemy_runestone_config_reader::alchemy_runestone_config_reader()
{
}

modify_attribute_addon_manager::modify_attribute_addon_manager()
{
	_addon_list[ALCHEMY_ADDON_ATK] = new AlchemyRunestoneAddonWrapper<property_template::INDEX_pointPhyAtk>();
	_addon_list[ALCHEMY_ADDON_ATK] = new AlchemyRunestoneAddonWrapper<property_template::INDEX_pointMagAtk>();
	_addon_list[ALCHEMY_ADDON_DEF] = new AlchemyRunestoneAddonWrapper<property_template::INDEX_pointPhyDef>();
	_addon_list[ALCHEMY_ADDON_MDEF] = new AlchemyRunestoneAddonWrapper<property_template::INDEX_pointMagDef>();
	_addon_list[ALCHEMY_ADDON_HP] = new AlchemyRunestoneAddonWrapper<property_template::INDEX_pointHP>();
	_addon_list[ALCHEMY_ADDON_CRIT] = new AlchemyRunestoneAddonWrapper<property_template::INDEX_pointCritLevel>();
	_addon_list[ALCHEMY_ADDON_MUL_ATK] = new AlchemyRunestoneAddonWrapper<property_template::INDEX_pointPsychokinesisLevel>();
	_addon_list[ALCHEMY_ADDON_CRIT_DEF] = new AlchemyRunestoneAddonWrapper<property_template::INDEX_pointCritResLevel>();
	_addon_list[ALCHEMY_ADDON_PIERCE] = new AlchemyRunestoneAddonWrapper<property_template::INDEX_pointPierceLevel>();
	_addon_list[ALCHEMY_ADDON_CDR] = new AlchemyRunestoneAddonWrapper<property_template::INDEX_pointCDReduLevel>();
	_addon_list[ALCHEMY_ADDON_PIERCE_DEF] = new AlchemyRunestoneAddonWrapper<property_template::INDEX_pointPierceReduLevel>();
	_addon_list[ALCHEMY_ADDON_CRIT_RATIO] = new AlchemyRunestoneAddonWrapper<property_template::INDEX_pointCritRatio>();
}
modify_attribute_addon_manager::~modify_attribute_addon_manager()
{
	for (auto& i : _addon_list)
	{
		if (i.second != nullptr)
		{
			delete i.second;
			i.second = nullptr;
		}
	}
}
modify_attribute_addon_manager& modify_attribute_addon_manager::GetInstance()
{
	static modify_attribute_addon_manager _Instance;
	return _Instance;
}
void modify_attribute_addon_manager::Active(int place, int level, gplayer_imp *_imp, std::vector<std::pair<int, int>> special_skill, std::vector<item_alchemy_special_addon> special_prop, int list_index)
{
	if ( !GET_FUNC_SWITCH(kFuncCodeAlchemyRunestoneOpen))
	{
		return;
	}
	std::vector<std::pair<ALCHEMY_RUNESTONE_ADDON_TYPE, int> > base_prop_list;
	auto iter2 = ALCHEMY_RUNESTONE_CONFIG.alchemy_runestone_base_addon_group.find(place);
	if (iter2 != ALCHEMY_RUNESTONE_CONFIG.alchemy_runestone_base_addon_group.end())
	{
		for (auto& i : iter2->second)
		{
			auto iter = _addon_list.find((ALCHEMY_RUNESTONE_ADDON_TYPE)i[1]);
			if (iter == _addon_list.end() || iter->second == nullptr)
			{
				return;
			}
			base_prop_list.push_back(std::pair<ALCHEMY_RUNESTONE_ADDON_TYPE, int>((ALCHEMY_RUNESTONE_ADDON_TYPE)i[1], i[0]));
		}
	}
	int slot_addon = 10000;
	float _inner_per = 1 / (float)slot_addon;
	alchemy_runestone_slot& slot = ((gplayer_imp *)_imp)->GetAlchemyRunestone().GetSlotInfo(list_index);
	if (slot.IsActive())
	{
		return;
	}
	LOG_TRACE("alchemy : active list_index:%d", list_index);
	if (!GET_FUNC_SWITCH(kFuncCodeAlchemySlotOpen))
	{
		for (auto& i : base_prop_list)
		{
			auto var = ALCHEMY_RUNESTONE_CONFIG.alchemy_runestone_per_level[i.first][i.second][level];
			slot.param1[i.first] = var;
			slot.param2[i.first] = var;
			slot.param3[i.first] = var;
			slot.param4[i.first] = var;
			slot.active_type.push_back((ALCHEMY_RUNESTONE_ADDON_TYPE)i.first);
			auto iter = _addon_list.find((ALCHEMY_RUNESTONE_ADDON_TYPE)i.first);
			if (iter != _addon_list.end() && iter->second != nullptr)
			{
				iter->second->active(_imp, var, var, var, var);
			}
		}
	}
	else
	{
		for (auto& i : base_prop_list)
		{
			auto var = ALCHEMY_RUNESTONE_CONFIG.alchemy_runestone_per_level[i.first][i.second][level];
			slot_addon += slot.GetPercent();
			slot.param1[i.first] = var * (slot_addon * _inner_per);
			slot.param2[i.first] = var * (slot_addon * _inner_per);
			slot.param3[i.first] = var * (slot_addon * _inner_per);
			slot.param4[i.first] = var * (slot_addon * _inner_per);
			slot.active_type.push_back((ALCHEMY_RUNESTONE_ADDON_TYPE)i.first);
			auto iter = _addon_list.find((ALCHEMY_RUNESTONE_ADDON_TYPE)i.first);
			if (iter != _addon_list.end() && iter->second != nullptr)
			{
				iter->second->active(_imp, slot.param1[i.first], slot.param2[i.first], slot.param3[i.first], slot.param4[i.first]);
			}
		}
	}
	slot.SetActive(true);
	auto& prop = _imp->GetSkill();
	for (auto& i : special_skill)
	{
		object_interface oif(_imp);
		prop.Upgrade(i.first, i.second, oif);
	}
	for (auto& i : special_prop)
	{
		auto iter = _addon_list.find((ALCHEMY_RUNESTONE_ADDON_TYPE)i.addon_type);
		if (iter != _addon_list.end() && iter->second != nullptr)
		{
			iter->second->active(_imp, i.value, i.value, i.value, i.value);
		}
	}
}
void modify_attribute_addon_manager::DeActive(int place, int level, gplayer_imp *_imp, std::vector<std::pair<int, int>> special_skill, std::vector<item_alchemy_special_addon> special_prop, int list_index)
{

	LOG_TRACE("alchemy : deactive list_index:%d", list_index);
	alchemy_runestone_slot& slot = ((gplayer_imp *)_imp)->GetAlchemyRunestone().GetSlotInfo(list_index);
	if (!slot.IsActive())
	{
		return;
	}
	slot.SetActive(false);
	std::vector<std::pair<ALCHEMY_RUNESTONE_ADDON_TYPE, int> > base_prop_list;
	auto iter2 = ALCHEMY_RUNESTONE_CONFIG.alchemy_runestone_base_addon_group.find(place);
	if (iter2 != ALCHEMY_RUNESTONE_CONFIG.alchemy_runestone_base_addon_group.end())
	{
		for (auto& i : iter2->second)
		{
			auto iter = _addon_list.find((ALCHEMY_RUNESTONE_ADDON_TYPE)i[1]);
			if (iter == _addon_list.end() || iter->second == nullptr)
			{
				return;
			}
			base_prop_list.push_back(std::pair<ALCHEMY_RUNESTONE_ADDON_TYPE, int>((ALCHEMY_RUNESTONE_ADDON_TYPE)i[1], i[0]));
		}
	}
	for (auto& i : base_prop_list)
	{
		auto iter = _addon_list.find((ALCHEMY_RUNESTONE_ADDON_TYPE)i.first);
		if (iter == _addon_list.end() || iter->second == nullptr)
		{
			return ;
		}
		iter->second->deactive(_imp, slot.param1[i.first], slot.param2[i.first], slot.param3[i.first], slot.param4[i.first]);
		//因战力计算需要，清空处理
		slot.param1[i.first] = 0;
		slot.param2[i.first] = 0;
		slot.param3[i.first] = 0;
		slot.param4[i.first] = 0;
		slot.active_type.clear();
	}
	auto& prop = _imp->GetSkill();
	for (auto& i : special_skill)
	{
		object_interface oif(_imp);
		prop.Degrade(i.first, i.second, oif);
	}
	for (auto& i : special_prop)
	{
		auto iter = _addon_list.find((ALCHEMY_RUNESTONE_ADDON_TYPE)i.addon_type);
		if (iter != _addon_list.end() && iter->second != nullptr)
		{
			iter->second->deactive(_imp, i.value, i.value, i.value, i.value);
		}
	}
}

void modify_attribute_addon_manager::SlotActive(gplayer_imp *_imp, ALCHEMY_RUNESTONE_ADDON_TYPE type, int param)
{
	auto iter = _addon_list.find((ALCHEMY_RUNESTONE_ADDON_TYPE)type);
	if (iter != _addon_list.end())
	{
		iter->second->active(_imp, param, param, param, param);
	}
}

void modify_attribute_addon_manager::SlotDeActive(gplayer_imp *_imp, ALCHEMY_RUNESTONE_ADDON_TYPE type, int param)
{
	auto iter = _addon_list.find((ALCHEMY_RUNESTONE_ADDON_TYPE)type);
	if (iter != _addon_list.end())
	{
		iter->second->deactive(_imp, param, param, param, param);
	}
}

alchemy_runestone_manager::~alchemy_runestone_manager()
{
}

void alchemy_runestone_manager::runestone_add_exp(int item_tid, unsigned int package, int place)
{
	item_list *list = GetListByMask(package);
	if (list == nullptr)
	{
		LOG_TRACE("alchemy runestone : 未能找到需求的背包：%d", package);
		return;
	}
	item *item_temp = ((*list)[place]);
	if (item_temp == NULL)
	{
		LOG_TRACE("alchemy runestone : 未能找到目标符文石：背包索引:%d ,位置:%d", package, place);
		return;
	}
	if (item_temp->GetTemplateType() != ITT_ALCHEMY_RUNESTONE)
	{
		//出大问题,石头以外的东西进包了
		LOG_TRACE("alchemy runestone : 找到了'符文石'，但该道具并不是符文石：背包索引:%d ,位置:%d ,物品模板ID:%d", package, place, item_temp->GetTemplate()->_tid);
		return;
	}
	item_temp->OnAlchemyDeactivate(_imp);
	auto it = ALCHEMY_RUNESTONE_CONFIG.alchemy_runestone_exp_item.find(item_tid);
	if (it != ALCHEMY_RUNESTONE_CONFIG.alchemy_runestone_exp_item.end() && item_temp->alchemy_add_exp(it->second, _imp) && item_temp->GetIsInAlchemyEquip())
	{
		//有升级
		//管理器不会去管石头，只管套装
		check_and_reset_all();
	}
}

int alchemy_runestone_manager::runestone_add_exp(std::vector<item *> from_runestone, item *to_runestone, int extar_exp)
{
	//和上面那个不一样，在外部就处理好物品索引问题
	to_runestone->OnAlchemyDeactivate(_imp);
	for (auto& i : from_runestone)
	{
		i->OnAlchemyDeactivate(_imp);
	}
	int ret = to_runestone->alchemy_add_exp_by_mix(from_runestone, _imp, extar_exp);
	check_and_reset_all();
	return ret;
}

void alchemy_runestone_manager::slot_add_exp(int item_tid, int place, int item_count)
{
	if (place < 0 || place >= _data.size())
	{
		return;
	}
	auto it = ALCHEMY_RUNESTONE_CONFIG.slot_exp_item.find(item_tid);
	if (it == ALCHEMY_RUNESTONE_CONFIG.slot_exp_item.end())
	{
		return;
	}
	if (place >= _data.size())
	{
		LOG_TRACE("alchemy slot : 索引异常:%d", place);
		return;
	}
	if (_data[place].GetLevel() + 1 == ALCHEMY_RUNESTONE_CONFIG.slot_max_level) //拦截满级溢出的经验道具使用
	{
		int max_cost = ((ALCHEMY_RUNESTONE_CONFIG.slot_need_exp[_data[place].GetLevel() - 1] - _data[place].GetExp()) / it->second)
		               + ((ALCHEMY_RUNESTONE_CONFIG.slot_need_exp[_data[place].GetLevel() - 1] - _data[place].GetExp()) % it->second > 0 ? 1 : 0);
		item_count = item_count < max_cost ? item_count : max_cost;
	}
	ITEM_CHECK_INFO info;
	if (!_imp->CheckItemExistAtBackpack2(info, it->first, item_count))
	{
		return;
	}
	if (!_data[place].try_add_exp(it->second * item_count)) //减少重刷次数
	{
		_data[place].add_exp(_imp, it->second * item_count);
		_imp->DecItem2(info, {kFuncCodeAlchemyItemCost});
		_imp->GetAchievement().OnUseItem(_imp, info.GetTid(), info.GetCount());
		return;
	}
	alchemy_runestone_equip_list *list = GetEquipList();
	if (list == nullptr)
	{
		return;
	}
	item *stone = ((*list)[place]);
	if (stone != nullptr)
	{
		stone->OnAlchemyDeactivate(_imp);
	}

	_data[place].add_exp(_imp, it->second * item_count);
	_imp->DecItem2(info, {kFuncCodeAlchemyItemCost});
	_imp->GetAchievement().OnUseItem(_imp, info.GetTid(), info.GetCount());
	check_and_reset_all();
}

void alchemy_runestone_manager::slot_refresh(int item_tid, int place)
{
	if (place >= _data.size())
	{
		LOG_TRACE("alchemy slot : 索引异常:%d", place);
		return;
	}
	alchemy_runestone_equip_list *list = GetEquipList();
	if (list == nullptr)
	{
		return;
	}
	item *stone = ((*list)[place]);
	if (stone != nullptr)
	{
		stone->OnAlchemyDeactivate(_imp);
		_data[place].refresh(_imp, item_tid);
		stone->OnAlchemyActivate(_imp);
	}
	else
	{
		_data[place].refresh(_imp, item_tid);
	}
}

void alchemy_runestone_manager::swap_slot(int place1, int place2)
{
	if (place1 >= _data.size() || place2 >= _data.size())
	{
		LOG_TRACE("alchemy slot : 索引异常:%d ,%d", place1, place2);
		return ;
	}
	std::swap(_data[place1], _data[place2]);
	//交换石头 //就不在这写了，单发一个交换石头协议吧
	_imp->CmdSwapItem(GNET::IL_ALCHEMY_RUNESTONE_EQUIP_LIST, place1, GNET::IL_ALCHEMY_RUNESTONE_EQUIP_LIST, place2);
}
void alchemy_runestone_manager::runestone_limit_break(item *runestone)
{
	if (runestone == nullptr)
	{
		return;
	}
	if (runestone->GetAlchemyBreakTimes() < 0 || runestone->GetAlchemyBreakTimes() >= ALCHEMY_RUNESTONE_CONFIG.alchemy_runestone_limit_break_level.size())
	{
		return;
	}
	if (ALCHEMY_RUNESTONE_CONFIG.alchemy_runestone_limit_break_level[runestone->GetAlchemyBreakTimes()] > runestone->GetAlchemyLevel())
	{
		return;
	}
	auto iter = ALCHEMY_RUNESTONE_CONFIG.alchemy_runestone_limit_break_item_count.find(runestone->GetAlchemyLevel());
	if (iter != ALCHEMY_RUNESTONE_CONFIG.alchemy_runestone_limit_break_item_count.end())
	{
		ITEM_CHECK_INFO info;
		if (!_imp->CheckItemExistAtBackpack2(info, iter->first, iter->second))
		{
			return;
		}
		runestone->AlchemyLimitBreak();
		_imp->DecItem2(info, {kFuncCodeAlchemyItemCost});
		_imp->GetAchievement().OnUseItem(_imp, info.GetTid(), info.GetCount());
	}
	else
	{
		LOG_TRACE("alchemy runestone : 异常突破请求，符文石等级:%d ,经验:%d", runestone->GetAlchemyLevel(), runestone->GetAlchemyExp());
		//满级了，或者还没到可以突破的等级，但是不知道怎么发过来的突破请求，正常不应该出现
	}
}

void alchemy_runestone_manager::check_and_reset_all()
{
	if (GET_FUNC_SWITCH(kFuncCodeAlchemySlotOpen))
	{
		DeactiveSlots();
		ActiveSlots();
	}

	if (!GET_FUNC_SWITCH(kFuncCodeAlchemyRunestoneOpen))
	{
		return ;
	}
	DeActiveAll();
	ActiveAll();
	for (auto& i : _suit_active_skill)
	{
		object_interface oif(_imp);
		_imp->GetSkill().RemoveSkill((int)i.first, (int)i.second, oif);
	}
	_suit_active_skill.clear();
	//就10个元素，没单独列出来的必要了吧
	std::map<int, std::pair<int, int>> suit_count;
	alchemy_runestone_equip_list *equip = GetEquipList();
	if (equip == nullptr)
	{
		return;
	}
	for (int i = 0; i < equip->Size(); ++i)
	{
		if (!equip->IsSlotEmpty(i))
		{
			auto *temp = ((item_template_alchemy_runestone *)(((*equip)[i])->GetTemplate()));
			if (suit_count.find(temp->suit_id) == suit_count.end())
			{
				suit_count[temp->suit_id] = std::pair<int, int>(0, INT_MAX);
			}
			auto iter = suit_count.find(temp->suit_id);
			iter->second.first++;
			if ((((*equip)[i]))->GetAlchemyLevel() < suit_count[temp->suit_id].second)
			{
				auto iter2 = suit_count.find(temp->suit_id);
				iter2->second.second = (((*equip)[i]))->GetAlchemyLevel();
			}
		}
	}
	for (auto& i : suit_count)
	{
		int skill_id = 0;
		switch (i.second.first)
		{
		case 6:
		{
			auto iter = ALCHEMY_RUNESTONE_CONFIG.suit_skill_list.find(i.first);
			if (iter != ALCHEMY_RUNESTONE_CONFIG.suit_skill_list.end() && iter->second.size() >= 3)
			{
				skill_id = iter->second[2];
			}
			//skill_id = ALCHEMY_RUNESTONE_CONFIG.suit_skill_list[i.first][2];
		}
		break;
		case 5:
		case 4:
		{
			auto iter = ALCHEMY_RUNESTONE_CONFIG.suit_skill_list.find(i.first);
			if (iter != ALCHEMY_RUNESTONE_CONFIG.suit_skill_list.end() && iter->second.size() >= 2)
			{
				skill_id = iter->second[1];
			}
			//skill_id = ALCHEMY_RUNESTONE_CONFIG.suit_skill_list[i.first][1];
		}
		break;
		case 3:
		case 2:
		{
			auto iter = ALCHEMY_RUNESTONE_CONFIG.suit_skill_list.find(i.first);
			if (iter != ALCHEMY_RUNESTONE_CONFIG.suit_skill_list.end() && iter->second.size() >= 1)
			{
				skill_id = iter->second[0];
			}
			//skill_id = ALCHEMY_RUNESTONE_CONFIG.suit_skill_list[i.first][0];
		}
		break;
		default:
		{

		}
		break;
		}
		if (skill_id != 0 && i.second.second != INT_MAX)
		{
			int skill_level = (int)i.second.second;
			LOG_TRACE("alchemy : player%ld active skill:%d ,level:%d", _imp->GetParent()->ID.id, skill_id, skill_level);
			ConditionSuppressCombatStat(_imp, SuppressCombatSystem::RUNESTONE)
				.Then(nullptr, "alchemy_runestone_manager::check_and_reset_all: suppress skill insert")
				.Else([skill_id, skill_level](gcreature_imp* imp) {
					object_interface oif(imp);
					imp->GetSkill().InsertSkill(skill_id, skill_level, oif);
				}, "alchemy_runestone_manager::check_and_reset_all: normal skill insert");
			_suit_active_skill[skill_id] = skill_level;
		}
	}
	_imp->GetAchievement().OnAlchemyRuneStonePutIn(_imp);
	//光效检查
	int temp_suit_level = GetHighestLevelofSuitSkill6();
	if (ALCHEMY_RUNESTONE_CONFIG.light_effect_unlock_level[light_effect_choice] > temp_suit_level)
	{
		auto iter = ALCHEMY_RUNESTONE_CONFIG.light_effect_level_unlock.find(temp_suit_level);
		if (iter != ALCHEMY_RUNESTONE_CONFIG.light_effect_level_unlock.end())
		{
			ChangeSuitLight(iter->first, -1);
		}
	}
}

void alchemy_runestone_slot::level_up(gplayer_imp *_imp)
{
	DeActiveProp(_imp);
	_level++;
	_exp = 0;
	ActiveProp(_imp);
	_imp->GetAchievement().OnAlchemySlotLevelUp(_imp, _level);
	PERSONAL_TARGET.TriggerTarget(_imp, TT_DRAGON_COCOON_SLOT_LEVEL_UP);
	BI_LOG_GLOBAL(_imp->GetParent()->account.ToStr());
	SLOG(FORMAT, "alchemy_slot_level_up")
	.BI_HEADER2_GS(_imp)
	.P("slot_id", _id)
	.P("new_level", _level);
}

bool alchemy_runestone_slot::try_add_exp(int exp)
{
	return _level < ALCHEMY_RUNESTONE_CONFIG.slot_need_exp.size() && exp + _exp >= ALCHEMY_RUNESTONE_CONFIG.slot_need_exp[_level - 1];
}

bool alchemy_runestone_slot::add_exp(gplayer_imp *_imp, int exp)
{
	bool ret = false;
	while (exp > 0 && _level < ALCHEMY_RUNESTONE_CONFIG.slot_max_level)
	{
		if (_level < ALCHEMY_RUNESTONE_CONFIG.slot_need_exp.size() && exp + _exp >= ALCHEMY_RUNESTONE_CONFIG.slot_need_exp[_level - 1])
		{
			exp -= ALCHEMY_RUNESTONE_CONFIG.slot_need_exp[_level - 1] - _exp;
			level_up(_imp);
			ret = true;
		}
		else
		{
			_exp += exp;
			break;
		}
	}
	//策划要求满级后剩余经验值归零
	if (_level == ALCHEMY_RUNESTONE_CONFIG.slot_max_level)
	{
		_exp = 0;
	}
	return ret;
}

bool alchemy_runestone_slot::refresh(gplayer_imp *_imp, int item_tid)
{
	auto iter = ALCHEMY_RUNESTONE_CONFIG.slot_addon_level.find(GetStage());
	if (iter != ALCHEMY_RUNESTONE_CONFIG.slot_addon_level.end() && _percent >= iter->second.second)
	{
		_percent = iter->second.second;//真的会大于吗，无论怎么说刷成最大一下
		return false;
	}
	auto iter2 = ALCHEMY_RUNESTONE_CONFIG.slot_refresh_item.find(item_tid);
	if (iter2 == ALCHEMY_RUNESTONE_CONFIG.slot_refresh_item.end())
	{
		return false;
	}
	if (iter2->second < GetStage())
	{
		return false;
	}
	ITEM_CHECK_INFO info;
	if (!_imp->CheckItemExistAtBackpack2(info, item_tid, 1))
	{
		return false;
	}
	int old_percent = _percent;
	int rand_type = -1;
	int rand_percent_min = 0;
	int rand_percent_max = 0;
	int limit_add = 0;

	bool ret = false;
	int rand_num = 0;
	auto iter3 = ALCHEMY_RUNESTONE_CONFIG.slot_refresh_weight_cal.find(GetStage());
	if (iter3 != ALCHEMY_RUNESTONE_CONFIG.slot_refresh_weight_cal.end())
	{
		rand_type = 0;
		int rand_choice = abase::RandSelect(iter3->second.data(), sizeof(float), iter3->second.size());
		rand_percent_min = ALCHEMY_RUNESTONE_CONFIG.slot_refresh_weight[GetStage()][rand_choice][0];
		rand_percent_max = ALCHEMY_RUNESTONE_CONFIG.slot_refresh_weight[GetStage()][rand_choice][1];
		rand_num = abase::Rand(rand_percent_min, rand_percent_max);
		auto iter = ALCHEMY_RUNESTONE_CONFIG.slot_addon_level.find(GetStage());
		if (iter == ALCHEMY_RUNESTONE_CONFIG.slot_addon_level.end())
		{
			return false;
		}
		rand_num = (rand_num * (iter->second.second - GetPercent()) / 10000 ) + GetPercent();
	}
	else
	{
		rand_type = 1;
		rand_percent_min = iter->second.first <= _percent ? _percent + 1 : iter->second.first;
		rand_percent_max = iter->second.second;
		rand_num = abase::Rand(rand_percent_min, rand_percent_max);
	}
	LOG_TRACE("alchemy : player%ld ,refresh slot type %d ,old num%d ,rand_num %d", _imp->GetParent()->ID.id, _active_addon_type, _percent, rand_num);
	if (rand_num > _percent)
	{
		_percent = rand_num;
		ret = true;
	}
	else
	{
		limit_add = 1;
		_percent += 1;//要求最少也得+1
		ret = true;
	}
	_imp->DecItem2(info, {kFuncCodeAlchemyItemCost});
	_imp->GetAchievement().OnUseItem(_imp, info.GetTid(), info.GetCount());
	_imp->GetAchievement().OnAlchemySlotRefresh(_imp, _percent);
	PERSONAL_TARGET.TriggerTarget(_imp, TT_DRAGON_COCOON_REFRESH_SLOT);
	BI_LOG_GLOBAL(_imp->GetParent()->account.ToStr());
	SLOG(FORMAT, "alchemy_slot_Refresh")
	.BI_HEADER2_GS(_imp)
	.P("slot_id", _id)
	.P("old_percent", old_percent)
	.P("new_percent", _percent)
	.P("rand_type", rand_type)
	.P("rand_percent_min", rand_percent_min)
	.P("rand_percent_max", rand_percent_max)
	.P("limit_add", limit_add);
	return ret;
}

void alchemy_runestone_slot::debug_reset(gplayer_imp *_imp)
{
	DeActiveProp(_imp);
	_level = 1;
	_exp = 0;
	auto iter = ALCHEMY_RUNESTONE_CONFIG.slot_addon_level.find(_level);
	if (iter != ALCHEMY_RUNESTONE_CONFIG.slot_addon_level.end())
	{
		_percent = iter->second.first;
	}
}

item_list *alchemy_runestone_manager::GetListByMask(unsigned int package)
{
	switch (package)
	{
	case GNET::IL_ALCHEMY_RUNESTONE_DEPOSITORY:
	{
		return &(_imp->GetInventory().GetAlchemyRunestoneDepository());
	}
	break;
	case GNET::IL_ALCHEMY_RUNESTONE_EQUIP_LIST:
	{
		return &(_imp->GetInventory().GetAlchemyRunestoneEquipList());
	}
	break;
	default:
	{
		return nullptr;
	}
	break;
	}
	return nullptr;
}

alchemy_runestone_equip_list *alchemy_runestone_manager::GetEquipList()
{
	return (alchemy_runestone_equip_list *)GetListByMask(GNET::IL_ALCHEMY_RUNESTONE_EQUIP_LIST);
}

alchemy_runestone_depository *alchemy_runestone_manager::GetDepositoryList()
{
	return (alchemy_runestone_depository *)GetListByMask(GNET::IL_ALCHEMY_RUNESTONE_DEPOSITORY);
}

void alchemy_runestone_manager::MakeSlotInfo(PB::gp_alchemy_re& proto)
{
	PB::alchemy_slot *sproto;
	for ( int i = 0 ; i < _data.size() ; ++i )
	{
		sproto = proto.add_slots();
		sproto->set_level(_data[i].GetLevel());
		sproto->set_exp(_data[i].GetExp());
		sproto->set_place(i);
		sproto->set_percent(_data[i].GetPercent());
		sproto->set_addon_type((PB::ALCHEMY_RUNESTONE_ADDON_TYPE)_data[i].GetAddonType());
	}
}

void alchemy_runestone_manager::MessageHandler(PB::gp_alchemy_c2s proto)
{
	PB::gp_alchemy_re re_proto;
	if (!proto.has_op())
	{
		re_proto.set_ret_code(S2C::ERR_ALCHEMY_OP);
	}
	else
	{
		re_proto.set_op(proto.op());
		LOG_TRACE("alchemy : Get Proto,OP:%d", proto.op());
		if (!GET_FUNC_SWITCH(kFuncCodeAlchemyOpen))
		{
			re_proto.set_ret_code(S2C::ERR_ALCHEMY_KFUNC);
			_imp->Runner()->CommonSend<S2C::CMD::PBS2C>(re_proto);
			return;
		}
		switch (proto.op())
		{
		case PB::ALCHEMY_ACTION_RUNESTONE_GET_EXP_BY_ITEM:
		{
			if (!GET_FUNC_SWITCH(kFuncCodeAlchemyRunestoneOpen))
			{
				re_proto.set_ret_code(S2C::ERR_ALCHEMY_KFUNC);
			}
			if (proto.has_param1() && proto.has_param2() && proto.has_param3())
			{
				ITEM_CHECK_INFO info;
				if (!_imp->CheckItemExistAtBackpack2(info, proto.has_param3(), 1))
				{
					re_proto.set_ret_code(S2C::ERR_ALCHEMY_SUCCESS);
					//UpdateInv();
					_imp->CmdItemInfo(proto.param1(), proto.param2());
					break;
				}
				runestone_add_exp(proto.param3(), proto.param1(), proto.param2());
				_imp->DecItem2(info, {kFuncCodeAlchemyItemCost});
				_imp->GetAchievement().OnUseItem(_imp, info.GetTid(), info.GetCount());
				re_proto.set_ret_code(S2C::ERR_ALCHEMY_SUCCESS);
				_imp->CmdItemInfo(proto.param1(), proto.param2());
				//UpdateInv();
			}
			else
			{
				re_proto.set_ret_code(S2C::ERR_ALCHEMY_PARAM);
			}
			MakeAllActiveInfo("runestone_get_exp_by_item,ALERT:NEVER APPEAR!");
		}
		break;
		case PB::ALCHEMY_ACTION_RUNESTONE_GET_EXP_BY_MIX:
		{
			if (!GET_FUNC_SWITCH(kFuncCodeAlchemyRunestoneOpen))
			{
				re_proto.set_ret_code(S2C::ERR_ALCHEMY_KFUNC);
			}
			if (proto.has_param1() && proto.has_param2() )
			{
				//一开始的设计是可以支持多种背包位置选素材的，但后续需求导致只能是装备中才可以
				if (proto.param1() != (int)GNET::IL_ALCHEMY_RUNESTONE_EQUIP_LIST)
				{
					re_proto.set_ret_code(S2C::ERR_ALCHEMY_PARAM);
					break;
				}
				int extar_exp = 0;
				item_list *list = GetListByMask(proto.param1());
				if (list != nullptr)
				{
					item *item_to = ((*list)[proto.param2()]);
					if (item_to == nullptr)
					{
						re_proto.set_ret_code(S2C::ERR_ALCHEMY_PARAM);
						break;
					}
					else if (!(proto.list1_size() > 0 && proto.list2_size() == proto.list1_size())
					         && !(proto.list3_size() > 0 && proto.list4_size() == proto.list3_size())
					         && !(item_to->GetAlchemyLevel() > 0 && item_to->GetAlchemyLevel() < ALCHEMY_RUNESTONE_CONFIG.alchemy_runestone_max_level && item_to->GetAlchemyExp() == ALCHEMY_RUNESTONE_CONFIG.alchemy_runestone_need_exp[item_to->GetAlchemyLevel()]))
					{
						re_proto.set_ret_code(S2C::ERR_ALCHEMY_PARAM);
						break;
					}
					std::vector<item *> item_from;

					for (int i = 0; i < proto.list1_size(); ++i)
					{
						item_list *list_temp = GetListByMask(proto.list1(i));
						if (list_temp != nullptr)
						{
							if ((*list_temp)[proto.list2(i)] != NULL)
							{
								item *item_temp = ((*list_temp)[proto.list2(i)]);
								if (item_temp->IsSecurityLocked() || item_to->GetAlchemyPlace() != item_temp->GetAlchemyPlace())//其实就也检测了是不是石头了
								{
									re_proto.set_ret_code(S2C::ERR_ALCHEMY_PARAM);
									_imp->Runner()->CommonSend<S2C::CMD::PBS2C>(re_proto);//这么写，感觉不如goto直观
									return;
								}
								item_from.push_back(item_temp);
							}
						}
						else
						{
							re_proto.set_ret_code(S2C::ERR_ALCHEMY_PARAM);
							break;
						}
					}
					int temp_old_suit_level = GetHighestLevelofSuitSkill6();
					item_list *list_depo = GetDepositoryList();
					for (int i = 0; i < proto.list3_size(); ++i)//炼金经验药
					{
						if (proto.list3(i) < 0 || proto.list3(i) >= list_depo->Size())
						{
							re_proto.set_ret_code(S2C::ERR_ALCHEMY_PARAM);
							return;
						}
						item *item_temp = ((*list_depo)[proto.list3(i)]);
						if (item_temp == nullptr)
						{
							re_proto.set_ret_code(S2C::ERR_ALCHEMY_PARAM);
							return;
						}
						if (!item_temp->AlchemyAllowPlace(item_to->GetAlchemyPlace()))//其实相当于检测了是不是经验药了
						{
							re_proto.set_ret_code(S2C::ERR_ALCHEMY_PARAM);
							_imp->Runner()->CommonSend<S2C::CMD::PBS2C>(re_proto);
							return;
						}
						if (item_temp->GetCount() >= proto.list4(i))
						{
							extar_exp += item_temp->GetAlchemyExpItemAddExp() * proto.list4(i);
						}
						else
						{
							re_proto.set_ret_code(S2C::ERR_ALCHEMY_PARAM);
							_imp->Runner()->CommonSend<S2C::CMD::PBS2C>(re_proto);
							return;
						}
					}
					switch (runestone_add_exp(item_from, item_to, extar_exp))
					{
					case S2C::ERR_ALCHEMY_SUCCESS:
					{
						for (int i = 0; i < proto.list1_size(); ++i)
						{
							item_list *list_temp = GetListByMask(proto.list1(i));
							if (list_temp != nullptr)
							{
								list_temp->Delete(proto.list2(i));
								_imp->CmdItemInfo(proto.list1(i), proto.list2(i));
							}
						}
						item_list *list_temp = GetDepositoryList();
						for (int i = 0; i < proto.list3_size(); ++i)
						{
							item *item_temp = ((*list_depo)[proto.list3(i)]);
							if (item_temp != nullptr)
							{
								int count = item_temp->GetCount() - proto.list4(i);
								if (count)
								{
									item_temp->SetCount(count);
								}
								else
								{
									list_temp->Delete(proto.list3(i));
								}
								_imp->CmdItemInfo((int)GNET::IL_ALCHEMY_RUNESTONE_DEPOSITORY, proto.list3(i));
							}
						}
						re_proto.set_ret_code(S2C::ERR_ALCHEMY_SUCCESS);
						//UpdateInv();
						int now_suit_level = GetHighestLevelofSuitSkill6();
						if (temp_old_suit_level != now_suit_level)
						{
							ChangeSuitLight(now_suit_level, -1);
						}
						item_to->OnAlchemyChanged(_imp, MakeRunestoneFightPoint((item_alchemy_runestone *)item_to));
						_imp->CmdItemInfo(proto.param1(), proto.param2());
					}
					break;
					default:
					{
						re_proto.set_ret_code(S2C::ERR_ALCHEMY_CANNOT_MIX);
					}
					break;
					}
				}
				else
				{
					re_proto.set_ret_code(S2C::ERR_ALCHEMY_PARAM);
					break;
				}
			}
			else
			{
				re_proto.set_ret_code(S2C::ERR_ALCHEMY_PARAM);
				break;
			}
			MakeAllActiveInfo("runestone_get_exp_by_mix");
		}
		break;
		case PB::ALCHEMY_ACTION_RUNESTONE_LIMIT_BREAK:
		{
			if (!GET_FUNC_SWITCH(kFuncCodeAlchemyRunestoneOpen))
			{
				re_proto.set_ret_code(S2C::ERR_ALCHEMY_KFUNC);
			}
			if (proto.has_param1() && proto.has_param2())
			{
				item_list *list = GetListByMask(proto.param1());
				if (list != nullptr)
				{
					item *pitem = ((*list)[proto.param2()]);
					runestone_limit_break(pitem);
					re_proto.set_ret_code(S2C::ERR_ALCHEMY_SUCCESS);
					_imp->CmdItemInfo(proto.param1(), proto.param2());
				}
				else
				{
					re_proto.set_ret_code(S2C::ERR_ALCHEMY_PARAM);
					break;
				}
			}
			else
			{
				re_proto.set_ret_code(S2C::ERR_ALCHEMY_PARAM);
			}
			MakeAllActiveInfo("runestone_limit_break");
		}
		break;
		case PB::ALCHEMY_ACTION_SLOT_SWAP:
		{
			if (!GET_FUNC_SWITCH(kFuncCodeAlchemySlotOpen))
			{
				re_proto.set_ret_code(S2C::ERR_ALCHEMY_KFUNC);
			}
			if (proto.has_param1() && proto.has_param2())
			{
				swap_slot(proto.param1(), proto.param2());
				re_proto.set_ret_code(S2C::ERR_ALCHEMY_SUCCESS);//正常是不会越界的，越界的话虽然返回成功但实际上也没有变化
				MakeSlotInfo(re_proto);
				_imp->CmdItemInfo(GNET::IL_ALCHEMY_RUNESTONE_EQUIP_LIST, proto.param1());
				_imp->CmdItemInfo(GNET::IL_ALCHEMY_RUNESTONE_EQUIP_LIST, proto.param2());
				//UpdateInv();
			}
			else
			{
				re_proto.set_ret_code(S2C::ERR_ALCHEMY_PARAM);
			}
			MakeAllActiveInfo("slot_swap,ALERT: NEVER APPEAR!");
		}
		break;
		case PB::ALCHEMY_ACTION_SLOT_GET_EXP:
		{

			if (proto.has_param1() && proto.param1() >= 0 && proto.param1() < _data.size()/*&& proto.has_param2() && proto.has_param3()*/)
			{
				alchemy_runestone_slot& slot = GetSlotInfo(proto.param1());
				std::vector<ITEM_CHECK_INFO> item_info;
				auto iter = ALCHEMY_RUNESTONE_CONFIG.slot_stage_up_cost_item_and_count.find(slot.GetLevel());
				if (iter != ALCHEMY_RUNESTONE_CONFIG.slot_stage_up_cost_item_and_count.end())
				{
					//使用特殊升级(进阶)
					for (auto& i : iter->second)
					{
						ITEM_CHECK_INFO info;
						if (!_imp->CheckItemExistAtBackpack2(info, i.first, i.second))
						{
							re_proto.set_ret_code(S2C::ERR_ALCHEMY_NO_ITEM);
							_imp->Runner()->CommonSend<S2C::CMD::PBS2C>(re_proto);
							return ;
						}
						item_info.push_back(info);
					}
				}
				//使用普通升级
				for (auto& i : ALCHEMY_RUNESTONE_CONFIG.slot_level_up_cost_item_and_count_build)
				{
					for (auto& j : i.second)
					{
						if (slot.GetLevel() <= j.first)
						{
							ITEM_CHECK_INFO info;
							if (!_imp->CheckItemExistAtBackpack2(info, i.first, alchemy_calc_function::CalcInt(j.second, slot.GetLevel())))
							{
								re_proto.set_ret_code(S2C::ERR_ALCHEMY_NO_ITEM);
								_imp->Runner()->CommonSend<S2C::CMD::PBS2C>(re_proto);
								return ;
							}
							item_info.push_back(info);
							break;
						}
					}
				}

				if (item_info.empty())
				{
					re_proto.set_ret_code(S2C::ERR_ALCHEMY_PARAM);
					_imp->Runner()->CommonSend<S2C::CMD::PBS2C>(re_proto);
					return;
				}

				slot.level_up(_imp);
				for (auto& i : item_info)
				{
					_imp->DecItem2(i, {kFuncCodeAlchemyItemCost});
					_imp->GetAchievement().OnUseItem(_imp, i.GetTid(), i.GetCount());
				}
				//slot_add_exp(proto.param2(), proto.param1(), proto.param3());
				re_proto.set_ret_code(S2C::ERR_ALCHEMY_SUCCESS);
				MakeSlotInfo(re_proto);
			}
			else
			{
				re_proto.set_ret_code(S2C::ERR_ALCHEMY_PARAM);
			}
			MakeAllActiveInfo("SLOT_GET_EXP");
		}
		break;
		case PB::ALCHEMY_ACTION_SLOT_REFRESH:
		{

			if (proto.has_param1() && proto.has_param2())
			{
				slot_refresh(proto.param2(), proto.param1());
				re_proto.set_ret_code(S2C::ERR_ALCHEMY_SUCCESS);
				MakeSlotInfo(re_proto);
			}
			else
			{
				re_proto.set_ret_code(S2C::ERR_ALCHEMY_PARAM);
			}
			MakeAllActiveInfo("SLOT_REFRESH");
		}
		break;
		case PB::ALCHEMY_ACTION_SLOT_INIT:
		{
			re_proto.set_ret_code(S2C::ERR_ALCHEMY_SUCCESS);
			MakeSlotInfo(re_proto);
			//MakeAllActiveInfo("SLOT_INIT");
		}
		break;
		default:
		{
			re_proto.set_ret_code(S2C::ERR_ALCHEMY_OP);
		}
		break;
		}
	}

	_imp->Runner()->CommonSend<S2C::CMD::PBS2C>(re_proto);
}
//以及不存在的话，初始化
void alchemy_runestone_manager::Load(const PB::player_universal_data_t& pud)
{
	has_Load = true;
	if (!pud.has_alchemy_data()) //初始化
	{
		light_effect_choice = 0;
		for (int i = 0; i < ALCHEMY_RUNESTONE_EQUIP_LIST_BASE_SIZE; ++i)
		{
			auto iter = ALCHEMY_RUNESTONE_CONFIG.slot_addon_level.find(1);
			if (iter == ALCHEMY_RUNESTONE_CONFIG.slot_addon_level.end())
			{
				continue;
			}
			_data.push_back(alchemy_runestone_slot(i, 1, 0, iter->second.first, ALCHEMY_RUNESTONE_ADDON_TYPE(i + 1)));
		}
	}
	else
	{
		PB::alchemy_other_data proto = pud.alchemy_data();
		if (proto.has_light_effect_select())
		{
			light_effect_choice = proto.light_effect_select();
		}
		else
		{
			light_effect_choice = 0;
		}
		_data.resize(ALCHEMY_RUNESTONE_EQUIP_LIST_BASE_SIZE);
		for (int i = 0; i < proto.slots_size(); ++i)
		{
			if (!proto.slots(i).has_level() || !proto.slots(i).has_exp() || !proto.slots(i).has_percent() || !proto.slots(i).has_addon_type() || !proto.slots(i).has_place())
			{
				//数据丢失了？
				LOG_TRACE("alchemy : onlogin : pud数据损失 %ld", _imp->GetParent()->ID.id);
				auto iter = ALCHEMY_RUNESTONE_CONFIG.slot_addon_level.find(1);
				if (iter == ALCHEMY_RUNESTONE_CONFIG.slot_addon_level.end())
				{
					continue;
				}
				_data[i] = alchemy_runestone_slot(i, 0, 0, iter->second.first, ALCHEMY_RUNESTONE_ADDON_TYPE(i + 1));
			}
			_data[proto.slots(i).place()] = alchemy_runestone_slot(i,
			                                proto.slots(i).level(),
			                                proto.slots(i).exp(),
			                                proto.slots(i).percent(),
			                                (ALCHEMY_RUNESTONE_ADDON_TYPE)proto.slots(i).addon_type());
		}
	}
}
void alchemy_runestone_manager::Save(PB::player_universal_data_t& pud)
{
	PB::alchemy_other_data proto;
	proto.set_light_effect_select(light_effect_choice);
	for (int i = 0; i < _data.size(); ++i)
	{
		PB::alchemy_slot *iter = proto.add_slots();
		iter->set_level(_data[i].GetLevel());
		iter->set_exp(_data[i].GetExp());
		iter->set_place(i);
		iter->set_percent(_data[i].GetPercent());
		iter->set_addon_type((PB::ALCHEMY_RUNESTONE_ADDON_TYPE)_data[i].GetAddonType());
	}
	pud.mutable_alchemy_data()->CopyFrom(proto);
	//OnLogout();
}

void alchemy_runestone_manager::OnLeaveScene()
{
	MakeAllActiveInfo("on Leavescene");
	DeactiveSlots();
	DeActiveAll();
	//临时性撤销套装技能
	auto& prop = _imp->GetSkill();
	for (auto& i : _suit_active_skill)
	{
		object_interface oif(_imp);
		prop.RemoveSkill((int)i.first, (int)i.second, oif);
	}
}

void alchemy_runestone_manager::OnEnterScene()
{
	PB::gp_alchemy_re re_proto;
	re_proto.set_op(PB::ALCHEMY_ACTION_SLOT_INIT);
	MakeSlotInfo(re_proto);
	re_proto.set_ret_code(S2C::ERR_ALCHEMY_SUCCESS);
	_imp->Runner()->CommonSend<S2C::CMD::PBS2C>(re_proto);

	LOG_TRACE("alchemy : player %ld login.", _imp->GetParent()->ID.id);

	//激活套装技能
	check_and_reset_all();
	//槽位初始化 2c
	ChangeSuitLightByEffectID(light_effect_choice);
	//同步一直存在
	MakeAllActiveInfo("on Enterscene");
}

int alchemy_runestone_manager::GetHighestLevelofSuitSkill6()
{
	int max = -1;
	for (auto& i : _suit_active_skill)
	{
		if (ALCHEMY_RUNESTONE_CONFIG.suit_skill_6.find(i.first) != ALCHEMY_RUNESTONE_CONFIG.suit_skill_6.end() && max < i.second)
		{
			max = i.second;
		}
	}
	return max;
}

void alchemy_runestone_manager::ActiveAll()
{
	if (!GET_FUNC_SWITCH(kFuncCodeAlchemyRunestoneOpen))
	{
		return;
	}
	if (is_active)
	{
		return;
	}
	is_active = true;
	LOG_TRACE("alchemy : active all.");
	alchemy_runestone_equip_list *equip = GetEquipList();
	if (equip == nullptr)
	{
		return ;
	}
	for (int i = 0; i < equip->Size(); ++i)
	{
		if (!equip->IsSlotEmpty(i))
		{
			((*equip)[i])->OnAlchemyActivate(_imp);
			((*equip)[i])->SetIsInAlchemyEquip(true);
		}
	}
}
void alchemy_runestone_manager::DeActiveAll()
{
	if (!GET_FUNC_SWITCH(kFuncCodeAlchemyOpen))
	{
		return;
	}
	if (!is_active)
	{
		return;
	}
	is_active = false;
	LOG_TRACE("alchemy : deactive all.");
	alchemy_runestone_equip_list *equip = GetEquipList();
	if (equip == nullptr)
	{
		return ;
	}
	for (int i = 0; i < equip->Size(); ++i)
	{
		if (!equip->IsSlotEmpty(i))
		{
			((*equip)[i])->OnAlchemyDeactivate(_imp);
		}
	}
}

alchemy_runestone_slot& alchemy_runestone_manager::GetSlotInfo(int index)//无检测，必须外部检测
{
	return _data[index];
}

void alchemy_runestone_manager::debug_level_up(int level)
{
	alchemy_runestone_equip_list *equip = GetEquipList();
	if (equip == nullptr)
	{
		return ;
	}
	DeActiveAll();
	for (int i = 0; i < equip->Size(); ++i)
	{
		if (!equip->IsSlotEmpty(i))
		{
			((*equip)[i])->debug_level_up(_imp, level);
			_imp->CmdItemInfo(GNET::IL_ALCHEMY_RUNESTONE_EQUIP_LIST, i);
		}
	}
	check_and_reset_all();
}

void alchemy_runestone_manager::ChangeSuitLight(int level, int effect)
{
	auto iter = ALCHEMY_RUNESTONE_CONFIG.light_effect_level_unlock.find(level);
	if (iter != ALCHEMY_RUNESTONE_CONFIG.light_effect_level_unlock.end() && iter->first <= _imp->GetAlchemyRunestone().GetHighestLevelofSuitSkill6())
	{
		int temp_effect = 0;
		if (effect == -1)
		{
			temp_effect = *(iter->second.begin());
		}
		else
		{
			auto iter2 = iter->second.find(effect);
			if (iter2 != iter->second.end())
			{
				temp_effect = effect;
			}
			else
			{
				return ;//
			}
		}
		//if (_imp->GetAlchemyRunestone().light_effect_choice == temp_effect)
		//{
		//	//return ;//重复的就直接返回吧 //还是不返回了
		//}
		_imp->GetAlchemyRunestone().light_effect_choice = temp_effect;
		_imp->GetParent()->alchemy_suit_effect = _imp->GetAlchemyRunestone().light_effect_choice;
		PB::gp_alchemy_on_effect_change protoc;
		protoc.set_effect_id(_imp->GetAlchemyRunestone().light_effect_choice);
		protoc.set_role_id(_imp->Parent()->ID.id);
		_imp->Runner()->RegionSend<S2C::CMD::PBS2C>(protoc);
		if (_imp->GetAlchemyRunestone().light_effect_choice != 0)
		{
			_imp->GetParent()->SetObjectState2(gobject::STATE2_ALCHEMY_SUIT_EFFECT);
		}
		else
		{
			_imp->GetParent()->ClrObjectState2(gobject::STATE2_ALCHEMY_SUIT_EFFECT);
		}
	}
}

void alchemy_runestone_manager::ChangeSuitLightByEffectID(int effect)
{
	auto iter = ALCHEMY_RUNESTONE_CONFIG.light_effect_unlock_level.find(effect);
	if (iter != ALCHEMY_RUNESTONE_CONFIG.light_effect_unlock_level.end())
	{
		ChangeSuitLight(iter->second, effect);
	}
}

bool alchemy_runestone_manager::CheckHasAnySuit(int count)
{
	if (_suit_active_skill.size() == 0)
	{
		return false;
	}
	for (auto& i : _suit_active_skill)
	{
		for (auto& j : ALCHEMY_RUNESTONE_CONFIG.suit_skill_list)
		{
			int index = (count + 1) / 2 - 1;
			if (index < 0 || index >= j.second.size())
			{
				LOG_TRACE("alchemy : error index in CheckHasAnySuit, index:%d ,count:%d", index, count);
				continue;
			}
			if (i.first == j.second[index] && i.second > 0)
			{
				return true;
			}
		}
	}
	return false;
}

bool alchemy_runestone_manager::CheckHasSuitBySkill(int skill_id)
{
	for (auto& i : _suit_active_skill)
	{
		if (i.first == skill_id )
		{
			return true;
		}
	}
	return false;
}

bool alchemy_runestone_manager::CheckHasAnySuitBySkillLevel(int count, int level)
{
	int pcount = 0;
	switch (count)
	{
	case 2:
	{
		pcount = 0;
	}
	break;
	case 4:
	{
		pcount = 1;
	}
	break;
	case 6:
	{
		pcount = 2;
	}
	break;
	default:
	{
		return false;
	}
	break;
	}
	for (auto& i : ALCHEMY_RUNESTONE_CONFIG.suit_skill_list)
	{
		auto iter = _suit_active_skill.find(i.second[pcount]);
		if (iter != _suit_active_skill.end() && iter->second >= level)
		{
			return true;
		}
	}
	return false;
}

bool alchemy_runestone_manager::CheckSlotVar(int var, int count)
{
	int sum = 0;
	for (auto& i : _data)
	{
		if (i.GetPercent() >= var)
		{
			++sum;
		}
	}
	if (sum >= count)
	{
		return true;
	}
	return false;
}

bool alchemy_runestone_manager::CheckCanSwapItem(item *it1, item *it2, unsigned char location1, unsigned char location2, item_list& inv1, item_list& inv2, unsigned short index1, unsigned short index2)
{
	if (!GET_FUNC_SWITCH(kFuncCodeAlchemyOpen))
	{
		return false;
	}
	std::map<int, std::map<int, int>> suit_check;
	item_list *temp_list = location1 == GNET::IL_ALCHEMY_RUNESTONE_EQUIP_LIST ?
	                       &inv1 : &inv2;
	for (int i = 0; i < temp_list->Size(); ++i)
	{
		if ((*temp_list)[i] != NULL && i != (location1 == GNET::IL_ALCHEMY_RUNESTONE_EQUIP_LIST ?
		                                     location1 : location2))
		{
			suit_check[(*temp_list)[i]->GetAlchemySuitID()][(*temp_list)[i]->GetAlchemyPlace()]++;
			if (suit_check[(*temp_list)[i]->GetAlchemySuitID()][(*temp_list)[i]->GetAlchemyPlace()] > 1)
			{
				return false;
				//虽然不可能发生
			}
		}
	}
	item *temp1 = location1 == GNET::IL_ALCHEMY_RUNESTONE_EQUIP_LIST ?
	              it1 : it2;
	item *temp2 = location1 == GNET::IL_ALCHEMY_RUNESTONE_EQUIP_LIST ?
	              it2 : it1;
	if ((temp1 == nullptr || temp1->GetTemplate()->GetTemplateType() != ITT_ALCHEMY_RUNESTONE)
	        && (temp2 == nullptr || temp2->GetTemplate()->GetTemplateType() != ITT_ALCHEMY_RUNESTONE) )
	{
		//因为引入了纯经验道具，需要多筛一次是否符文石
		return false;
	}
	if (temp1 != NULL)
	{
		suit_check[temp1->GetAlchemySuitID()][temp1->GetAlchemyPlace()]--;
	}
	if (temp2 != NULL)
	{
		suit_check[temp2->GetAlchemySuitID()][temp2->GetAlchemyPlace()]++;
		auto iter1 = suit_check.find(temp2->GetAlchemySuitID());
		if (iter1 != suit_check.end())
		{
			auto iter2 = iter1->second.find(temp2->GetAlchemyPlace());
			if (iter2 != iter1->second.end() && iter2->second > 1)
			{
				return false;
			}
		}
		unsigned short equip_index = location1 == GNET::IL_ALCHEMY_RUNESTONE_EQUIP_LIST ?
		                             index1 : index2;
		auto iter = ALCHEMY_RUNESTONE_CONFIG.alchemy_slot_limit.find(equip_index);
		if (iter != ALCHEMY_RUNESTONE_CONFIG.alchemy_slot_limit.end())//没配置的槽位是自由位
		{
			auto iter2 = iter->second.find(temp2->GetAlchemyPlace());
			if (iter2 == iter->second.end())
			{
				LOG_TRACE("alchemy : 不符合槽位限制的交换.place:%d ,index:%d", temp2->GetAlchemyPlace(), equip_index);
				return false;
			}
		}
	}

	if (temp1 != NULL)
	{
		temp1->OnAlchemyDeactivate(_imp);
	}
	return true;
}

void alchemy_runestone_manager::MakeComplicatedData(gplayer_imp *imp, PB::role_complicated_data_alchemy& complicated_data)
{
	complicated_data.set_total_fight_point(MakeFightPoint());
	PB::gp_alchemy_re proto;
	MakeSlotInfo(proto);
	complicated_data.mutable_slots()->CopyFrom(proto.slots());
	auto equip = GetEquipList();
	if (equip != nullptr)
	{
		for (int i = 0; i < equip->Size(); ++i)
		{
			if (!equip->IsSlotEmpty(i))
			{
				auto *temp = ((item_alchemy_runestone *)((*equip)[i]));
				auto add_iter = complicated_data.add_stones();
				PB::alchemy_runestone_essence_t temp_pb;
				temp->GetAlchemyEss(temp_pb);
				add_iter->mutable_stone()->CopyFrom(temp_pb);
				add_iter->set_pos(i);
				add_iter->set_id(temp->GetTemplate()->_tid);
			}
		}
	}
}

int64_t alchemy_runestone_manager::MakeFightPoint()
{
	int64_t sum = 0;
	//基础属性
	runestone_fight_point.resize(10);
	slot_fight_point.resize(10);
	mix_fight_point.resize(10);
	for (int i = 0; i < _data.size(); ++i)
	{
		for (auto& j : _data[i].active_type)
		{
			int temp = InnerCalcPropFightPoint(j, _data[i].param3[j], _data[i].param4[j]);
			sum += temp;
			mix_fight_point[i] += temp;
		}
		auto iter = ALCHEMY_RUNESTONE_CONFIG.slot_base_addon_group_build.find( _data[i].GetId());
		if (iter != ALCHEMY_RUNESTONE_CONFIG.slot_base_addon_group_build.end())
		{
			for (auto& j : iter->second)
			{
				for (auto& k : j.second)
				{
					if (_data[i].GetLevel() <= k.first)
					{
						int temp = InnerCalcPropFightPoint((ALCHEMY_RUNESTONE_ADDON_TYPE)j.first, alchemy_calc_function::CalcInt(k.second, _data[i].GetLevel()), alchemy_calc_function::CalcFloat(k.second, _data[i].GetLevel()));
						sum += temp;
						mix_fight_point[i] += temp;
						slot_fight_point[i] += temp;
						break;
					}
				}
			}
		}
	}
	//套装技能
	for (auto& i : _suit_active_skill)
	{
		sum += SkillConfig::GetInstance().GetSkillCapacity(i.first, i.second, true);
	}
	auto equip = GetEquipList();
	if (equip != nullptr)
	{
		for (int i = 0; i < equip->Size(); ++i)
		{
			if (!equip->IsSlotEmpty(i))
			{
				auto *temp = ((item_alchemy_runestone *)((*equip)[i]));
				PB::alchemy_runestone_essence_t proto;
				temp->GetAlchemyEss(proto);
				//单独战力计算用基础属性
				auto iter = ALCHEMY_RUNESTONE_CONFIG.alchemy_runestone_base_addon_group.find(temp->GetAlchemyPlace());
				if (iter != ALCHEMY_RUNESTONE_CONFIG.alchemy_runestone_base_addon_group.end())
				{
					std::vector<std::pair<ALCHEMY_RUNESTONE_ADDON_TYPE, int> > base_prop_list;
					for (auto& x : iter->second)
					{
						base_prop_list.push_back(std::pair<ALCHEMY_RUNESTONE_ADDON_TYPE, int>((ALCHEMY_RUNESTONE_ADDON_TYPE)x[1], x[0]));
					}
					for (auto& z : base_prop_list)
					{
						auto var = ALCHEMY_RUNESTONE_CONFIG.alchemy_runestone_per_level[z.first][z.second][proto.level()];
						runestone_fight_point[i] +=  InnerCalcPropFightPoint(z.first, var, var);
					}
				}
				//词条属性
				for (int j = 0; j < proto.prop_size(); ++j)
				{
					int temp = InnerCalcPropFightPoint((ALCHEMY_RUNESTONE_ADDON_TYPE)proto.prop(j).type(), proto.prop(j).value(), proto.prop(j).value());
					sum += temp;
					runestone_fight_point[i] += temp;
					mix_fight_point[i] += temp;
				}
				//词条技能 不存在
				//for (int i = 0; i < proto.passive_skill_2_size(); ++i)
				//{
				//	sum += SkillConfig::GetInstance().GetSkillCapacity(proto.passive_skill_2(i).id(), proto.passive_skill_2(i).level(), true);
				//}
			}
		}
	}
	_imp->GetAchievement().OnCommonFightingCapacityUpdate(_imp, 5, sum);
	PB::gp_alchemy_re re_proto;
	re_proto.set_op(PB::ALCHEMY_ACTION_ON_FIGHTPOINT_CHANGE);
	re_proto.set_ret_code(S2C::ERR_ALCHEMY_SUCCESS);
	re_proto.set_param1(sum);
	_imp->Runner()->CommonSend<S2C::CMD::PBS2C>(re_proto);
	return sum;
}

int alchemy_runestone_manager::MakeRunestoneFightPoint(item_alchemy_runestone *runestone)
{
	if (runestone == nullptr)
	{
		return 0;
	}
	int sum = 0;
	PB::alchemy_runestone_essence_t proto;
	runestone->GetAlchemyEss(proto);
	//单独战力计算用基础属性
	auto iter = ALCHEMY_RUNESTONE_CONFIG.alchemy_runestone_base_addon_group.find(runestone->GetAlchemyPlace());
	if (iter != ALCHEMY_RUNESTONE_CONFIG.alchemy_runestone_base_addon_group.end())
	{
		std::vector<std::pair<ALCHEMY_RUNESTONE_ADDON_TYPE, int> > base_prop_list;
		for (auto& x : iter->second)
		{
			base_prop_list.push_back(std::pair<ALCHEMY_RUNESTONE_ADDON_TYPE, int>((ALCHEMY_RUNESTONE_ADDON_TYPE)x[1], x[0]));
		}
		for (auto& z : base_prop_list)
		{
			auto var = ALCHEMY_RUNESTONE_CONFIG.alchemy_runestone_per_level[z.first][z.second][proto.level()];
			sum +=  InnerCalcPropFightPoint(z.first, var, var);
		}
	}
	//词条属性
	for (int j = 0; j < proto.prop_size(); ++j)
	{
		int temp = InnerCalcPropFightPoint((ALCHEMY_RUNESTONE_ADDON_TYPE)proto.prop(j).type(), proto.prop(j).value(), proto.prop(j).value());
		sum += temp;
	}
	return sum;
}

int64_t alchemy_runestone_manager::InnerCalcPropFightPoint(ALCHEMY_RUNESTONE_ADDON_TYPE type, int64_t param1, double param2)
{
	switch (type)
	{
	case ALCHEMY_ADDON_NONE:
		return 0;
	case ALCHEMY_ADDON_ATK			:
	case ALCHEMY_ADDON_DEF			:
	case ALCHEMY_ADDON_HP			:
	case ALCHEMY_ADDON_CRIT			:
	case ALCHEMY_ADDON_MUL_ATK		:
	case ALCHEMY_ADDON_CRIT_DEF		:
	case ALCHEMY_ADDON_PIERCE		:
	case ALCHEMY_ADDON_CDR			:
	case ALCHEMY_ADDON_PIERCE_DEF	:
	case ALCHEMY_ADDON_MDEF			:
	{
		//int64
		auto iter = ALCHEMY_RUNESTONE_CONFIG.fight_point_calc_ratio.find(type);
		if (iter == ALCHEMY_RUNESTONE_CONFIG.fight_point_calc_ratio.end())
		{
			return 0;
		}
		return iter->second * param1;
	}
	break;
	case ALCHEMY_ADDON_CRIT_RATIO:
	{
		//double
		auto iter = ALCHEMY_RUNESTONE_CONFIG.fight_point_calc_ratio.find(type);
		if (iter == ALCHEMY_RUNESTONE_CONFIG.fight_point_calc_ratio.end())
		{
			return 0;
		}
		return iter->second * param2;
	}
	break;
	default:
	{
		return 0;
	}
	break;
	}
	return 0;
}

void alchemy_runestone_manager::debug_reset_slot()         //仅清空槽
{
	for (auto& i : _data)
	{
		i.debug_reset(_imp);
	}
	PB::gp_alchemy_re re_proto;
	re_proto.set_op(PB::ALCHEMY_ACTION_SLOT_INIT);
	MakeSlotInfo(re_proto);
	re_proto.set_ret_code(S2C::ERR_ALCHEMY_SUCCESS);
	_imp->Runner()->CommonSend<S2C::CMD::PBS2C>(re_proto);
	check_and_reset_all();
}

void alchemy_runestone_manager::debug_reset_alchemy_runestone(bool only_depo) //仅清空符文石
{
	if (!only_depo)
	{
		auto equip = GetEquipList();
		if (equip != nullptr)
		{
			for (int i = 0; i < equip->Size(); ++i)
			{
				if (!equip->IsSlotEmpty(i))
				{
					auto *temp = ((item_alchemy_runestone *)((*equip)[i]));
					temp->OnAlchemyDeactivate(_imp);
				}
			}
			equip->Clear();
			check_and_reset_all();
		}
	}
	auto depo = GetDepositoryList();
	if (depo != nullptr)
	{
		depo->Clear();
	}
}

void alchemy_runestone_manager::debug_reset_all()          //清空所有
{
	debug_reset_slot();
	debug_reset_alchemy_runestone(false);
}

void alchemy_runestone_slot::ActiveProp(gplayer_imp *_imp)
{
	if (is_prop_active)
	{
		return ;
	}
	is_prop_active = true;
	auto iter = ALCHEMY_RUNESTONE_CONFIG.slot_base_addon_group_build.find(_id);
	if (iter != ALCHEMY_RUNESTONE_CONFIG.slot_base_addon_group_build.end())
	{
		for (auto& i : iter->second)
		{
			for (auto& j : i.second)
			{
				if (GetLevel() <= j.first)
				{
					ALCHEMY_ADDON.SlotActive(_imp, (ALCHEMY_RUNESTONE_ADDON_TYPE)i.first, alchemy_calc_function::CalcInt(j.second, GetLevel()));
					break;
				}
			}
		}
	}
}

void alchemy_runestone_slot::DeActiveProp(gplayer_imp *_imp)
{
	if (!is_prop_active)
	{
		return ;
	}
	is_prop_active = false;
	auto iter = ALCHEMY_RUNESTONE_CONFIG.slot_base_addon_group_build.find(_id);
	if (iter != ALCHEMY_RUNESTONE_CONFIG.slot_base_addon_group_build.end())
	{
		for (auto& i : iter->second)
		{
			for (auto& j : i.second)
			{
				if (GetLevel() <= j.first)
				{
					ALCHEMY_ADDON.SlotDeActive(_imp, (ALCHEMY_RUNESTONE_ADDON_TYPE)i.first, alchemy_calc_function::CalcInt(j.second, GetLevel()));
					break;
				}
			}
		}
	}
}

void alchemy_runestone_manager::ActiveSlots()
{
	if (!GET_FUNC_SWITCH(kFuncCodeAlchemySlotOpen))
	{
		return;
	}
	if (is_slots_active)
	{
		return ;
	}
	is_slots_active = true;
	for (auto& i : _data)
	{
		i.ActiveProp(_imp);
	}
}

void alchemy_runestone_manager::DeactiveSlots()
{
	if (!GET_FUNC_SWITCH(kFuncCodeAlchemySlotOpen))
	{
		return;
	}
	if (!is_slots_active)
	{
		return ;
	}
	is_slots_active = false;
	for (auto& i : _data)
	{
		i.DeActiveProp(_imp);
	}
}

void alchemy_runestone_manager::debug_level_up_slots(int level, int place)
{
	if (place != -1)
	{
		auto iter = GetSlotInfo(place);
		while (iter.GetLevel() < level)
		{
			iter.level_up(_imp);
		}
	}
	else
	{
		for (auto& i : _data)
		{
			while (i.GetLevel() < level)
			{
				i.level_up(_imp);
			}
		}
	}
	PB::gp_alchemy_re re_proto;
	re_proto.set_ret_code(S2C::ERR_ALCHEMY_SUCCESS);
	MakeSlotInfo(re_proto);
	_imp->Runner()->CommonSend<S2C::CMD::PBS2C>(re_proto);
}

void alchemy_runestone_manager::MakeAllActiveInfo(std::string act)
{
	alchemy_runestone_equip_list *list = GetEquipList();
	if (list == nullptr)
	{
		return ;
	}
	auto GetRunestone = [&list](int place)->item*
	{
		return ((*list)[place]);
	};
	item *temp_list[ALCHEMY_RUNESTONE_EQUIP_LIST_BASE_SIZE];
	for (int i = 0; i < ALCHEMY_RUNESTONE_EQUIP_LIST_BASE_SIZE; ++i)
	{
		if (list->IsSlotEmpty(i))
		{
			temp_list[i] = nullptr;
		}
		else
		{
			temp_list[i] = GetRunestone(i);
		}
	}
	std::string skills;
	for (auto& i : _suit_active_skill)
	{
		skills += ";" + std::to_string(i.first) + ":" + std::to_string(i.second) + ";";
	}
	BI_LOG_GLOBAL(_imp->GetParent()->account.ToStr());
	SLOG(FORMAT, "alchemy_make_all_active_info")
	.BI_HEADER2_GS(_imp)
	.P("act", act.c_str())
	.P("ss", skills.c_str())
	.P("fp", (int)MakeFightPoint())
#ifndef ALCHEMY_TO_STRING
#define ALCHEMY_TO_STRING(p) #p
#endif
#ifndef MAKE_RUNESTONE_PROP
#define MAKE_RUNESTONE_PROP(place1,place2) \
	.P(ALCHEMY_TO_STRING(r##place1##p##place2##t),temp_list[place1] != nullptr ? temp_list[place1]->GetAlchemyPropTypeAt(place2) : 0) \
	.P(ALCHEMY_TO_STRING(r##place1##p##place2##v),temp_list[place1] != nullptr ? temp_list[place1]->GetAlchemyPropValueAt(place2) : 0)
#endif
#ifndef MAKE_RUNESTONE_INFO
#define MAKE_RUNESTONE_INFO(place) \
	.P(ALCHEMY_TO_STRING(r##place##ID),temp_list[place] != nullptr ? temp_list[place]->GetTemplate()->_tid : 0) \
	.P(ALCHEMY_TO_STRING(r##place##Lv),temp_list[place] != nullptr ? temp_list[place]->GetAlchemyLevel() : 0) \
	.P(ALCHEMY_TO_STRING(r##place##Exp),temp_list[place] != nullptr ? temp_list[place]->GetAlchemyExp() : 0) \
	.P(ALCHEMY_TO_STRING(r##place##bt),temp_list[place] != nullptr ? temp_list[place]->GetAlchemyBreakTimes() : 0) \
	.P(ALCHEMY_TO_STRING(r##place##prop),temp_list[place] != nullptr ? temp_list[place]->GetAlchemyPropInfo().c_str() : "")
	//MAKE_RUNESTONE_PROP(place,0) \
	//MAKE_RUNESTONE_PROP(place,1) \
	//MAKE_RUNESTONE_PROP(place,2) \
	//MAKE_RUNESTONE_PROP(place,3) \
	//MAKE_RUNESTONE_PROP(place,4)
#endif
	MAKE_RUNESTONE_INFO(0)
	MAKE_RUNESTONE_INFO(1)
	MAKE_RUNESTONE_INFO(2)
	MAKE_RUNESTONE_INFO(3)
	MAKE_RUNESTONE_INFO(4)
	MAKE_RUNESTONE_INFO(5)
	MAKE_RUNESTONE_INFO(6)
	MAKE_RUNESTONE_INFO(7)
	MAKE_RUNESTONE_INFO(8)
	MAKE_RUNESTONE_INFO(9)
#undef MAKE_RUNESTONE_INFO
#undef MAKE_RUNESTONE_PROP
#ifndef MAKE_SLOT_INFO
#define MAKE_SLOT_INFO(place) \
	.P(ALCHEMY_TO_STRING(s##place##lv),GetSlotInfo(place).GetLevel()) \
	.P(ALCHEMY_TO_STRING(s##place##p),GetSlotInfo(place).GetPercent())
#endif
	MAKE_SLOT_INFO(0)
	MAKE_SLOT_INFO(1)
	MAKE_SLOT_INFO(2)
	MAKE_SLOT_INFO(3)
	MAKE_SLOT_INFO(4)
	MAKE_SLOT_INFO(5)
	MAKE_SLOT_INFO(6)
	MAKE_SLOT_INFO(7)
	MAKE_SLOT_INFO(8)
	MAKE_SLOT_INFO(9);
#undef MAKE_SLOT_INFO
#undef ALCHEMY_TO_STRING
}

void alchemy_runestone_manager::MakeRoleTradeRunestoneInfo(gplayer_imp *_imp, PB::role_trade_runestone_info& r_info)
{
	if (!_imp)
	{
		return;
	}
	r_info.set_score(_imp->GetPlayerMisc().fight_cap_cache().runestone_fc_value());
	//装备中的
	alchemy_runestone_equip_list *list = GetEquipList();
	if (list)
	{
		list->ForEachItems([&r_info](item_list *, unsigned int index, item * it)
		{
			if (it)
			{
				item_alchemy_runestone *p_item_stone = dynamic_cast<item_alchemy_runestone *>(it);
				if (!p_item_stone)
				{
					return;
				}
				auto *p_add_one = r_info.add_cur_stones();
				p_add_one->set_id(p_item_stone->GetTID());
				p_add_one->set_lv(p_item_stone->GetAlchemyLevel());
				p_add_one->set_place(index);
				p_add_one->set_count(1);
			}
		});
	}
	//仓库
	auto *d_list = GetDepositoryList();
	if (d_list)
	{
		std::map<int, std::pair<int, int>> runs_map = {};
		d_list->ForEachItems([&runs_map](item_list *, unsigned int, item * it)
		{
			if (it)
			{
				item_alchemy_runestone *p_item_stone = dynamic_cast<item_alchemy_runestone *>(it);
				if (!p_item_stone)
				{
					return;
				}
				if (runs_map.find(p_item_stone->GetTID()) == runs_map.end())
				{
					runs_map[p_item_stone->GetTID()].first = p_item_stone->GetAlchemyPlace();
					runs_map[p_item_stone->GetTID()].second = p_item_stone->GetCount();
				}
				else
				{
					runs_map[p_item_stone->GetTID()].second += p_item_stone->GetCount();
				}
			}
		});
		for (auto& kv : runs_map)
		{
			auto *p_add_one = r_info.add_depository_stones();
			p_add_one->set_id(kv.first);
			p_add_one->set_lv(1);
			p_add_one->set_place(kv.second.first);
			p_add_one->set_count(kv.second.second);
		}
	}
	//槽位
	for ( int i = 0 ; i < _data.size() ; ++i )
	{
		auto *p_add_slot = r_info.add_slots();
		p_add_slot->set_place(i);
		p_add_slot->set_lv(_data[i].GetLevel());
		p_add_slot->set_percent(_data[i].GetPercent());
	}
}

/*================================================================
*   文件名称：player_holy_ghost.h
*   创 建 者：zhangfan
*   创建日期：2024年01月02日
*   描    述：英灵系统
================================================================*/


#include "holy_ghost_cfg_manager.h"
#include "player_holy_ghost.h"

#include "funcswitchmgr.h"
#include "types.h"
#include "creature.h"
#include "player.h"
#include "addon_manager.h"
#include "slog.h"
#include "protocol_s2c.h"
#include "player_star_ghost.h"
#include "player_misc.h"

#include "gprotoc/player_universal_data_t.pb.h"
#include "gprotoc/gp_holy_ghost_req.pb.h"
#include "gprotoc/gp_holy_ghost_rsp.pb.h"
#include "gprotoc/role_trade_holy_ghost_info.pb.h"

class creature_enhance_if;
class addon_manager;

#define EXIST_COUNT(entry)   std::get<0>(entry)
#define COMBINE_COUNT(entry) std::get<1>(entry)
#define RETURN(retcode, reason) \
do { \
       	logger.P("retcode", retcode).P("reason", reason); \
	return retcode; \
} while (false)

//自动补足检查及消耗
static int AutoFillCheckAndDec(gplayer_imp *pImp, PB::gp_holy_ghost_req& cmd, std::map<int, int>& cost_item_map, GNET::LogMessage& logger)
{
	//1. 检查是否需要补足并记录补足数量
	std::map<int, std::tuple<int, int>> exist_combine_map; //最终解锁 tid->(已有道具数量, 需要补足道具数量)
	std::map<int, ITEM_CHECK_INFO> item_check_infos;
	for (auto& kv : cost_item_map)
	{
		auto& ec_one = exist_combine_map[kv.first];
		ITEM_CHECK_INFO item_check_info;
		pImp->CheckItemExistAtBackpack2(item_check_info, kv.first, kv.second, false);
		if (item_check_info.GetTotalCount() < kv.second)
		{
			EXIST_COUNT(ec_one) = item_check_info.GetTotalCount();
			COMBINE_COUNT(ec_one) = kv.second - item_check_info.GetTotalCount();
		}
		else
		{
			EXIST_COUNT(ec_one) = kv.second;
		}
		item_check_infos.insert(std::make_pair(kv.first, item_check_info));
	}
	bool fill_flag = false;
	for (auto& kv : exist_combine_map)
	{
		if (COMBINE_COUNT(kv.second) > 0)
		{
			fill_flag = true;
			break;
		}
	}
	if (!fill_flag) // never
	{
		RETURN(S2C::ERR_FATAL_ERR, "无需补足");
	}

	//2. 整理客户端发上来的材料列表
	int op_id = cmd.op_type() == PB::HGO_UPGRADE_WORLD_TREE_POINT ?  cmd.point_id() : cmd.holy_ghost_id();
	std::map<int, PB::gp_item_combine_advance> gp_items; //客户端发上来的材料列表map
	for (auto& ic : cmd.item_combines())
	{
		gp_items.insert(std::make_pair(ic.target(), ic));
	}

	//3. 检查补足
	FuncInfo fi{kFuncCodeHolyGhost, cmd.op_type(), op_id};
	struct CheckInfo
	{
		std::map<tid_t, int> exist_material;
		std::map<tid_t, int> need_material;
		unsigned int cost_material = 0U;
		int money_cost_material = 0;
		int extra_id_material = 0;
		int exist_extra_material = 0;
	};
	std::map<int, CheckInfo> check_infos;
	for (auto& kv : exist_combine_map)
	{
		auto& ec_one = kv.second;
		// 合并检查 并 扣除
		if (COMBINE_COUNT(ec_one) <= 0)
		{
			continue;
		}
		CheckInfo c_info;
		auto gi_iter = gp_items.find(kv.first);
		if (gi_iter == gp_items.end())
		{
			RETURN(S2C::ERR_FATAL_ERR, "客户端未发补足列表");
		}

		PB::gp_item_combine_advance combine_info = gi_iter->second;
		combine_info.mutable_slot()->set_location(GNET::IL_EQUIPMENT_ENHANCE);
		std::stringstream ss;
		int ret = pImp->CheckCombineItemAdvance(combine_info,
		                                        item_manager::GetInstance().GetItemActType(kv.first),
		                                        COMBINE_COUNT(ec_one),
		                                        c_info.exist_material, c_info.need_material,
		                                        c_info.cost_material, c_info.money_cost_material,
		                                        c_info.extra_id_material, c_info.exist_extra_material,
		                                        fi, ss);
		logger.P("material_combine_info", std::string("(") + ss.str() + std::string(")"));
		if (ret != 0)
		{
			logger.P("combine_material_errcode", ret);
			RETURN(S2C::ERR_FATAL_ERR, "客户端补足列表错误");
		}
		check_infos.insert(std::make_pair(kv.first, c_info));
	}

	//4. 扣除用于补足的道具/货币 + 已经存在的道具
	for (auto& kv : exist_combine_map)
	{
		auto& ec_one = kv.second;
		auto c_iter = check_infos.find(kv.first);
		if (COMBINE_COUNT(ec_one) > 0)
		{
			if (c_iter == check_infos.end())
			{
				RETURN(S2C::ERR_FATAL_ERR, "未知错误");
			}
			auto& c_info = c_iter->second;
			PB::gp_item_combine_advance combine_info = gp_items[kv.first];
			combine_info.mutable_slot()->set_location(GNET::IL_EQUIPMENT_ENHANCE);
			// 前边检查通过 这里一定成功
			pImp->DecCombineItemAdvance(combine_info, COMBINE_COUNT(ec_one),
			                            c_info.exist_material, c_info.need_material,
			                            c_info.cost_material, c_info.money_cost_material,
			                            c_info.extra_id_material, c_info.exist_extra_material,
			                            fi);
		}
		//扣除本来存在的道具
		if (EXIST_COUNT(ec_one) > 0)
		{
			pImp->DecItem2(item_check_infos[kv.first], fi);
		}
	}
	RETURN(0, "");
}

#undef RETURN
#undef COMBINE_COUNT
#undef EXIST_COUNT

void holy_ghost::__UpdateProp(gplayer_imp *pImp)
{
	if (!pImp)
	{
		return;
	}

	creature_enhance_if cef(pImp);
	ConditionSuppressCombatStat(pImp, SuppressCombatSystem::HOLY_GHOST)
		.Then(nullptr, "holy_ghost::__UpdateProp: suppress prop dec")
		.Else([&](gcreature_imp* imp) {
			cef.GetProperty().DecByStruct(_prop);
		}, "holy_ghost::__UpdateProp: normal prop dec");
	memset(&_prop, 0, sizeof(_prop));
	auto p_hg_cfg = HOLY_GHOST_CONFIG::get(_db_data.id());
	if (!p_hg_cfg)
	{
		return;
	}
	int enhance_config_id = p_hg_cfg->part_enhance;
	if (enhance_config_id <= 0)
	{
		return;
	}
	auto p_enhance_config = ENHANCE_CONFIG::get(enhance_config_id);
	int config_idx = _db_data.lv() - 1;
	if (!p_enhance_config || config_idx < 0 || config_idx >= p_enhance_config->configs.size())
	{
		return;
	}
	float ratio = p_enhance_config->configs[config_idx].basic_prop_addition_percent * 0.01f;
	PlayerBasePropEnhance(EBRT_HERO, p_hg_cfg->attri_id, &_prop, ratio);
	ConditionSuppressCombatStat(pImp, SuppressCombatSystem::HOLY_GHOST)
		.Then(nullptr, "holy_ghost::__UpdateProp: suppress prop inc")
		.Else([&](gcreature_imp* imp) {
			cef.GetProperty().IncByStruct(_prop);
		}, "holy_ghost::__UpdateProp: normal prop inc");
}

void holy_ghost::UpgradeLv(gplayer_imp *pImp, PB::gp_holy_ghost_req& cmd)
{
	if (!pImp || !_p_manager)
	{
		return;
	}

	int tid = cmd.holy_ghost_id();
	int old_level = cmd.holy_ghost_old_lv();
	int new_level = cmd.holy_ghost_new_lv();

	SLOG(INFO, "holy_ghost::UpgradeLv")
	.P("roleid", pImp->GetRoleID())
	.PS(tid)
	.PS(old_level)
	.PS(new_level);

	auto p_hg_cfg = HOLY_GHOST_CONFIG::get(tid);
	if (!p_hg_cfg)
	{
		return;
	}
	int enhance_config_id = p_hg_cfg->part_enhance;
	if (enhance_config_id <= 0)
	{
		return;
	}
	auto p_enhance_config = ENHANCE_CONFIG::get(enhance_config_id);
	if (!p_enhance_config)
	{
		return;
	}
	int break_lv = _db_data.break_lv();
	if (break_lv < 0 || break_lv >= p_hg_cfg->break_phase.size())
	{
		return;
	}
	int lv_limit = p_hg_cfg->break_phase[break_lv].limit;
	//判断等级是否合法
	if (old_level < 0 || new_level - old_level <= 0 || new_level - old_level > 10
	        || old_level >= p_enhance_config->configs.size()
	        || new_level >= p_enhance_config->configs.size()
	        || old_level != _db_data.lv() || new_level > lv_limit)
	{
		_p_manager->SendResponse(pImp, cmd, 1);
		return;
	}
	//收集需要扣除的道具
	std::map<int, int> cost_item_map;
	if (!holy_ghost_cfg_manager::GetInstance().GetUpgradeItemMap(p_enhance_config, old_level, new_level, cost_item_map))
	{
		return;
	}
	//扣除
	std::stringstream ss_cost;
	if (!cmd.auto_fill())
	{
		std::vector<ITEM_CHECK_INFO> item_check_infos;
		for (auto& kv : cost_item_map)
		{
			ITEM_CHECK_INFO item_check_info;
			if (!pImp->CheckItemExistAtBackpack2(item_check_info, kv.first, kv.second, false))
			{
				_p_manager->SendResponse(pImp, cmd, 2);
				return;
			}
			item_check_infos.push_back(item_check_info);
		}

		FuncInfo func_info{kFuncCodeHolyGhost, cmd.op_type(), tid};
		for (auto& item_check_info : item_check_infos)
		{
			pImp->DecItem2(item_check_info, func_info);
			ss_cost << "(" << item_check_info.GetTid() << "," << item_check_info.GetCount() << "),";
		}
	}
	else
	{
		//自动补足
		GNET::LogMessage logger(LOG_DEBUG, "holy_ghost::UpgradeLv::AutoFillCheckAndDec");
		int retcode = AutoFillCheckAndDec(pImp, cmd, cost_item_map, logger);
		if (retcode != 0)
		{
			_p_manager->SendResponse(pImp, cmd, 2);
			return;
		}
	}

	//升级
	_db_data.set_lv(new_level);

	//喊话
	//if (p_enhance_config->configs[new_level].speak_id)
	//{
	//gplayer_imp::SPEAK_PARAM_MAP map;
	//raw_wrapper& h1 = map[SYS_SPEAK::ENHANCE_PART];
	//SYS_SPEAK::MakeTypeInt(h1, SYS_SPEAK::ENHANCE_PART, cmd.enhance_part() + pImp->GetProf() * 100);
	//raw_wrapper& h2 = map[SYS_SPEAK::ENHANCE_LEVEL];
	//SYS_SPEAK::MakeTypeInt(h2, SYS_SPEAK::ENHANCE_LEVEL, level);

	//pImp->SystemSpeak2(config.speak_id, &map);
	//}

	//出战中的英灵 且 此次升级解锁了天赋, 需要将天赋层数加上
	std::set<int> unlock_talent_stages = {};
	if (_p_manager->IsHolyGhostCombat(tid) && HolyGhostTalentConfigManager::GetInstance().GetUnlockStage(old_level, new_level, unlock_talent_stages) && break_lv)
	{
		auto& talent_phase = p_hg_cfg->enhance_phase;
		for (int i = 0; i <= break_lv; i++)
		{
			if (i < talent_phase.size()
			        && talent_phase[i].level && talent_phase[i].phase
			        && unlock_talent_stages.find(talent_phase[i].phase) != unlock_talent_stages.end())
			{
				_talent.StageAddLv(talent_phase[i].phase, talent_phase[i].level);
			}
		}
	}

	//更新属性
	__UpdateProp(pImp);

	pImp->GetAchievement().OnHolyGhostLevelUp(pImp);

	//通知客户端
	_p_manager->SendResponse(pImp, cmd, 0, tid);

	BI_LOG_GLOBAL(pImp->GetParent()->account.ToStr());
	SLOG(FORMAT, "holy_ghost_upgrade_lv")
	.BI_HEADER2_GS(pImp)
	.PS(tid)
	.PS(old_level)
	.PS(new_level)
	.P("cost_items", ss_cost.str());
}

void holy_ghost::BreakLv(gplayer_imp *pImp, PB::gp_holy_ghost_req& cmd)
{
	if (!pImp || !_p_manager)
	{
		return;
	}

	int tid = cmd.holy_ghost_id();
	int old_level = cmd.holy_ghost_old_lv();
	int new_level = cmd.holy_ghost_new_lv();

	SLOG(INFO, "holy_ghost::BreakLv")
	.P("roleid", pImp->GetRoleID())
	.PS(tid)
	.PS(old_level)
	.PS(new_level);

	auto p_hg_cfg = HOLY_GHOST_CONFIG::get(tid);
	if (!p_hg_cfg)
	{
		return;
	}
	auto& break_phases = p_hg_cfg->break_phase;
	if (break_phases.empty())
	{
		return;
	}
	//判断等级是否合法
	if (old_level < 0 || new_level - old_level <= 0 || new_level - old_level > 1
	        || old_level >= break_phases.size()
	        || new_level >= break_phases.size()
	        || old_level != _db_data.break_lv())
	{
		_p_manager->SendResponse(pImp, cmd, 1);
		return;
	}
	//收集需要扣除的道具
	ITEM_CHECK_INFO item_check_info;
	if (!pImp->CheckItemExistAtBackpack2(item_check_info, break_phases[old_level].item_id, break_phases[old_level].item_count, false))
	{
		_p_manager->SendResponse(pImp, cmd, 2);
		return;
	}

	//扣除
	FuncInfo func_info{kFuncCodeHolyGhost, cmd.op_type(), tid};
	pImp->DecItem2(item_check_info, func_info);

	//升级
	_db_data.set_break_lv(new_level);

	//出战中的英灵
	if (_p_manager->IsHolyGhostCombat(tid))
	{
		//修改主动技能等级
		ConditionSuppressCombatStat(pImp, SuppressCombatSystem::HOLY_GHOST)
			.Then(nullptr, "holy_ghost::UpgradeLv: suppress skill remove/insert")
			.Else([&](gcreature_imp* imp) {
				if (break_phases[old_level].skill_lv != break_phases[new_level].skill_lv)
				{
					object_interface oif(imp);
					imp->GetSkill().RemoveSkill(p_hg_cfg->main_skill, 1, oif);
					if (break_phases[new_level].skill_lv > 0)
					{
						imp->GetSkill().InsertSkill(p_hg_cfg->main_skill, break_phases[new_level].skill_lv, oif);
					}
				}
			}, "holy_ghost::UpgradeLv: normal skill remove/insert");
		//天赋
		auto& talent_phase = p_hg_cfg->enhance_phase;
		if (new_level > 0 && new_level < talent_phase.size()
		        && talent_phase[new_level].level && talent_phase[new_level].phase
		        && HolyGhostTalentConfigManager::GetInstance().GetStageLevel(talent_phase[new_level].phase) <= _db_data.lv()) //强化等级已达到
		{
			_talent.StageAddLv(talent_phase[new_level].phase, talent_phase[new_level].level);
		}
	}

	//通知客户端
	_p_manager->SendResponse(pImp, cmd, 0, tid);

	std::stringstream ss_cost;
	ss_cost << "(" << item_check_info.GetTid() << "," << item_check_info.GetCount() << "),";
	BI_LOG_GLOBAL(pImp->GetParent()->account.ToStr());
	SLOG(FORMAT, "holy_ghost_break_lv")
	.BI_HEADER2_GS(pImp)
	.PS(tid)
	.PS(old_level)
	.PS(new_level)
	.P("cost_items", ss_cost.str());
}

void holy_ghost::SendGift(gplayer_imp *pImp, PB::gp_holy_ghost_req& cmd)
{
	if (!pImp || !_p_manager)
	{
		return;
	}

	int tid = cmd.holy_ghost_id();
	int gift_id = cmd.gift_id();
	int old_amity = _db_data.amity();

	SLOG(INFO, "holy_ghost::SendGift")
	.P("roleid", pImp->GetRoleID())
	.PS(tid)
	.PS(gift_id)
	.PS(old_amity);

	auto p_gift = HERO_GIFT_CFG::get(gift_id);
	if (!p_gift)
	{
		return;
	}
	auto p_hg_cfg = HOLY_GHOST_CONFIG::get(tid);
	if (!p_hg_cfg)
	{
		return;
	}

	auto& gift_map = _p_manager->GetGiftMap();
	auto iter = gift_map.find(gift_id);
	if (iter == gift_map.end() || iter->second < 1)
	{
		_p_manager->SendResponse(pImp, cmd, 1);
		return;
	}

	//扣除
	int gift_left = iter->second - 1;
	if (gift_left <= 0)
	{
		gift_map.erase(iter);
	}
	else
	{
		iter->second = gift_left;
	}

	//升级
	_db_data.set_amity(old_amity + p_gift->value);

	//好感度解锁被动
	int new_amity = _db_data.amity();
	int new_skill_id = holy_ghost_cfg_manager::GetInstance().GetSkillIdByAmity(new_amity, p_hg_cfg->favorability_lv_list, p_hg_cfg->prop_lv_list);
	if (new_skill_id != _active_skill_id)
	{
		auto pef = creature_enhance_if(pImp);
		if (_active_skill_id)
		{
			//被动附加属性组失效
			ConditionSuppressCombatStat(pImp, SuppressCombatSystem::HOLY_GHOST)
				.Then(nullptr, "holy_ghost::SendGift: suppress addon deactivate")
				.Else([&](gcreature_imp* imp) {
					addon_manager::GetInstance().DeactivateGroup(pef, _active_skill_id);
				}, "holy_ghost::SendGift: normal addon deactivate");
		}
		if (new_skill_id)
		{
			//被动附加属性组生效
			ConditionSuppressCombatStat(pImp, SuppressCombatSystem::HOLY_GHOST)
				.Then(nullptr, "holy_ghost::SendGift: suppress addon activate")
				.Else([&](gcreature_imp* imp) {
					addon_manager::GetInstance().ActivateGroup(pef, new_skill_id);
				}, "holy_ghost::SendGift: normal addon activate");
			_active_skill_id = new_skill_id;
		}
	}

	//通知客户端
	_p_manager->SendResponse(pImp, cmd, 0, tid);

	pImp->GetAchievement().OnHolyGhostAddAmity(pImp);

	int old_lv = holy_ghost_cfg_manager::GetInstance().AmityToLv(old_amity);
	int new_lv = holy_ghost_cfg_manager::GetInstance().AmityToLv(new_amity);
	BI_LOG_GLOBAL(pImp->GetParent()->account.ToStr());
	SLOG(FORMAT, "holy_ghost_send_gift")
	.BI_HEADER2_GS(pImp)
	.PS(tid)
	.PS(gift_id)
	.PS(old_amity)
	.P("new_amity", _db_data.amity())
	.PS(old_lv)
	.PS(new_lv)
	.PS(gift_left);
}

int holy_ghost::GetTalentAddLv(int talent_stage)
{
	int break_lv = _db_data.break_lv();
	int tid = _db_data.id();
	auto p_hg_cfg = HOLY_GHOST_CONFIG::get(tid);
	if (!p_hg_cfg)
	{
		return 0;
	}
	int lv = 0;
	for (int i = 0; i < p_hg_cfg->enhance_phase.size() && i <= break_lv; i++)
	{
		if (p_hg_cfg->enhance_phase[i].phase == talent_stage)
		{
			lv += p_hg_cfg->enhance_phase[i].level;
		}
	}
	return lv;
}

player_holy_ghost_manager::~player_holy_ghost_manager()
{
	for (auto& kv : _hg_map)
	{
		if (kv.second)
		{
			delete kv.second;
		}
	}
	_hg_map.clear();
}

void player_holy_ghost_manager::Load(gplayer_imp *pImp, PB::player_universal_data_t& pud)
{
	if (pud.has_holy_ghost_data())
	{
		_db_data.CopyFrom(pud.holy_ghost_data());
	}
	//构造所有holy_ghost
	for (int i = 0; i < _db_data.holy_ghosts().size(); i++)
	{
		_hg_map[_db_data.holy_ghosts(i).id()] = new holy_ghost(pImp, this, _db_data.mutable_holy_ghosts(i));
	}
	for (int i = 0; i < _db_data.gifts().size(); i++)
	{
		_gift_map[_db_data.gifts(i).gift_id()] = _db_data.gifts(i).gift_count();
	}
	//清一下, 避免占内存
	_db_data.clear_holy_ghosts();
	_db_data.clear_gifts();
}

void player_holy_ghost_manager::Save(gcreature_imp *imp, PB::player_universal_data_t& pud)
{
	//先清
	_db_data.clear_holy_ghosts();
	_db_data.clear_gifts();
	//重新序列化回去
	for (auto& kv : _hg_map)
	{
		if (!kv.second)
		{
			break;
		}
		auto *p_add = _db_data.add_holy_ghosts();
		p_add->CopyFrom(kv.second->GetDBData());
	}
	for (auto& kv : _gift_map)
	{
		auto *p_add = _db_data.add_gifts();
		p_add->set_gift_id(kv.first);
		p_add->set_gift_count(kv.second);
	}

	pud.mutable_holy_ghost_data()->CopyFrom(_db_data);
}

void player_holy_ghost_manager::OnLogin(gplayer_imp *pImp)
{
	if (!GET_FUNC_SWITCH(kFuncCodeHolyGhost))
	{
		return;
	}
	if (_is_actived)
	{
		SyncAllData(pImp);
		return;
	}
	//生效世界树属性
	__UpdateWorldTreePointProp(pImp);
	//生效强化属性 + 英灵被动
	__ActiveAllPropAndPassive(pImp);
	//生效出战英灵的天赋 及 主动技能
	for (auto& hg_id : _db_data.combat_ids())
	{
		if (hg_id == 0)
		{
			continue;
		}
		auto *p_hg = __GetHolyGhost(hg_id);
		if (!p_hg)
		{
			continue;
		}
		auto p_hg_cfg = HOLY_GHOST_CONFIG::get(hg_id);
		int break_idx = p_hg->GetDBData().break_lv();
		if (!p_hg_cfg || break_idx < 0 || break_idx >= p_hg_cfg->break_phase.size())
		{
			continue;
		}
		if (p_hg_cfg->break_phase[break_idx].skill_lv)
		{
			ConditionSuppressCombatStat(pImp, SuppressCombatSystem::HOLY_GHOST)
				.Then(nullptr, "player_holy_ghost_manager::OnLogin: suppress skill insert")
				.Else([&](gcreature_imp* imp) {
					object_interface oif(imp);
					imp->GetSkill().InsertSkill(p_hg_cfg->main_skill, p_hg_cfg->break_phase[break_idx].skill_lv, oif);
				}, "player_holy_ghost_manager::OnLogin: normal skill insert");
		}
		p_hg->_talent.ActiveAll();
	}
	//生效通用附加属性组
	if (HaveCombatHolyGhost())
	{
		__ActiveCommonAddGroup(pImp);
	}
	//同步所有数据
	SyncAllData(pImp);
	_is_actived = true;
}

void player_holy_ghost_manager::HandleRequest(gplayer_imp *pImp, PB::gp_holy_ghost_req& cmd)
{
	if (!pImp || !GET_FUNC_SWITCH(kFuncCodeHolyGhost))
	{
		return;
	}

	switch (cmd.op_type())
	{
	//所有数据
	case (PB::HGO_ALL):
	{

	}
	break;
	//世界树节点升级
	case (PB::HGO_UPGRADE_WORLD_TREE_POINT):
	{
		UpgradeWorldTreePoint(pImp, cmd);
	}
	break;
	//世界树高度奖励领取
	case (PB::HGO_GET_WORLD_TREE_REWARD):
	{
		GetWorldTreeHeightReward(pImp, cmd);
	}
	break;
	//英灵强化升级
	case (PB::HGO_UPGRADE_LV):
	{
		auto *p_hg = __GetHolyGhost(cmd.holy_ghost_id());
		if (p_hg)
		{
			p_hg->UpgradeLv(pImp, cmd);
		}
	}
	break;
	//英灵突破升级
	case (PB::HGO_UPGRADE_BREAK_LV):
	{
		auto *p_hg = __GetHolyGhost(cmd.holy_ghost_id());
		if (p_hg)
		{
			p_hg->BreakLv(pImp, cmd);
		}
	}
	break;
	//英灵设置出战
	case (PB::HGO_SET_COMBAT):
	{
		SetHolyGhostCombat(pImp, cmd);
	}
	break;
	//英灵送礼
	case (PB::HGO_SEND_GIFT):
	{
		auto *p_hg = __GetHolyGhost(cmd.holy_ghost_id());
		if (p_hg)
		{
			p_hg->SendGift(pImp, cmd);
		}
	}
	break;
	default:
	{
		auto *p_hg = __GetHolyGhost(cmd.holy_ghost_id());
		if (p_hg)
		{
			p_hg->_talent.HandleRequest(cmd);
		}
	}
	break;
	}
}

void player_holy_ghost_manager::SyncAllData(gplayer_imp *pImp)
{
	PB::gp_holy_ghost_rsp msg;
	msg.set_op_type(PB::HGO_ALL);
	msg.mutable_hg_data()->CopyFrom(_db_data);
	msg.mutable_hg_data()->clear_holy_ghosts();
	for (auto& kv : _hg_map)
	{
		if (kv.second)
		{
			auto *p_add = msg.mutable_hg_data()->add_holy_ghosts();
			p_add->CopyFrom(kv.second->GetDBData());
		}
	}
	for (auto& kv : _gift_map)
	{
		auto *p_add = msg.mutable_hg_data()->add_gifts();
		p_add->set_gift_id(kv.first);
		p_add->set_gift_count(kv.second);
	}
	pImp->Runner()->QuietSend<S2C::CMD::PBS2C>(msg);
}

void player_holy_ghost_manager::SendResponse(gplayer_imp *pImp, PB::HOLY_GHOST_OP_TYPE op_type, int retcode, int tid)
{
	PB::gp_holy_ghost_rsp msg;
	msg.set_op_type(op_type);
	if (retcode)
	{
		msg.set_retcode(retcode);
		pImp->Runner()->QuietSend<S2C::CMD::PBS2C>(msg);
		return;
	}

	holy_ghost *p_hg = nullptr;
	if (tid)
	{
		p_hg = __GetHolyGhost(tid);
	}

	//成功则获取最新数据返回
	switch (op_type)
	{
	//所有数据
	case (PB::HGO_ALL):
	{

	}
	break;
	//世界树节点升级
	case (PB::HGO_UPGRADE_WORLD_TREE_POINT):
	//世界树高度奖励领取
	case (PB::HGO_GET_WORLD_TREE_REWARD):
	{
		msg.mutable_hg_data()->mutable_world_tree()->CopyFrom(_db_data.world_tree());
	}
	break;
	//英灵解锁
	case (PB::HGO_UNLOCK):
	//英灵强化升级
	case (PB::HGO_UPGRADE_LV):
	//英灵突破升级
	case (PB::HGO_UPGRADE_BREAK_LV):
	//英灵天赋升级
	case (PB::HGO_TALENT_ADD_POINT):
	//英灵清空天赋
	case (PB::HGO_TALENT_CLEAR):
	{
		if (p_hg)
		{
			auto *p_add = msg.mutable_hg_data()->add_holy_ghosts();
			p_add->CopyFrom(p_hg->GetDBData());
		}
	}
	break;
	//英灵送礼
	case (PB::HGO_SEND_GIFT):
	{
		if (p_hg)
		{
			auto *p_add = msg.mutable_hg_data()->add_holy_ghosts();
			p_add->CopyFrom(p_hg->GetDBData());
		}
		for (auto& kv : _gift_map)
		{
			auto *p_add = msg.mutable_hg_data()->add_gifts();
			p_add->set_gift_id(kv.first);
			p_add->set_gift_count(kv.second);
		}
	}
	break;
	//英灵设置出战
	case (PB::HGO_SET_COMBAT):
	{
		msg.mutable_hg_data()->mutable_combat_ids()->CopyFrom(_db_data.combat_ids());
	}
	break;
	//英灵礼物增加
	case (PB::HGO_ADD_GIFT):
	{
		//for (auto& kv : _gift_map)
		//{
		//	auto *p_add = msg.mutable_hg_data()->add_gifts();
		//	p_add->set_gift_id(kv.first);
		//	p_add->set_gift_count(kv.second);
		//}
		auto *p_add = msg.mutable_hg_data()->add_gifts();
		p_add->set_gift_id(_last_add_gift_id);
		p_add->set_gift_count(_last_add_gift_count);
	}
	break;
	default:
		break;
	}
	msg.set_retcode(retcode);
	pImp->Runner()->QuietSend<S2C::CMD::PBS2C>(msg);
}

void player_holy_ghost_manager::SendResponse(gplayer_imp *pImp, PB::gp_holy_ghost_req& cmd, int retcode, int tid)
{
	SendResponse(pImp, cmd.op_type(), retcode, tid);
}

void player_holy_ghost_manager::__UpdateWorldTreePointProp(gplayer_imp *pImp)
{
	creature_enhance_if cef(pImp);
	ConditionSuppressCombatStat(pImp, SuppressCombatSystem::HOLY_GHOST)
		.Then(nullptr, "player_holy_ghost_manager::__UpdateWorldTreePointProp: suppress prop dec")
		.Else([&](gcreature_imp* imp) {
			cef.GetProperty().DecByStruct(_world_tree_prop);
		}, "player_holy_ghost_manager::__UpdateWorldTreePointProp: normal prop dec");
	memset(&_world_tree_prop, 0, sizeof(_world_tree_prop));
	for (int i = 0; i < _db_data.world_tree().point_lvs().size(); i++)
	{
		int point_id = i + 1;
		int point_lv = _db_data.world_tree().point_lvs(i);
		if (point_id <= 0 || point_lv <= 0)
		{
			continue;
		}
		auto *p_cfg = holy_ghost_cfg_manager::GetInstance().GetWorldTreePointCfg(point_id);
		if (!p_cfg)
		{
			continue;
		}
		int enhance_config_id = p_cfg->enhance_cfg_id;
		if (enhance_config_id <= 0)
		{
			continue;
		}
		auto p_enhance_config = ENHANCE_CONFIG::get(enhance_config_id);
		int config_idx = point_lv - 1;
		if (!p_enhance_config || config_idx < 0 || config_idx >= p_enhance_config->configs.size())
		{
			continue;
		}
		float ratio = p_enhance_config->configs[config_idx].basic_prop_addition_percent * 0.01f;
		PlayerBasePropEnhance(EBRT_HERO, p_cfg->enhance_base_id, &_world_tree_prop, ratio);
	}
	ConditionSuppressCombatStat(pImp, SuppressCombatSystem::HOLY_GHOST)
		.Then(nullptr, "player_holy_ghost_manager::__UpdateWorldTreePointProp: suppress prop inc")
		.Else([&](gcreature_imp* imp) {
			cef.GetProperty().IncByStruct(_world_tree_prop);
		}, "player_holy_ghost_manager::__UpdateWorldTreePointProp: normal prop inc");
}

//生效所有属性 + 已解锁的被动
void player_holy_ghost_manager::__ActiveAllPropAndPassive(gplayer_imp *pImp)
{
	auto pef = creature_enhance_if(pImp);
	for (auto& kv : _hg_map)
	{
		auto p_hg_cfg = HOLY_GHOST_CONFIG::get(kv.first);
		if (!kv.second || !p_hg_cfg)
		{
			continue;
		}
		kv.second->__UpdateProp(pImp);
		int amity = kv.second->GetDBData().amity();
		int skill_id = holy_ghost_cfg_manager::GetInstance().GetSkillIdByAmity(amity, p_hg_cfg->favorability_lv_list, p_hg_cfg->prop_lv_list);
		if (skill_id)
		{
			ConditionSuppressCombatStat(pImp, SuppressCombatSystem::HOLY_GHOST)
				.Then(nullptr, "player_holy_ghost_manager::__ActiveAllPropAndPassive: suppress addon activate")
				.Else([&](gcreature_imp* imp) {
					addon_manager::GetInstance().ActivateGroup(pef, skill_id);
				}, "player_holy_ghost_manager::__ActiveAllPropAndPassive: normal addon activate");
			kv.second->SetActiveSkillId(skill_id);
		}
	}
}

//生效通用附加属性组
void player_holy_ghost_manager::__ActiveCommonAddGroup(gplayer_imp *pImp)
{
	int common_add = holy_ghost_cfg_manager::GetInstance().GetCommonAddGroupId();
	if (common_add)
	{
		auto pef = creature_enhance_if(pImp);
		ConditionSuppressCombatStat(pImp, SuppressCombatSystem::HOLY_GHOST)
			.Then(nullptr, "player_holy_ghost_manager::__ActiveCommonAddGroup: suppress addon activate")
			.Else([&](gcreature_imp* imp) {
				addon_manager::GetInstance().ActivateGroup(pef, common_add);
			}, "player_holy_ghost_manager::__ActiveCommonAddGroup: normal addon activate");
	}
}

//失效通用附加属性组
void player_holy_ghost_manager::__DeactiveCommonAddGroup(gplayer_imp *pImp)
{
	int common_add = holy_ghost_cfg_manager::GetInstance().GetCommonAddGroupId();
	if (common_add)
	{
		auto pef = creature_enhance_if(pImp);
		ConditionSuppressCombatStat(pImp, SuppressCombatSystem::HOLY_GHOST)
			.Then(nullptr, "player_holy_ghost_manager::__DeactiveCommonAddGroup: suppress addon deactivate")
			.Else([&](gcreature_imp* imp) {
				addon_manager::GetInstance().DeactivateGroup(pef, common_add);
			}, "player_holy_ghost_manager::__DeactiveCommonAddGroup: normal addon deactivate");
	}
}


void player_holy_ghost_manager::UpgradeWorldTreePoint(gplayer_imp *pImp, PB::gp_holy_ghost_req& cmd)
{
	int point_id = cmd.point_id();
	int old_level = cmd.point_old_level();
	int new_level = cmd.point_new_level();
	SLOG(INFO, "player_holy_ghost_manager::UpgradeWorldTreePoint")
	.P("roleid", pImp->GetRoleID())
	.PS(point_id)
	.PS(old_level)
	.PS(new_level);
	auto *p_cfg = holy_ghost_cfg_manager::GetInstance().GetWorldTreePointCfg(point_id);
	if (!p_cfg)
	{
		return;
	}
	int enhance_config_id = p_cfg->enhance_cfg_id;
	if (enhance_config_id <= 0)
	{
		return;
	}
	auto p_enhance_config = ENHANCE_CONFIG::get(enhance_config_id);
	if (!p_enhance_config)
	{
		return;
	}
	int p_size = holy_ghost_cfg_manager::GetInstance().GetWorldTreePointSize();
	if (_db_data.world_tree().point_lvs().size() < p_size)
	{
		_db_data.mutable_world_tree()->mutable_point_lvs()->Resize(p_size, 0);
	}
	if (point_id - 1 < 0 || point_id > _db_data.world_tree().point_lvs().size())
	{
		return;
	}
	//判断等级是否合法
	if (old_level < 0 || new_level - old_level <= 0 || new_level - old_level > 10
	        || old_level >= p_enhance_config->configs.size()
	        || new_level >= p_enhance_config->configs.size()
	        || old_level != _db_data.world_tree().point_lvs(point_id - 1))
	{
		SendResponse(pImp, cmd, 1);
		return;
	}
	//收集需要扣除的道具
	std::map<int, int> cost_item_map;
	if (!holy_ghost_cfg_manager::GetInstance().GetUpgradeItemMap(p_enhance_config, old_level, new_level, cost_item_map))
	{
		return ;
	}
	std::stringstream ss_cost;
	if (!cmd.auto_fill())
	{
		std::vector<ITEM_CHECK_INFO> item_check_infos;
		for (auto& kv : cost_item_map)
		{
			ITEM_CHECK_INFO item_check_info;
			if (!pImp->CheckItemExistAtBackpack2(item_check_info, kv.first, kv.second, false))
			{
				SendResponse(pImp, cmd, 2);
				return;
			}
			item_check_infos.push_back(item_check_info);
		}

		//扣除
		FuncInfo func_info{kFuncCodeHolyGhost, cmd.op_type(), point_id};
		std::stringstream ss_cost;
		for (auto& item_check_info : item_check_infos)
		{
			pImp->DecItem2(item_check_info, func_info);
			ss_cost << "(" << item_check_info.GetTid() << "," << item_check_info.GetCount() << "),";
		}
	}
	else
	{
		//自动补足
		GNET::LogMessage logger(LOG_DEBUG, "player_holy_ghost_manager::UpgradeWorldTreePoint::AutoFillCheckAndDec");
		int retcode = AutoFillCheckAndDec(pImp, cmd, cost_item_map, logger);
		if (retcode != 0)
		{
			SendResponse(pImp, cmd, 2);
			return;
		}
	}

	int old_height = GetWorldTreeHeight();
	//升级
	_db_data.mutable_world_tree()->set_point_lvs(point_id - 1, new_level);

	//更新属性
	__UpdateWorldTreePointProp(pImp);

	//通知客户端
	SendResponse(pImp, cmd, 0);

	//高度变化
	int new_height = GetWorldTreeHeight();
	if (old_height < new_height)
	{
		OnWorldTreeAddHeight(pImp);
	}

	BI_LOG_GLOBAL(pImp->GetParent()->account.ToStr());
	SLOG(FORMAT, "holy_ghost_upgrade_world_tree")
	.BI_HEADER2_GS(pImp)
	.PS(point_id)
	.PS(old_level)
	.PS(new_level)
	.PS(old_height)
	.PS(new_height)
	.P("total_talent_point", _db_data.total_talent_point())
	.P("cost_items", ss_cost.str());
}

int player_holy_ghost_manager::GetWorldTreeHeight()
{
	auto& wt_cfg = holy_ghost_cfg_manager::GetInstance().GetWorldTreeCfg();
	int min_lv = INT32_MAX;
	for (int i = 0; i < _db_data.world_tree().point_lvs().size(); i++)
	{
		if (_db_data.world_tree().point_lvs(i) < min_lv)
		{
			min_lv = _db_data.world_tree().point_lvs(i);
		}
	}
	if (min_lv == INT32_MAX || wt_cfg.add_height_need_lv <= 0)
	{
		return 0;
	}
	return min_lv / wt_cfg.add_height_need_lv * wt_cfg.add_height;
}

void player_holy_ghost_manager::OnWorldTreeAddHeight(gplayer_imp *pImp)
{
	int height = GetWorldTreeHeight();
	SLOG(INFO, "player_holy_ghost_manager::OnWorldTreeAddHeight")
	.P("roleid", pImp->GetRoleID())
	.PS(height);

	pImp->GetAchievement().OnHolyGhostAddWorldTreeHeight(pImp);
	pImp->GetStarGhost().OnHolyGhostAddWorldTreeHeight(pImp, height);
}

void player_holy_ghost_manager::GetWorldTreeHeightReward(gplayer_imp *pImp, PB::gp_holy_ghost_req& cmd)
{
	int rewared_tree_height = _db_data.world_tree().rewarded_height();
	int cur_height = GetWorldTreeHeight();
	auto& wt_cfg = holy_ghost_cfg_manager::GetInstance().GetWorldTreeCfg();
	auto& height_rewards = wt_cfg.height_rewards;
	int next_reward_height = wt_cfg.GetNextRewardHeight(rewared_tree_height);

	SLOG(INFO, "player_holy_ghost_manager::GetWorldTreeHeightReward")
	.P("roleid", pImp->GetRoleID())
	.PS(rewared_tree_height)
	.PS(cur_height)
	.PS(next_reward_height);

	auto iter = height_rewards.find(next_reward_height);
	if (next_reward_height == 0 || iter == height_rewards.end() || cur_height < next_reward_height)
	{
		return;
	}
	if (_db_data.world_tree().rewarded_height() >= next_reward_height)
	{
		return;
	}
	if (pImp->GetInventory().GetInventory(GNET::IL_BACKPACK).GetEmptySlotCount() < 1)
	{
		pImp->error_message(GS_ERRMSGTYPE_NORMAL, SHOWERRMSG_CHAT, S2C::ERR_SELF_SELECT_REWARD_SLOT_NOT_ENOUGH);
		SendResponse(pImp, cmd, 1);
		return;
	}
	//发奖
	gen_item_t gen;
	gen.tid = iter->second;
	gen.count = 1;
	FuncInfo func_info{kFuncCodeHolyGhost, cmd.op_type(), next_reward_height};
	if (!pImp->GenAndIncItem(IGT_HOLY_GHOST, func_info, gen))
	{
		SendResponse(pImp, cmd, 2);
		return;
	}
	//记录数据
	_db_data.mutable_world_tree()->set_rewarded_height(next_reward_height);
	SendResponse(pImp, cmd, 0);
}

holy_ghost *player_holy_ghost_manager::__GetHolyGhost(int tid) const
{
	auto iter = _hg_map.find(tid);
	if (iter == _hg_map.end())
	{
		return nullptr;
	}
	return iter->second;
}

bool player_holy_ghost_manager::CheckAddHolyGhost(gplayer_imp *pImp, int tid)
{
	if (!GET_FUNC_SWITCH(kFuncCodeHolyGhost))
	{
		return false;
	}
	auto p_hg = __GetHolyGhost(tid);
	if (p_hg != nullptr)
	{
		return false;
	}
	return true;
}

void player_holy_ghost_manager::AddHolyGhost(gplayer_imp *pImp, int tid)
{
	if (!GET_FUNC_SWITCH(kFuncCodeHolyGhost))
	{
		return;
	}
	SLOG(INFO, "player_holy_ghost_manager::AddHolyGhost")
	.P("roleid", pImp->GetRoleID())
	.PS(tid);

	if (!pImp || !CheckAddHolyGhost(pImp, tid))
	{
		return;
	}

	auto p_hg_cfg = HOLY_GHOST_CONFIG::get(tid);
	if (!p_hg_cfg)
	{
		return;
	}

	PB::db_holy_ghost_info tmp_info;
	tmp_info.set_id(tid);
	_hg_map[tid] = new holy_ghost(pImp, this, &tmp_info);

	pImp->GetAchievement().OnHolyGhostUnlock(pImp);

	SendResponse(pImp, PB::HGO_UNLOCK, 0, tid);

	SLOG(FORMAT, "holy_ghost_add_holy_ghost")
	.P("roleid", pImp->GetRoleID())
	.PS(tid);
}

void player_holy_ghost_manager::SetHolyGhostCombat(gplayer_imp *pImp, PB::gp_holy_ghost_req& cmd)
{
	int tid = cmd.holy_ghost_id();
	int combat_pos = cmd.combat_pos();
	SLOG(INFO, "player_holy_ghost_manager::SetHolyGhostCombat")
	.P("roleid", pImp->GetRoleID())
	.PS(tid)
	.PS(combat_pos);

	if (!pImp || pImp->IsCombatState())
	{
		return;
	}
	//检查出战位是否解锁
	int combat_count = holy_ghost_cfg_manager::GetInstance().GetCombatCountByHeight(GetWorldTreeHeight());
	if (combat_pos < 0 || combat_pos >= combat_count)
	{
		SendResponse(pImp, PB::HGO_SET_COMBAT, 1);
		return;
	}
	if (tid && IsHolyGhostCombat(tid))
	{
		SendResponse(pImp, PB::HGO_SET_COMBAT, 1);
		return;
	}
	bool old_flag = HaveCombatHolyGhost();
	//设置出战位置
	if ((int)_db_data.combat_ids().size() < combat_count)
	{
		_db_data.mutable_combat_ids()->Resize(combat_count, 0);
	}
	int old_tid = _db_data.combat_ids(combat_pos);
	_db_data.set_combat_ids(combat_pos, tid);

	object_interface oif(pImp);
	if (old_tid)
	{
		//失效原来的英灵的天赋
		auto *p_hg = __GetHolyGhost(old_tid);
		if (p_hg)
		{
			p_hg->_talent.DeActiveAll();
		}
		//删除主动技能
		auto p_hg_cfg = HOLY_GHOST_CONFIG::get(old_tid);
		if (p_hg_cfg)
		{
			ConditionSuppressCombatStat(pImp, SuppressCombatSystem::HOLY_GHOST)
				.Then(nullptr, "player_holy_ghost_manager::SetHolyGhostCombat: suppress skill remove")
				.Else([&](gcreature_imp* imp) {
					imp->GetSkill().RemoveSkill(p_hg_cfg->main_skill, 1, oif);
				}, "player_holy_ghost_manager::SetHolyGhostCombat: normal skill remove");
		}
	}

	if (tid)
	{
		//生效新的英灵的天赋
		auto *p_hg = __GetHolyGhost(tid);
		if (p_hg)
		{
			p_hg->_talent.ActiveAll();
		}
		//增加主动技能
		int break_idx = p_hg->GetDBData().break_lv();
		auto p_hg_cfg = HOLY_GHOST_CONFIG::get(tid);
		if (p_hg_cfg && break_idx >= 0 && break_idx < p_hg_cfg->break_phase.size() && p_hg_cfg->break_phase[break_idx].skill_lv > 0)
		{
			ConditionSuppressCombatStat(pImp, SuppressCombatSystem::HOLY_GHOST)
				.Then(nullptr, "player_holy_ghost_manager::SetHolyGhostCombat: suppress skill insert")
				.Else([&](gcreature_imp* imp) {
					imp->GetSkill().InsertSkill(p_hg_cfg->main_skill, p_hg_cfg->break_phase[break_idx].skill_lv, oif);
				}, "player_holy_ghost_manager::SetHolyGhostCombat: normal skill insert");
		}
	}
	bool new_flag = HaveCombatHolyGhost();
	//有到无
	if (old_flag && !new_flag)
	{
		__DeactiveCommonAddGroup(pImp);
	}
	//无到有
	if (!old_flag && new_flag)
	{
		__ActiveCommonAddGroup(pImp);
	}

	SendResponse(pImp, PB::HGO_SET_COMBAT, 0);

	SLOG(FORMAT, "holy_ghost_set_combat")
	.P("roleid", pImp->GetRoleID())
	.PS(old_tid)
	.PS(tid);
}

int player_holy_ghost_manager::GetTalentStar(int hg_id, int talent, int skill) const
{
	if (!GET_FUNC_SWITCH(kFuncCodeHolyGhost))
	{
		return 0;
	}
	auto *p_hg = __GetHolyGhost(hg_id);
	if (!p_hg)
	{
		return 0;
	}
	return p_hg->_talent.GetTalentLv(talent, skill);
}

int player_holy_ghost_manager::CheckAddHolyGhostGift(int gift_id, int count)
{
	if (!GET_FUNC_SWITCH(kFuncCodeHolyGhost))
	{
		return 1;
	}
	if (count < 1)
	{
		return 2;
	}

	auto p_gift = HERO_GIFT_CFG::get(gift_id);
	if (!p_gift)
	{
		return 3;
	}
	return 0;
}

int player_holy_ghost_manager::AddHolyGhostGift(gplayer_imp *pImp, int gift_id, int count)
{
	if (!GET_FUNC_SWITCH(kFuncCodeHolyGhost))
	{
		return S2C::ERR_SERVICE_UNAVILABLE;
	}
	if (!pImp)
	{
		return S2C::ERR_SERVICE_UNAVILABLE;
	}
	if (CheckAddHolyGhostGift(gift_id, count) != 0)
	{
		return S2C::ERR_SERVICE_UNAVILABLE;
	}

	_gift_map[gift_id] += count;
	_last_add_gift_id = gift_id;
	_last_add_gift_count = count;

	SendResponse(pImp, PB::HGO_ADD_GIFT, 0);

	int total_count = _gift_map[gift_id];
	SLOG(FORMAT, "holy_ghost_add_gift")
	.PS(gift_id)
	.PS(count)
	.PS(total_count);
	return 0;
}

int player_holy_ghost_manager::GetHolyGhostAmity(int hg_id) const
{
	if (!GET_FUNC_SWITCH(kFuncCodeHolyGhost))
	{
		return 0;
	}
	auto *p_hg = __GetHolyGhost(hg_id);
	if (!p_hg)
	{
		return 0;
	}
	return p_hg->GetDBData().amity();
}

int player_holy_ghost_manager::GetHolyGhostLevel(int hg_id) const
{
	if (!GET_FUNC_SWITCH(kFuncCodeHolyGhost))
	{
		return 0;
	}
	auto *p_hg = __GetHolyGhost(hg_id);
	if (!p_hg)
	{
		return 0;
	}
	return p_hg->GetDBData().lv();
}

int player_holy_ghost_manager::GetHolyGhostCount() const
{
	if (!GET_FUNC_SWITCH(kFuncCodeHolyGhost))
	{
		return 0;
	}
	return _hg_map.size();
}

void player_holy_ghost_manager::DebugOp(gplayer_imp *pImp, PB::gp_holy_ghost_req& cmd)
{
	SLOG(INFO, "player_holy_ghost_manager::DebugOp")
	.P("roleid", pImp->GetRoleID())
	.PB("cmd", cmd);

	switch (cmd.op_type())
	{
	//清所有数据
	case (PB::HGO_ALL):
	{

	}
	break;
	//世界树节点升级
	case (PB::HGO_UPGRADE_WORLD_TREE_POINT):
	{
		int point_id = cmd.point_id();
		int old_level = cmd.point_old_level();
		int new_level = cmd.point_new_level();
		auto *p_cfg = holy_ghost_cfg_manager::GetInstance().GetWorldTreePointCfg(point_id);
		if (!p_cfg)
		{
			return;
		}
		int enhance_config_id = p_cfg->enhance_cfg_id;
		if (enhance_config_id <= 0)
		{
			return;
		}
		auto p_enhance_config = ENHANCE_CONFIG::get(enhance_config_id);
		if (!p_enhance_config)
		{
			return;
		}
		int p_size = holy_ghost_cfg_manager::GetInstance().GetWorldTreePointSize();
		if (_db_data.world_tree().point_lvs().size() < p_size)
		{
			_db_data.mutable_world_tree()->mutable_point_lvs()->Resize(p_size, 0);
		}
		if (point_id - 1 < 0 || point_id > _db_data.world_tree().point_lvs().size())
		{
			return;
		}
		//判断等级是否合法
		if (old_level < 0 || new_level - old_level <= 0 || new_level - old_level > 10
		        || old_level >= p_enhance_config->configs.size()
		        || new_level >= p_enhance_config->configs.size()
		        || old_level != _db_data.world_tree().point_lvs(point_id - 1))
		{
			return;
		}
		//升级
		_db_data.mutable_world_tree()->set_point_lvs(point_id - 1, new_level);

		//更新属性
		__UpdateWorldTreePointProp(pImp);

		//通知客户端
		SendResponse(pImp, cmd, 0);
	}
	break;
	//世界树高度奖励领取
	case (PB::HGO_GET_WORLD_TREE_REWARD):
	{
		//GetWorldTreeHeightReward(pImp, cmd);
	}
	break;
	//英灵强化升级
	case (PB::HGO_UPGRADE_LV):
	{
		//auto *p_hg = __GetHolyGhost(cmd.holy_ghost_id());
		//if (p_hg)
		//{
		//	p_hg->UpgradeLv(pImp, cmd);
		//}
	}
	break;
	//英灵突破升级
	case (PB::HGO_UPGRADE_BREAK_LV):
	{
		//auto *p_hg = __GetHolyGhost(cmd.holy_ghost_id());
		//if (p_hg)
		//{
		//	p_hg->BreakLv(pImp, cmd);
		//}
	}
	break;
	//英灵设置出战
	case (PB::HGO_SET_COMBAT):
	{
		//SetHolyGhostCombat(pImp, cmd);
	}
	break;
	//英灵送礼
	case (PB::HGO_SEND_GIFT):
	{
		//auto *p_hg = __GetHolyGhost(cmd.holy_ghost_id());
		//if (p_hg)
		//{
		//	p_hg->SendGift(pImp, cmd);
		//}
	}
	break;
	default:
	{
		//auto *p_hg = __GetHolyGhost(cmd.holy_ghost_id());
		//if (p_hg)
		//{
		//	p_hg->_talent.HandleRequest(cmd);
		//}
	}
	break;
	}

}

void player_holy_ghost_manager::MakeRoleTradeHolyGhostInfo(gplayer_imp *pImp, PB::role_trade_holy_ghost_info& hg_info)
{
	//灵树高度
	hg_info.set_tree_height(GetWorldTreeHeight());
	//灵树等级
	int p_size = holy_ghost_cfg_manager::GetInstance().GetWorldTreePointSize();
	hg_info.mutable_tree_lvs()->CopyFrom(_db_data.world_tree().point_lvs());
	if (p_size > hg_info.tree_lvs().size())
	{
		hg_info.mutable_tree_lvs()->Resize(p_size, 0);
	}
	//英灵数据
	for (auto& kv : _hg_map)
	{
		if (!kv.second)
		{
			break;
		}
		auto *p_add_one = hg_info.add_holy_ghost();
		auto& db_data_one = kv.second->GetDBData();
		if (p_add_one)
		{
			p_add_one->set_id(db_data_one.id());
			p_add_one->set_lv(db_data_one.lv());
			p_add_one->set_break_lv(db_data_one.break_lv());
			p_add_one->set_amity_lv(holy_ghost_cfg_manager::GetInstance().AmityToLv(db_data_one.amity()));
		}
	}
}
